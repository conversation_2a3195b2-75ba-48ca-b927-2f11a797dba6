# Phase 10: Future Enhancements — Verified-only Recovery & Localization

---

## 🎯 Goals

- Prepare for extending system security, flexibility, and internationalization based on future needs.
- Restrict account recovery to verified emails only.
- Support multi-language (localization) for all email templates and related dashboard UI.
- Provide the foundation for further feature extensibility (e.g., bulk actions, MFA, new channels).

---

## ✅ Codex-Friendly Task List

### 🔹 Task 1: Restrict Recovery Flow to Verified Emails Only

**Prompt:**
"Update recovery flow logic and API:
- Only allow account recovery for emails that are marked as verified in the user’s profile.
- Update error handling, API responses, and docs to reflect this new restriction.
- Add tests for both eligible (verified) and ineligible (unverified) emails attempting recovery."

---

### 🔹 Task 2: Multi-language/Localization Support for Email Templates

**Prompt:**
"Refactor email template loading and management to support multiple languages/locales:
- Define fallback logic (default to English).
- Allow template selection based on user or system locale.
- Update template placeholders and ensure correct rendering for different languages.
- Add at least one alternate language template (stub/sample) for future expansion."

---

### 🔹 Task 3: Localization in Dashboard UI

**Prompt:**
"Update dashboard UI components (in `/static/js/`) to:
- Display all invite, verification, and recovery status/labels/messages in the selected locale.
- Add locale/language switcher if not already present.
- Ensure fallback to English for untranslated strings.
- Add unit/integration tests for localization handling."

---

### 🔹 Task 4: Prepare for Future Extensibility

**Prompt:**
"Identify and document further extensibility points, such as:
- Bulk actions for invite/recovery management.
- Additional recovery factors (e.g., MFA).
- SMS or other channel support.
- Additional admin dashboard features (preview/edit templates, advanced stats, etc.).
- Write stubs or TODOs in code/docs for prioritized next features."

---

### 🔹 Task 5: Invite Management Table for Users <3 Invites

**Prompt:**
"Add a dashboard component that lists all users with fewer than 3 remaining invites:
- Display user info, current quota, and outstanding invites in a dedicated management table.
- Table should support sorting, filtering, and direct action buttons for top-up or invite management.
- Place this table beside (not combined with) the quota change table in the UI for admin clarity."

---

### 🧪 Acceptance Criteria

- Recovery is allowed only for verified emails (enforced and tested).
- Email templates and dashboard UI support multiple languages/locales.
- Fallbacks and language switching are robust and tested.
- Future extensibility points are clearly documented or stubbed in codebase.

---

## 📁 Relevant Files

- `peepsapi/auth/services/recover_service.py` — Updated recovery logic.
- `peepsapi/auth/routes/recover.py` — API changes for recovery eligibility.
- `peepsapi/services/email_service.py` — Template localization logic.
- `templates/email/invite.*.html`, `verification.*.html`, `recovery.*.html` — Multi-language template variants.
- `/static/js/components/` — Dashboard UI updates for localization.
- `/static/js/i18n/` — Localization/internationalization utilities and files.
- `tests/auth/services/test_recover_service.py` — Tests for verified-only recovery.
- `tests/services/test_email_service.py` — Tests for localized template handling.
- `tests/static/js/components/` — UI localization tests.
- `docs/localization.md` — Localization/intl documentation.
- `docs/roadmap.md` — Documented future extensibility/TODOs.

---

## 📏 Guidelines

- Default to English, fallback for missing translations.
- All user-facing strings and templates must be localizable.
- Only verified emails may initiate recovery (no exceptions).
- Dashboard UI and backend must be fully tested for new localization and restriction logic.
- Document all TODOs or next features for transparent handoff and planning.
