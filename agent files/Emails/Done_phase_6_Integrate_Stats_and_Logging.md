# Phase 6: Integrate Dashboard Stats and Logging for Invite, Verification, and Recovery

---

## 🎯 Goals

- Provide admins with operational visibility into all invite, verification, and recovery actions and statuses.
- Surface delivery/failure stats and aggregate metrics in the admin dashboard via backend API.
- Ensure all user- and admin-triggered flows are fully auditable and queryable.
- Validate the reliability and usability of logging/statistics endpoints.
- Implement robust invite quota management and audit logging, including infra and API support.

---

## ✅ Codex-Friendly Task List

### 🔹 Task 1: Extend API for Delivery/Failure Stats

**Prompt:**
"Add or extend endpoints (e.g., in `peepsapi/auth/routes/` or `dashboard/routes/`) to provide:
- Aggregate delivery/failure stats for invite, verification, and recovery emails.
- Per-user/email breakdowns and time-based filters (e.g., last 24h, 7d).
- API should surface total sent, delivered, failed, and bounced counts.
- Ensure endpoints are secured and only accessible to authorized admin/dashboard users."

---

### 🔹 Task 2: Expose Invite Quota Assignment and Usage

**Prompt:**
"Implement API endpoints for:
- Assigning invite quotas to users (admin-only, SSO required).
- Fetching invite usage per user and per email.
- Returning results suitable for direct dashboard consumption.
- Log all quota assignments and fetch actions for auditability.
- Add endpoints for increasing quota and for fetching current quota.
- All quota management endpoints must be under an `admin/quota` route and require SSO authentication."

---

### 🔹 Task 3: Infra - Add `invite_quotas` Container

**Prompt:**
"Add a new Cosmos DB container named `invite_quotas` in the infra (bicep/json) files.
- This container will store audit logs for all quota changes (assignments, increases, etc.), including the SSO ID/admin who performed the action, the change amount, and the resulting quota.
- Only the remaining quota is tracked on the person profile; all changes are logged in `invite_quotas`."

---

### 🔹 Task 4: Ensure Logs and Stats Are Queryable and Auditable

**Prompt:**
"Review all invite, verification, and recovery actions for:
- Consistent logging of events (success/failure/attempt/audit) in the backend log/audit system.
- Test log query APIs for accuracy and reliability.
- Ensure stats can be filtered, aggregated, and exported as needed for dashboard reporting and troubleshooting.
- Ensure all quota changes are logged with SSO ID/admin in the `invite_quotas` container."

---

### 🔹 Task 5: Dashboard Data Aggregation and Query Testing

**Prompt:**
"Write tests to validate:
- API stat responses (invite, verification, recovery) match the expected log/audit events.
- Aggregation logic is correct for per-user, per-period, and per-type breakdowns.
- All endpoints enforce correct permissions and error handling.
- Data structures are dashboard-ready (fields, units, time formatting).
- Quota management endpoints are tested for SSO-only access and correct audit logging."

---

### 🔹 Task 6: Invite Quota Decrement Logic

**Prompt:**
"When sending a brand new invite (not extending an existing one), decrement the inviter's invite quota from their profile counter.
- Do not decrement quota when simply extending the expiry of an existing invite."

---

## 🧪 Acceptance Criteria

- Admin dashboard APIs surface delivery/failure stats for invite, verification, and recovery emails.
- Invite quotas and usage are manageable and visible via API, with SSO-only access for quota management.
- Logging and stats APIs are reliable, secure, and match actual events.
- All quota changes are audit-logged in the `invite_quotas` container with SSO ID/admin.
- Remaining quota is tracked only on the person profile.
- Quota is decremented only for new invites, not for extensions.
- Tests cover data correctness, permissions, and edge cases.

---

## 📁 Relevant Files

- `Infra/main-parameters-*.json`, `Infra/all-collections.bicep` — Add `invite_quotas` container.
- `peepsapi/auth/routes/admin/quota.py` (or equivalent) — API endpoints for quota management.
- `peepsapi/auth/services/invite_service.py` — Extended with quota/stat logic and audit logging.
- `peepsapi/auth/services/email_verification_service.py` — Extended with stats logic.
- `peepsapi/auth/services/recover_service.py` — Extended with stats logic.
- `tests/auth/routes/test_dashboard_stats.py` — Tests for dashboard stats endpoints.
- `tests/auth/services/test_invite_service.py` — Tests for quota/stat logic.
- `tests/auth/services/test_email_verification_service.py` — Tests for verification stats.
- `tests/auth/services/test_recover_service.py` — Tests for recovery stats.

---

## 📏 Guidelines

- All dashboard/stat and quota management APIs must be secured (admin-only, SSO required for quota management).
- Log and surface both delivery attempts and failures for all email flows.
- Data must be suitable for real-time dashboard display and export.
- Use Peeps conventions for logging, permissions, and API response formatting.
- Run all tests with `make test`.
