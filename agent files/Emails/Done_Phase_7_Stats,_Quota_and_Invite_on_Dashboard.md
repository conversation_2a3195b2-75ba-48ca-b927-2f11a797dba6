# Phase 7: Expose Stats, Quota Assignment, and Invite Management in Dashboard UI

---

## 🎯 Goals

- Show invite, verification, and recovery delivery/failure stats in the admin dashboard UI (JS-based, /static folder).
- Allow admins to assign invite quotas and view invite usage per user in the dashboard.
- Provide UI for managing outstanding invites and recovery tokens.
- All actions should be secure, match backend API state, and refresh every minute.

---

## ✅ Codex-Friendly Task List

### 🔹 Task 1: Display Email Delivery/Failure Stats in Dashboard UI

**Prompt:**
"Extend the dashboard UI (JS, under `/static`) to show:
- Aggregate delivery/failure/bounce stats for invite, verification, and recovery emails (last 24h, 7d, custom range).
- Per-user and per-email breakdowns in tables and charts.
- Highlight failed or bounced emails for troubleshooting.
- Data should auto-refresh every 60 seconds (no real-time sockets required)."

---

### 🔹 Task 2: Invite Quota and Usage Management UI

**Prompt:**
"Add UI to the dashboard for:
- Assigning invite quotas to users (manually, by admin) via a form or editable table.
- Showing used/remaining invite quota for each user and per email.
- Clear indicators for quota exhaustion."

---

### 🔹 Task 3: Invite and Recovery Token Management UI

**Prompt:**
"Create dashboard UI for:
- Listing all outstanding invites and recovery tokens by user/email.
- Allowing admins to cancel individual invites or recovery tokens.
- Filter and sort by status (pending, sent, expired, used, failed).
- Provide action buttons for resend/cancel on each row.
- Bulk actions (e.g., cancel all expired) are not required in this phase."

---

### 🔹 Task 4: UI Error Handling and Permissions

**Prompt:**
"Ensure all UI components:
- Gracefully handle backend/API errors (user feedback, retry).
- Only allow access/actions to authenticated SSO (admin/company) users.
- Reflect the latest backend state (no client-side caching beyond 1-minute refresh)."

---

### 🔹 Task 5: Dashboard UI Tests

**Prompt:**
"Add JS tests (unit/integration) for all new dashboard UI elements:
- Stats rendering and refresh
- Quota assignment and usage updates
- Invite and recovery token management flows
- Error and permission handling
- Accessibility (a11y) checks and cross-browser compatibility"

---

### 🧪 Acceptance Criteria

- Dashboard UI displays accurate, refreshed stats for all invite, verification, and recovery flows.
- Admins can assign and view invite quotas, usage, and outstanding invites/tokens.
- All UI actions (resend, cancel, quota assign) are secure and reflect backend state.
- No bulk actions or exports needed in this phase.
- UI is tested for functionality, permissions, and accessibility.

---

## 📁 Relevant Files

- `/static/js/components/InviteStats.js` — Stats charts/tables component.
- `/static/js/components/InviteQuotaManagement.js` — Quota assignment/usage UI.
- `/static/js/components/InviteList.js` — List and management UI for invites.
- `/static/js/components/RecoveryList.js` — List and management UI for recovery tokens.
- `/static/js/api/admin.js` — API integration for dashboard admin features.

---

## 📏 Guidelines

- Match the style, folder structure, and design patterns in existing `/static` dashboard code.
- Use only authenticated, SSO-verified company users for all dashboard features.
- No CSV/XLSX exports or bulk actions in this phase.
- All dashboard data must refresh every 60 seconds.
- Rely on backend API for audit/logging—no extra client logging required.
- All UI changes should be fully tested (unit/integration/a11y).
- Use only UI libraries already present in the codebase unless cleared by tech lead.

---
