# Phase 3: Implement Invite Flow — Models, Service, API Endpoints, and Tests

---

## 🎯 Goals

- Enable users and admins to send invite emails via ACS using the shared email service and invite template.
- Ensure invites are unique, linked to user profiles, expire after 30 days, and are extendable on resend.
- Expose full invite lifecycle: create, resend, fetch, cancel.
- Model all invite data for auditability and future-proofing.
- Ensure all logic is tested and horizontally reusable.

---

## ✅ Codex-Friendly Task List

### 🔹 Task 1: Define/Update Invite Models

**Prompt:**
"Define or update `Invite` model(s) in `peepsapi/auth/models/invite.py` to represent:
- Invite token, email, creator, expiry, status, timestamps, and linkage to user profile.
- Ensure model supports auditing, resend/expiry extension, and cancellation.
- Add/extend any enums or helper types as needed."

---

### 🔹 Task 2: Implement Invite Service Logic

**Prompt:**
"Implement `InviteService` in `peepsapi/auth/services/invite_service.py`:
- Methods for create_invite, resend_invite, fetch_invites (by user/email), cancel_invite, and extend_expiry.
- Enforce unique emails (not in use by any profile, verified or not).
- Handle quota (users) and unlimited sends (admins).
- Use central email service to send all invite emails.
- Log all actions and failures with audit detail."

---

### 🔹 Task 3: Scaffold and Extend Invite API Endpoints

**Prompt:**
"Create or extend endpoints in `peepsapi/auth/routes/invite.py` for:
- Creating/sending invites
- Resending existing invites (extends expiry)
- Fetching invites for current user or by email
- Cancelling invites
- Secure endpoints via auth (except for token validation if needed)
- Use decorators for error handling and logging.
- Ensure response format matches Peeps standards."

---

### 🔹 Task 4: Integrate with Shared Email Service and Template

**Prompt:**
"Ensure all invite-related sends use `EmailService` and the invite template.
- Populate all placeholders (e.g., invite link, user name, expiry).
- Handle and log all send failures for troubleshooting/dashboard stats."

---

### 🔹 Task 5: Unit and Integration Tests

**Prompt:**
"Create or extend:
- `tests/auth/models/test_invite.py` (model/unit tests)
- `tests/auth/services/test_invite_service.py` (service/logic tests)
- `tests/auth/routes/test_invite.py` (endpoint/integration tests)
- Cover: happy path, errors, expired/invalid/cancelled invites, quota logic, audit, logging, and email sending (mocked)."

---

### 🧪 Acceptance Criteria

- Invite model, service, and API endpoints are fully implemented.
- Invite lifecycle supports create, resend (with expiry extension), fetch, and cancel.
- All business rules (uniqueness, quota, expiry) are enforced and tested.
- Invite emails are sent using the correct template and logged.
- Test suite covers all flows and edge cases.

---

## 📁 Relevant Files

- `peepsapi/auth/models/invite.py` — Invite model definitions.
- `peepsapi/auth/services/invite_service.py` — Business logic for invites.
- `peepsapi/auth/routes/invite.py` — API endpoints for invites.
- `peepsapi/services/email_service.py` — Used by invite service for email sending.
- `tests/auth/models/test_invite.py` — Unit tests for invite model.
- `tests/auth/services/test_invite_service.py` — Unit tests for invite service.
- `tests/auth/routes/test_invite.py` — Endpoint/integration tests for invite API.
- `templates/email/invite.html` — Invite email template (location per convention).

---

## 📏 Guidelines

- Follow horizontal conventions for error handling, logging, and audit.
- API endpoints should be secure (auth required) except for token validation.
- Test all logic, including quota enforcement and expiry extension.
- Run all tests with `make test`.
