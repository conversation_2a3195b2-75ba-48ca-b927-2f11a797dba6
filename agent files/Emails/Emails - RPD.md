# Product Requirements Document (PRD): Email Invite, Verification, and Recovery (Azure Communication Service)

## 1. Introduction/Overview

This document defines the requirements for secure email-based invite, verification, and account recovery flows using Azure Communication Service (ACS). The goal is to ensure users can onboard, verify, and recover their accounts using modern, secure, and auditable workflows. This is a backend-only feature, built to align with <PERSON>eeps' horizontal logging, error handling, and audit conventions.

---

## 2. Goals

- **Profile Emails:**
  - Support multiple verified emails per user profile.
  - Ensure only valid and deliverable email addresses can be added to user profiles.
  - At the time of adding a new email address, ensure it is not in use as a verified email or active invite by another profile.
- **Invites:**
  - Allow users and admins to invite new users by email; enforce unique, deliverable emails.
  - Enable sending of invite emails using Azure Communication Service.
  - Provide endpoints to fetch, resend, extend expiry, cancel, and manage invites.
- **Verification:** Ensure all user emails are verifiable through a secure email process.
- **Recovery:** Enable secure, user-friendly account recovery using verified (and for now, any) emails.
- **Templates:** Use distinct, customizable templates for invite and recovery emails.
- **Audit & Observability:** Log and expose all invite, verification, and recovery actions for compliance and support.
- - **Horizontal:** Implement email backend using Azure Communication Service.

---

## 3. User Stories

**invites**
- **As a user**, I can receive an invite email and register by clicking the link, which verifies my email.
- **As a user**, I can generate and send invite emails to others (if I have available invites).
- **As a user**, I can view my outstanding invites and resend invite emails (which extends their expiry).
**verification**
- **As a user**, I can add new email addresses to my profile and trigger a verification process for each.
- **As a user**, I want to recover my account using `auth/recover/initiate` which will validate existance of email address, send me a invite recovery token and rest will be similar to invite token validation.
**admins**
- **As an admin**, I can view invite/verification delivery status and audit logs on the dashboard.
- **As an admin**, I can send invite emails and assign additional invites to any user via the admin dashboard.
**recover**
- **As a user**, I can recover my account using `auth/recover/initiate`, which validates my email and sends me a recovery token.

---

## 4. Functional Requirements

### 4.1. Invite Flow

- Only users with invite quota or admins can generate invites.
- Invites are sent via ACS using the "invite" template.
- Invites are valid for 30 days; resending an active invite extends the expiry.
- Invites only accepted for emails not already in use (verified or not).
- On invite creation, an empty user profile is generated and linked to the email.
- Users must be informed of email send failures.


### 4.2. Verification Flow

- Users may add multiple emails to their profile.
- Verification codes/links are sent via ACS using the "verification" template.
- Clicking the link or submitting the code marks the email as verified.
- Verification codes expire in 1 day.
- Unverified emails cannot be used for login or critical notifications.
- Users must be informed of email send failures.


### 4.3. Recovery Flow

- Endpoint: `auth/recover/initiate` (public, rate-limited).
- Accepts an email address; always returns silent success.
- If the email exists, a recovery token (valid for 1 hour) is sent via ACS using the "recovery" template.
- Re-requesting an active recovery updates and extends the expiry.
- After token validation, flow matches registration (passkey challenge/verification).
- Admin dashboard shows recovery attempt stats.
- All requests, tokens, and actions are logged for audit.
- (Future phase: restrict recovery to only verified emails.)
- Users must be informed of email send failures.

### 4.4. Email Templates

- **Invite Template:** Used for invite emails, supports placeholders (e.g., user name, invite link).
- **Verification Template:** Used for email verification; distinct copy from invite.
- **Recovery Template:** Used for account recovery; distinct copy.
- Templates must be customizable, localizable (future phase), and versioned.

### 4.5. API & Service Structure

- Provide separate endpoints for:
   - Creating/sending invites
   - Fetching/resending invites (extends expiry)
   - Adding/verifying emails on user profiles
   - Cancelling invites
- Expose endpoints for fetching existing invites for a user.
- New models/services for invites and verification in `peepsapi/auth/models/` and `peepsapi/auth/services/`.
- Shared email sending/integration logic lives in `peepsapi/services/email_service.py`.
- Email verification endpoints in `peepsapi/auth/routes/email_verification.py`.
- Invite endpoints in `peepsapi/auth/routes/invite.py`.
- Recovery endpoints in `peepsapi/auth/routes/recover.py`.
- verification endpoints in `peepsapi/auth/routes/verify.py`.
- Email verification container is defined in infra, with secrets in Key Vault.

### 4.6. Validation & Security

- Frontend validates email format.
- Backend validates domain and deliverability before sending.
- Emails that fail validation/deliverability must be rejected.
- All endpoints are authenticated unless explicitly public (recover is public, rate-limited). verify and invite are secured with auth.
- All tokens/links are single-use, minimum 32 chars entropy, HTTPS-only.
- Audit logging applies to all actions.

### 4.7. Error Handling, Logging, Rate Limiting

- Use existing decorator, error, and logging patterns throughout.
- All endpoints and services return responses in the standard API format.
- Rate limiting on all public endpoints, using codebase’s existing implementation.
- Logs must be accessible and stats visualized in the admin dashboard.

### 4.8. Admin dashboard
- Sending invites
- Assigning more invites to any user
- Viewing delivery status/logs for invites and verifications
- Admin dashboard must display failed delivery attempts for troubleshooting.

---

## 5. Non-Goals (Out of Scope)

- SMS-based invites, verification, or recovery.
- UI/frontend changes (other than dashboard stats).
- Email delivery vendors other than ACS.
- Locales/languages other than English for templates (future phase).
- Recovery limited only to verified emails (future phase).
- Admin notification for recovery attempts (dashboard stats only).
- Integrating with services like Kickbox or ZeroBounce for email validation (vendors out of scope).

## 6. Design Considerations

- All flows are backend/API-only; endpoints to be consumed by apps and dashboard.
- Email templates must be versioned and support future localization.
- Endpoint and service structure must follow horizontal, reusable conventions.

## 7. Technical Considerations

- **User System:** Custom backend (with passkey auth) and Azure AD SSO for admins.
- **ACS Setup:** resource and secrets are managed via infra and Key Vault.
- **APIs:** All actions (invite, verification, fetch/resend/cancel) must be implemented as authenticated API endpoints.
- **Services:** Services must use centralized `email_service.py` for all sending/validation.
- **Email Validation:**
  - Recommend using libraries like [`email-validator`](https://pypi.org/project/email-validator/) for syntax and domain checks.
  - For deliverability, use ACS bounce feedback.
- **Security:**
  - Invite links and verification codes must be single-use, high-entropy (minimum 32 chars), and expire after 30 days (invite) or 1 day (verification).
  - All links/codes must require HTTPS.
- **Extensibility:** All logging, auth, and error handling must use the existing horizontal implementations.
- **Testing:** Test placement mirrors implementation for all new logic.

## 8. Success Metrics

- ≥ 95% deliverability for invite, verification, and recovery emails.
- 100% of actions logged and available for admin audit/statistics.
- Zero verified emails associated with more than one user.
- Silent, user-friendly UX for recovery with no information leakage.

## 9. Open Questions

- None at this time; all previous open questions have been addressed in this version.
