## Relevant Files

- `infra/main.bicep` - IaC for ACS resource and related secrets.
- `infra/main-parameters-dev.json` / `stage.json` / `prod.json` Environment configuration for each environment.

- `peepsapi/auth/models/email_verification.py` - Pydantic models for email verification tokens and status.
- `peepsapi/auth/models/invite.py` - Models for invite objects if updates are required.
- `peepsapi/auth/models/recovery.py` - Data model for recovery tokens as needed, prioritize reuse if code is (re)using invite token models.
- `peepsapi/auth/routes/email_verification.py` - API endpoints for email verification flows.
- `peepsapi/auth/routes/invite.py` - API endpoints for invites (updates as needed).
- `peepsapi/auth/routes/recover.py` - API endpoints for recovery initiation and token validation.
- `peepsapi/auth/services/email_verification_service.py` - Business logic for email verification workflows.
- `peepsapi/auth/services/invite_service.py` - (May update if needed) Business logic for invite creation and management.
- `peepsapi/auth/services/recover_service.py` - Business logic for recovery flow.
- `peepsapi/services/email_service.py` - Centralized integration logic with Azure Communication Service (send, handle bounces).
- `tests/auth/models/test_email_verification.py` - Unit tests for email verification models.
- `tests/auth/models/test_invite.py` - Tests for invite data model.
- `tests/auth/models/test_recovery.py` - Tests for recovery model.
- `tests/auth/routes/test_invite.py` - Endpoint tests for invite API.
- `tests/auth/routes/test_email_verification.py` - Endpoint tests for verification API.
- `tests/auth/routes/test_recover.py` - Endpoint tests for recovery API.
- `tests/auth/services/test_email_verification_service.py` - Tests for business logic (including edge cases).
- `tests/auth/services/test_invite_service.py` - Tests for invite logic.
- `tests/auth/services/test_recover_service.py` - Tests for recovery logic.
- `tests/services/test_email_service.py` - Tests for email sending/integration and error handling.

### Notes

- Unit/integration tests should be created or updated for all new logic and endpoints, with coverage for success, failure, and edge cases.
- Use the standard error handling/logging patterns in all services and routes.
- Run all tests with `make test`

## Tasks

- [ ] 1.0 Provision ACS Infrastructure and Secret Management
  - [ ] 1.1 Author and deploy `infra/main.bicep` to provision ACS Email resource, parameterized by environment.
  - [ ] 1.2 Add ACS config to `infra/main-parameters-dev.json`, `stage.json`, `prod.json`.
  - [ ] 1.3 Define and manage ACS credentials in Azure Key Vault and update infra scripts (`makefile deploy-infra`)

- [ ] 2.0 Implement Shared Email Service and Template Management
  - [ ] 2.1 Scaffold `peepsapi/services/email_service.py` with interface for send_email, template rendering, and error handling.
  - [ ] 2.2 Implement email sending logic using ACS SDK.
  - [ ] 2.3 Implement email template loading, versioning, and distinct templates for invite, verification, recovery.
  - [ ] 2.4 Integrate email validation (syntax, domain, bounce/deliverability check).
  - [ ] 2.5 Write unit tests for core service and all template code paths.

- [ ] 3.0 Implement Invite Flow: Models, Service, API Endpoints, and Tests
  - [ ] 3.1 Define/update invite models in `peepsapi/auth/models/invite.py`.
  - [ ] 3.2 Implement invite logic in `peepsapi/auth/services/invite_service.py` (quota, expiry, linking profile).
  - [ ] 3.3 Add/extend invite API endpoints in `peepsapi/auth/routes/invite.py`: create, resend, fetch, cancel, extend expiry.
  - [ ] 3.4 Integrate invite flow with email service and template.
  - [ ] 3.5 Write unit and integration tests for invite models, service, and endpoints.

- [ ] 4.0 Implement Email Verification Flow: Models, Service, API Endpoints, and Tests
  - [ ] 4.1 Define verification models in `peepsapi/auth/models/email_verification.py`.
  - [ ] 4.2 Implement verification logic in `peepsapi/auth/services/email_verification_service.py` (code/token lifecycle).
  - [ ] 4.3 Add/extend verification API endpoints in `peepsapi/auth/routes/email_verification.py`: add email, send code, verify, resend, fetch status.
  - [ ] 4.4 Integrate with email service and template, enforce expiry and single-use.
  - [ ] 4.5 Write unit and integration tests for verification models, service, and endpoints.

- [ ] 5.0 Implement Recovery Flow: Models, Service, API Endpoints, and Tests
  - [ ] 5.1 Define recovery models in `peepsapi/auth/models/recovery.py`.
  - [ ] 5.2 Implement recovery logic in `peepsapi/auth/services/recover_service.py` (token creation, expiry, linkage).
  - [ ] 5.3 Add recovery API endpoints in `peepsapi/auth/routes/recover.py`: initiate, validate token.
  - [ ] 5.4 Integrate with recovery email template and existing flows.
  - [ ] 5.5 Audit/log all recovery attempts and show stats in admin dashboard.
  - [ ] 5.6 Write unit and integration tests for recovery models, service, and endpoints.

- [ ] 6.0 Integrate Dashboard Stats and Logging for Invite, Verification, and Recovery
  - [ ] 6.1 Extend admin dashboard API for invite, verification, and recovery delivery/failure stats.
  - [ ] 6.2 Expose API endpoints for assigning invite quotas and reviewing usage.
  - [ ] 6.3 Ensure logs are queryable and stats can be rendered for dashboard consumption.
  - [ ] 6.4 Test data fetching, aggregation, and dashboard queries.

- [ ] 7.0 Enforce Error Handling, Rate Limiting, and Audit Logging
  - [ ] 7.1 Review all new endpoints/services for proper decorator usage and error logging.
  - [ ] 7.2 Ensure all responses are standardized and rate limiting is active for public endpoints.
  - [ ] 7.3 Write/extend tests to verify logging, errors, and throttling behavior.

- [ ] 8.0 Security, Compliance, and Documentation
  - [ ] 8.1 Review token entropy, expiry, and HTTPS enforcement for all links.
  - [ ] 8.2 Confirm no info leakage in invite, verify, or recovery flows.
  - [ ] 8.3 Document endpoint usage, templates, and error codes.
  - [ ] 8.4 Provide developer and operator enablement docs and test scripts.

- [ ] 9.0 Future Enhancements: Verified-only Recovery & Localization
  - [ ] 9.1 Restrict recovery to verified emails only.
  - [ ] 9.2 Add localization (multi-language) support to all email templates.
  - [ ] 9.3 Implement dashboard preview/editing for templates.
  - [ ] 9.4 Explore additional factors (MFA, vendor integrations) as needed.
