# Phase 1: Provision ACS Infrastructure and Secret Management

This phase covers all tasks required to provision Azure Communication Service (ACS) Email resources, parameterize per environment, manage secrets, and ensure that the app can securely load ACS config at runtime. All infrastructure must be automated, auditable, and follow Peeps infra conventions.

---

## 🛠 Goals

1. Provision Azure Communication Service (ACS) resources for email sending.
2. Store and manage ACS credentials in Azure Key Vault.
3. Configure all relevant environment parameter files.
4. Update Makefile/scripts to support full infra lifecycle.
5. Ensure secret loading and integration at app runtime.

---

## ✅ Codex-Friendly Task List

### 🔹 Task 1: Author ACS Bicep Module

**Prompt:**
"Create or update `infra/main.bicep` to provision an Azure Communication Service (ACS) resource with Email capability.
- Resource must be parameterized for environment (dev, stage, prod).
- Outputs: ACS resource name and connection string.
- Ensure correct tagging and resource group naming."

---

### 🔹 Task 2: Parameterize and Integrate ACS with Environment Files

**Prompt:**
"Add new ACS-related parameters to:
- `infra/main-parameters-dev.json`
- `infra/main-parameters-stage.json`
- `infra/main-parameters-prod.json`
Include resource name, connection string, and any required tags.
Document the parameter purpose in each file."

---

### 🔹 Task 3: Key Vault Secret Management

**Prompt:**
"Update `infra/key-vault.bicep` to create a secret for the ACS connection string (`ACS_CONNECTION_STRING`).
- Ensure correct access policies for app identity.
- Ensure parameter-driven population for all environments."

---

### 🔹 Task 4: Makefile and Deployment Script Automation

**Prompt:**
"Add Makefile targets for:
- Deploying ACS infra (`make deploy-acs-infra`).
- Syncing secrets to Key Vault after deployment.
- Ensure that `make deploy-infra` (or the relevant composite target) includes ACS provisioning for all envs.
- Add clean-up/destroy targets as needed for safe infra rollbacks."

---

### 🔹 Task 5: Backend Secret Loader

**Prompt:**
"Update `peepsapi/config.py` so the backend loads the ACS connection string:
- From Key Vault in deployed environments.
- From `.env` file for local/dev.
- Log a warning if missing or fallback is used.
- Add config docstring for ACS fields."

---

## 🧪 Acceptance Criteria

- ACS with Email is provisioned for all environments (`dev`, `stage`, `prod`) using Bicep.
- Parameter files and Key Vault contain correct ACS config and connection string.
- Makefile target can fully deploy, sync, and clean up ACS infrastructure.
- Backend loads ACS config and logs presence of secrets at startup (in dev/test).
- Verified via Azure portal, Key Vault, and backend log output.

---

## Relevant Files

- `infra/main.bicep` — IaC module for ACS resource and secret management.
- `infra/main-parameters-dev.json` — ACS parameters for dev environment.
- `infra/main-parameters-stage.json` — ACS parameters for stage environment.
- `infra/main-parameters-prod.json` — ACS parameters for prod environment.
- `infra/key-vault.bicep` — IaC for Key Vault where ACS connection string will be stored.
- `Makefile` — Deployment targets for provisioning ACS and syncing secrets.
- `peepsapi/config.py` — Loads ACS config/secrets from Key Vault or environment.

---

## Guidelines

- All environments must be supported (dev, stage, prod).
- IaC changes should be idempotent and parameterized.
- Ensure secret integration with backend runtime is tested before sign-off.
