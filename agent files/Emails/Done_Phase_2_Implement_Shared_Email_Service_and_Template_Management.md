# Phase 2: Implement Shared Email Service and Template Management

---

## 🎯 Goals

- Create a central email service for all transactional email flows (invite, verification, recovery) using ACS.
- Implement a system for loading and managing versioned, customizable email templates.
- Integrate syntax/domain/bounce validation before sending emails.
- Ensure the service is fully tested, horizontally reusable, and follows Peeps’ conventions.

---

## ✅ Codex-Friendly Task List

### 🔹 Task 1: Scaffold Core Email Service

**Prompt:**
"Create `peepsapi/services/email_service.py` with a class `EmailService` that exposes methods for:
- `send_email(recipient, template_name, context)`
- Internal logic for template rendering, ACS send call, error/bounce handling
- Logging all sends/failures using Peeps conventions
- Ensure pluggable for future template types and senders."

---

### 🔹 Task 2: Template Management System

**Prompt:**
"Implement email template loading in `email_service.py` (or supporting file):
- Store templates for `invite`, `verification`, `recovery` (as separate files or string resources)
- Support placeholder substitution (e.g., `{user_name}`, `{link}`)
- Support versioning for template edits (use a `version` param or naming scheme)
- Raise errors if a required template is missing."

---

### 🔹 Task 3: Email Validation Logic

**Prompt:**
"Add backend email validation in `email_service.py`:
- Use syntax validation (`email-validator` or equivalent)
- Add domain existence check
- Integrate ACS bounce feedback for deliverability (or stub for now if not supported)
- Refuse to send if validation fails; log all rejections."

---

### 🔹 Task 4: Error Handling & Logging

**Prompt:**
"All public service methods must use horizontal error handling (Peeps decorators, emoji-logging)
- Log all sends, failures, and template errors
- Ensure error details are available for dashboard stats and support triage."

---

### 🔹 Task 5: Unit Tests

**Prompt:**
"Create `tests/services/test_email_service.py`
- Cover: success, failure, all template variants, validation errors, logging paths
- Use mocks/fakes for ACS send logic
- Ensure at least 80% coverage for all service logic and error branches."

---

### 🧪 Acceptance Criteria

- Central email service (`email_service.py`) handles all sending/templating for invite, verification, and recovery flows.
- Supports at least 3 template variants with placeholders and versioning.
- All emails validated for syntax, domain, and (stubbed) deliverability before sending.
- Service and tests use Peeps conventions for error handling and logging.
- Test suite passes with high coverage.

---

## 📁 Relevant Files

- `peepsapi/services/email_service.py` — Shared email send logic and template handling.
- `tests/services/test_email_service.py` — Unit tests for email service.
- Template files (location based on project convention; e.g., `templates/email/invite.html`, etc.)

---

## 📏 Guidelines

- All services and tests must use standard error handling and emoji-logging.
- Templates should be easily extendable for new flows in future phases.
- Ensure template management supports future localization needs (future phase).
- Run all tests with `make test`.
