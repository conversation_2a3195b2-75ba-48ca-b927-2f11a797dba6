# Phase 5: Implement Recovery Flow — Models, Service, API Endpoints, and Tests

---

## 🎯 Goals

- Enable users to recover their accounts via a public, rate-limited recovery endpoint that sends a secure recovery token to their email.
- Implement the full backend logic for initiating, updating, validating, and consuming recovery tokens.
- Integrate with ACS using the recovery template for all recovery-related emails.
- Ensure all actions are audit logged and stats are available to admins.
- Cover all flows with robust tests.

---

## ✅ Codex-Friendly Task List

### 🔹 Task 1: Define/Update Recovery Models

**Prompt:**
"Define or update `RecoveryToken` (or equivalent) model in `peepsapi/auth/models/recovery.py` to represent:
- Recovery token, associated email, user (if applicable), expiry (1 hour), status, and timestamps.
- Support updating/extending expiry on re-request, and audit all attempts.
- Add any helper types as needed.
- If code is re-using invite token models, prioritize reuse but ensure recovery-specific logic is clear."

---

### 🔹 Task 2: Implement Recovery Service Logic

**Prompt:**
"Implement `RecoverService` in `peepsapi/auth/services/recover_service.py`:
- Methods for initiate_recovery, update_or_extend_token, validate_and_consume_token.
- On new or re-request, always respond with silent success.
- Only send recovery emails for emails that exist in the system (but never reveal existence in response).
- Expire old tokens after use or expiry period.
- Use the shared email service and recovery template for all recovery sends.
- Log all attempts and status for dashboard stats and audit."

---

### 🔹 Task 3: Scaffold and Extend Recovery API Endpoints

**Prompt:**
"Create or extend endpoints in `peepsapi/auth/routes/recover.py` for:
- Initiating recovery (`POST /auth/recover/initiate`)
- Validating and consuming a recovery token (`POST /auth/recover/validate`)
- Ensure public endpoints are rate-limited and always return silent success for initiate.
- Use decorators for error handling, logging, and match Peeps API response standards.
- Ensure proper linkage to registration-like flow after token validation (e.g., passkey challenge)."

---

### 🔹 Task 4: Integrate with Shared Email Service and Recovery Template

**Prompt:**
"Ensure all recovery emails use the `EmailService` and the recovery template.
- Populate placeholders (user, recovery link/token, expiry).
- Log all send failures and recovery attempts for audit and dashboard stats."

---

### 🔹 Task 5: Unit and Integration Tests

**Prompt:**
"Create or extend:
- `tests/auth/models/test_recovery.py` (model/unit tests)
- `tests/auth/services/test_recover_service.py` (service/logic tests)
- `tests/auth/routes/test_recover.py` (endpoint/integration tests)
- Cover: silent success for non-existent emails, valid/invalid/expired tokens, expiry extension on re-request, logging, audit, and email sending (mocked)."

---

### 🧪 Acceptance Criteria

- Recovery flow is implemented end-to-end: public initiate, token send, validation, and post-validation.
- Silent success response for all initiate attempts, regardless of email existence.
- Recovery tokens are single-use, expire in 1 hour, and are properly logged.
- All actions (initiation, validation, failure) are available in admin dashboard stats.
- Full test suite covers all flows, including security and edge cases.

---

## 📁 Relevant Files

- `peepsapi/auth/models/recovery.py` — Recovery token model.
- `peepsapi/auth/services/recover_service.py` — Business logic for recovery.
- `peepsapi/auth/routes/recover.py` — API endpoints for recovery.
- `peepsapi/services/email_service.py` — Used for sending recovery emails.
- `tests/auth/models/test_recovery.py` — Unit tests for recovery model.
- `tests/auth/services/test_recover_service.py` — Unit tests for recovery service.
- `tests/auth/routes/test_recover.py` — Endpoint/integration tests for recovery API.
- `templates/email/recovery.html` — Recovery email template.

---

## 📏 Guidelines

- Always return silent success for `/auth/recover/initiate` to avoid info leakage.
- Follow Peeps conventions for error handling, logging, and audit.
- Rate limit all public recovery endpoints.
- Only verified emails will be eligible in a future phase.
- Run all tests with `make test`.
