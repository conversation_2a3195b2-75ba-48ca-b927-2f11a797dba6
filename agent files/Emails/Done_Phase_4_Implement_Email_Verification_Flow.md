# Phase 4: Implement Email Verification Flow — Models, Service, API Endpoints, and Tests

---

## 🎯 Goals

- Enable users to add multiple emails to their profile and verify each email via secure, single-use links or codes.
- Implement all backend logic for issuing, resending, and validating verification codes using ACS and the verification template.
- Ensure only verified emails are used for authentication/notifications.
- Fully cover the flow with models, service logic, endpoints, and tests.

---

## ✅ Codex-Friendly Task List

### 🔹 Task 1: Define/Update Email Verification Models

**Prompt:**
"Define or update `EmailVerification` model(s) in `peepsapi/auth/models/email_verification.py` to represent:
- Verification token/code, associated email, user, expiry (1 day), status, timestamps.
- Support single-use tokens and auditing of all attempts.
- Add any helper enums/types as needed."

---

### 🔹 Task 2: Implement Email Verification Service Logic

**Prompt:**
"Implement `EmailVerificationService` in `peepsapi/auth/services/email_verification_service.py`:
- Methods for initiate_verification, resend_verification, verify_code_or_link, fetch_verification_status.
- Enforce that only unverified emails may be verified, and only verified emails may be used for login/notifications.
- Tokens must be single-use, expire after 1 day, and be audit logged.
- Use the shared email service and verification template for all sends."

---

### 🔹 Task 3: Scaffold and Extend Email Verification API Endpoints

**Prompt:**
"Create or extend endpoints in `peepsapi/auth/routes/email_verification.py` for:
- Initiating email verification for an added email
- Resending verification code/link
- Verifying an email (link/code submission)
- Fetching verification status
- Secure endpoints with auth as appropriate
- Use decorators for error handling and logging, and match Peeps API response standards."

---

### 🔹 Task 4: Integrate with Shared Email Service and Template

**Prompt:**
"Ensure all verification emails use the `EmailService` and the verification template.
- All placeholders (user, verification link/code, expiry) must be rendered.
- Log all send failures and verification events for audit/statistics."

---

### 🔹 Task 5: Unit and Integration Tests

**Prompt:**
"Create or extend:
- `tests/auth/models/test_email_verification.py` (model/unit tests)
- `tests/auth/services/test_email_verification_service.py` (service/logic tests)
- `tests/auth/routes/test_email_verification.py` (endpoint/integration tests)
- Cover: valid/invalid token, expiry, single-use enforcement, resend, error cases, logging, and email sending (mocked)."

---

### 🧪 Acceptance Criteria

- Email verification model, service, and endpoints are fully implemented and enforce all business rules.
- Verification codes/links are single-use, expire in 1 day, and are properly logged.
- Users can only verify unverified emails; only verified emails are used in critical flows.
- Email delivery and all events are auditable.
- Tests cover all flows, branches, and edge cases.

---

## 📁 Relevant Files

- `peepsapi/auth/models/email_verification.py` — Verification model definitions.
- `peepsapi/auth/services/email_verification_service.py` — Business logic for verification.
- `peepsapi/auth/routes/email_verification.py` — API endpoints for verification.
- `peepsapi/services/email_service.py` — Used for sending verification emails.
- `tests/auth/models/test_email_verification.py` — Unit tests for verification model.
- `tests/auth/services/test_email_verification_service.py` — Unit tests for verification service.
- `tests/auth/routes/test_email_verification.py` — Endpoint/integration tests for verification API.
- `templates/email/verification.html` — Verification email template.

---

## 📏 Guidelines

- Follow Peeps conventions for all error handling, logging, audit, and API response formatting.
- Enforce security requirements: single-use, minimum 32 char tokens, HTTPS-only links, 1-day expiry.
- Ensure email delivery failures and verification attempts are audit logged.
- Run all tests with `make test`.
