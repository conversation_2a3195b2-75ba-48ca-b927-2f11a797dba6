import { fetchInvites, cancelInvite, resendInvite } from '../api/admin.js';
import { formatExpiryDate } from '../utils.js';

async function drawList(container) {
    try {
        const invites = await fetchInvites();
        container.innerHTML = '';
        const table = document.createElement('table');
        table.innerHTML = '<thead><tr><th>Identifier</th><th>Status</th><th>Expires</th><th>Actions</th></tr></thead>';
        const tbody = document.createElement('tbody');
        invites.forEach(inv => {
            const tr = document.createElement('tr');
            const cancelBtn = `<button data-id="${inv.id}" class="cancel">Cancel</button>`;
            const resendBtn = `<button data-id="${inv.id}" class="resend">Resend</button>`;
            tr.innerHTML = `<td>${inv.identifierValue}</td><td>${inv.status}</td><td>${formatExpiryDate(inv.expiresAt)}</td><td>${resendBtn} ${cancelBtn}</td>`;
            tbody.appendChild(tr);
        });
        table.appendChild(tbody);
        container.appendChild(table);

        container.querySelectorAll('button.cancel').forEach(btn =>
            btn.addEventListener('click', () => {
                cancelInvite(btn.dataset.id).then(() => drawList(container));
            })
        );
        container.querySelectorAll('button.resend').forEach(btn =>
            btn.addEventListener('click', () => {
                resendInvite(btn.dataset.id).then(() => drawList(container));
            })
        );
    } catch {
        container.textContent = 'Failed to load invites';
    }
}

export function initInviteList(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    drawList(container);
    setInterval(() => drawList(container), 60000);
}
