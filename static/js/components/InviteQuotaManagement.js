import {
    fetchInviteQuota,
    setInviteQuota,
    increaseInviteQuota,
} from '../api/admin.js';
import { makeApiRequest } from '../api.js';
import { API_ENDPOINTS } from '../constants.js';

export async function loadQuota(personId, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    try {
        const q = await fetchInviteQuota(personId);
        container.textContent = `Remaining: ${q.quota} Used: ${q.used}`;
    } catch {
        container.textContent = 'Failed to load quota';
    }
}

export async function updateQuota(personId, quotaInputId, containerId) {
    const quotaInput = document.getElementById(quotaInputId);
    if (!quotaInput) {
        // Optionally, show an error in the container
        const container = document.getElementById(containerId);
        if (container) container.textContent = 'Invalid quota input element';
        return;
    }
    const quota = parseInt(quotaInput.value, 10);
    if (isNaN(quota)) {
        const container = document.getElementById(containerId);
        if (container) container.textContent = 'Quota must be a valid number';
        return;
    }
    await setInviteQuota(personId, quota);
    loadQuota(personId, containerId);
}

export async function incrementQuota(personId, incInputId, containerId) {
    const inc = parseInt(document.getElementById(incInputId).value, 10);
    if (!inc || inc <= 0) return;
    await increaseInviteQuota(personId, inc);
    loadQuota(personId, containerId);
}

export async function searchUsers(attrId, valId, resultsId) {
    const attr = document.getElementById(attrId).value;
    const val = document.getElementById(valId).value.trim();
    if (!val) return;
    const params = new URLSearchParams();
    params.append(attr, val);
    const people = await makeApiRequest(
        `${API_ENDPOINTS.PEOPLE_SEARCH}?${params.toString()}`,
        'GET'
    );
    const container = document.getElementById(resultsId);
    container.innerHTML = '';
    if (people.length === 0) {
        container.textContent = 'No results';
        return;
    }
    const table = document.createElement('table');
    const thead = document.createElement('thead');
    thead.innerHTML = `<tr><th>Name</th><th>Email</th><th>Remaining</th><th>Used</th><th>Set</th><th>Inc</th></tr>`;
    table.appendChild(thead);
    const tbody = document.createElement('tbody');
    for (const p of people) {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td><a href="#" class="person-link" data-id="${p.id}">${p.name}</a></td>
            <td>${p.emails?.[0]?.address || ''}</td>
            <td id="quota-remaining-${p.id}">...</td>
            <td id="quota-used-${p.id}">...</td>
            <td>
                <input type="number" id="quota-set-input-${p.id}" style="width:60px;">
                <button id="quota-set-btn-${p.id}">Set</button>
            </td>
            <td>
                <input type="number" id="quota-inc-input-${p.id}" style="width:60px;">
                <button id="quota-inc-btn-${p.id}">Inc</button>
            </td>
        `;
        tbody.appendChild(tr);
        // Fetch and display quota info
        fetchInviteQuota(p.id).then(q => {
            document.getElementById(`quota-remaining-${p.id}`).textContent = q.quota;
            document.getElementById(`quota-used-${p.id}`).textContent = q.used;
        }).catch(() => {
            document.getElementById(`quota-remaining-${p.id}`).textContent = 'Err';
            document.getElementById(`quota-used-${p.id}`).textContent = 'Err';
        });
        // Set quota event
        setTimeout(() => {
            const setBtn = document.getElementById(`quota-set-btn-${p.id}`);
            if (setBtn) {
                setBtn.addEventListener('click', async () => {
                    const val = parseInt(document.getElementById(`quota-set-input-${p.id}`).value, 10);
                    if (!isNaN(val)) {
                        await setInviteQuota(p.id, val);
                        const q = await fetchInviteQuota(p.id);
                        document.getElementById(`quota-remaining-${p.id}`).textContent = q.quota;
                        document.getElementById(`quota-used-${p.id}`).textContent = q.used;
                    }
                });
            }
            // Increment quota event
            const incBtn = document.getElementById(`quota-inc-btn-${p.id}`);
            if (incBtn) {
                incBtn.addEventListener('click', async () => {
                    const val = parseInt(document.getElementById(`quota-inc-input-${p.id}`).value, 10);
                    if (!isNaN(val) && val > 0) {
                        await increaseInviteQuota(p.id, val);
                        const q = await fetchInviteQuota(p.id);
                        document.getElementById(`quota-remaining-${p.id}`).textContent = q.quota;
                        document.getElementById(`quota-used-${p.id}`).textContent = q.used;
                    }
                });
            }
            // Person profile modal event
            const link = tr.querySelector('.person-link');
            if (link) {
                link.addEventListener('click', async (e) => {
                    e.preventDefault();
                    showPersonModal(p.id);
                });
            }
        }, 0);
    }
    table.appendChild(tbody);
    container.appendChild(table);
}

export async function selectUser(personId) {
    const endpoint = API_ENDPOINTS.PEOPLE_GET.replace('{id}', personId);
    const person = await makeApiRequest(endpoint, 'GET');
    document.getElementById('quota-selected-id').value = person.id;
    document.getElementById('quota-user-display').textContent = JSON.stringify(person, null, 2);
    document.getElementById('quota-user-section').classList.remove('hidden');
    loadQuota(person.id, 'quota-info');
}

export function initQuotaManagement() {
    const searchBtn = document.getElementById('quota-search-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', () =>
            searchUsers('quota-search-attribute', 'quota-search-value', 'quota-search-results')
        );
    }
    const setBtn = document.getElementById('quota-set-btn');
    if (setBtn) {
        setBtn.addEventListener('click', () => {
            const id = document.getElementById('quota-selected-id').value;
            updateQuota(id, 'quota-set-input', 'quota-info');
        });
    }
    const incBtn = document.getElementById('quota-inc-btn');
    if (incBtn) {
        incBtn.addEventListener('click', () => {
            const id = document.getElementById('quota-selected-id').value;
            incrementQuota(id, 'quota-inc-input', 'quota-info');
        });
    }
}

// Modal logic
if (!document.getElementById('person-modal')) {
    const modal = document.createElement('div');
    modal.id = 'person-modal';
    modal.style.display = 'none';
    modal.style.position = 'fixed';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100vw';
    modal.style.height = '100vh';
    modal.style.background = 'rgba(0,0,0,0.5)';
    modal.style.zIndex = '1000';
    modal.innerHTML = '<div id="person-modal-content" style="background:#fff;max-width:600px;margin:5% auto;padding:24px;position:relative;"></div>';
    modal.addEventListener('click', (e) => {
        if (e.target === modal) modal.style.display = 'none';
    });
    document.body.appendChild(modal);
}

export async function showPersonModal(personId) {
    const modal = document.getElementById('person-modal');
    const content = document.getElementById('person-modal-content');
    content.innerHTML = 'Loading...';
    modal.style.display = 'block';
    const endpoint = API_ENDPOINTS.PEOPLE_GET.replace('{id}', personId);
    const person = await makeApiRequest(endpoint, 'GET');
    content.innerHTML = `<h2>${person.name}</h2><pre>${JSON.stringify(person, null, 2)}</pre><button id="close-person-modal">Close</button>`;
    document.getElementById('close-person-modal').onclick = () => {
        modal.style.display = 'none';
    };
}
