import { fetchRecoveryTokens, cancelRecoveryToken } from '../api/admin.js';
import { formatExpiryDate } from '../utils.js';

async function drawList(container) {
    try {
        const tokens = await fetchRecoveryTokens();
        container.innerHTML = '';
        const table = document.createElement('table');
        table.innerHTML = '<thead><tr><th>Identifier</th><th>Status</th><th>Expires</th><th>Action</th></tr></thead>';
        const tbody = document.createElement('tbody');
        tokens.forEach(tok => {
            const tr = document.createElement('tr');
            const cancelBtn = `<button data-id="${tok.id}" class="cancel">Cancel</button>`;
            tr.innerHTML = `<td>${tok.identifierValue}</td><td>${tok.status}</td><td>${formatExpiryDate(tok.expiresAt)}</td><td>${cancelBtn}</td>`;
            tbody.appendChild(tr);
        });
        table.appendChild(tbody);
        container.appendChild(table);
        container.querySelectorAll('button.cancel').forEach(btn =>
            btn.addEventListener('click', () => {
                cancelRecoveryToken(btn.dataset.id).then(() => drawList(container));
            })
        );
    } catch {
        container.textContent = 'Failed to load recovery tokens';
    }
}

export function initRecoveryList(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    drawList(container);
    setInterval(() => drawList(container), 60000);
}
