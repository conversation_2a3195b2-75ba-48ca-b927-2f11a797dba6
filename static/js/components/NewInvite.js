import { API_ENDPOINTS } from '../constants.js';
import { makeApiRequest } from '../api.js';
import { getIdentifierFromForm, toggleIdentifierInputForForm } from '../forms.js';

async function create(prefix) {
    const { requestBody, isValid } = getIdentifierFromForm(prefix);
    if (!isValid) return;
    const expires = parseInt(document.getElementById(`${prefix}-expires`).value, 10) || 7;
    requestBody.expiresInDays = expires;
    try {
        await makeApiRequest(
            API_ENDPOINTS.AUTH_INVITE,
            'POST',
            requestBody,
            `${prefix}-display`,
            `${prefix}-result`
        );

        // Show the result and reset button, keep the form visible
        const result = document.getElementById(`${prefix}-result`);
        if (result) result.classList.remove('hidden');
        const resetBtn = document.getElementById(`${prefix}-reset-btn`);
        if (resetBtn) resetBtn.classList.remove('hidden');

        // Optionally, disable the create button to prevent duplicate invites
        const btn = document.getElementById(`${prefix}-create-btn`);
        if (btn) btn.disabled = true;
    } catch (e) {
        // Leave form visible on error so the admin can retry
        console.error('Failed to create invite:', e);
    }
}

export function initNewInvite(prefix = 'new-invite') {
    const btn = document.getElementById(`${prefix}-create-btn`);
    if (btn) {
        btn.addEventListener('click', () => create(prefix));
    }
    const selector = document.getElementById(`${prefix}-identifier-type`);
    if (selector) {
        selector.addEventListener('change', () => toggleIdentifierInputForForm(prefix));
    }
    // Add reset button logic
    const resetBtn = document.getElementById(`${prefix}-reset-btn`);
    if (resetBtn) {
        resetBtn.addEventListener('click', () => {
            // Hide only the result pane
            const result = document.getElementById(`${prefix}-result`);
            if (result) result.classList.add('hidden');
            resetBtn.classList.add('hidden');
            // Show the form again
            const form = document.getElementById(`${prefix}-form`);
            if (form) {
                form.classList.remove('hidden');
                form.reset && form.reset();
            }
            // Re-enable the create button
            const btn = document.getElementById(`${prefix}-create-btn`);
            if (btn) btn.disabled = false;
        });
    }
}
