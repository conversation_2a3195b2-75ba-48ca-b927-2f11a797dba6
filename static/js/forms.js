/**
 * Forms module for handling form-related functionality
 */

import { makeApiRequest } from './api.js';
import { API_ENDPOINTS } from './constants.js';
import {
    challengeData,
    prepareWebAuthnRegistrationOptions,
    formatRegistrationCredential,
    formatAuthenticationCredential
} from './webauthn.js';

/**
 * Generic function to toggle between email and phone input for any form
 * @param {string} formPrefix - Prefix for form element IDs
 */
export function toggleIdentifierInputForForm(formPrefix) {
    const identifierType = document.getElementById(`${formPrefix}-identifier-type`).value;
    const emailContainer = document.getElementById(`${formPrefix}-email-container`);
    const phoneContainer = document.getElementById(`${formPrefix}-phone-container`);

    if (identifierType === 'email') {
        emailContainer.classList.remove('hidden');
        phoneContainer.classList.add('hidden');
    } else {
        emailContainer.classList.add('hidden');
        phoneContainer.classList.remove('hidden');
    }
}

/**
 * Function to toggle between email and phone input for recovery form
 */
export function toggleRecoveryIdentifierInput() {
    toggleIdentifierInputForForm('recovery');
}

/**
 * Function to toggle between email and phone input for login form
 */
export function toggleLoginIdentifierInput() {
    toggleIdentifierInputForForm('login');
}

/**
 * Function to toggle between email and phone input for invite form
 */
export function toggleIdentifierInput() {
    const identifierType = document.getElementById('invite-identifier-type').value;
    const emailContainer = document.getElementById('email-input-container');
    const phoneContainer = document.getElementById('phone-input-container');

    // Note: This function doesn't use toggleIdentifierInputForForm because
    // the invite form has different container IDs (without the 'invite-' prefix)
    if (identifierType === 'email') {
        emailContainer.classList.remove('hidden');
        phoneContainer.classList.add('hidden');
    } else {
        emailContainer.classList.add('hidden');
        phoneContainer.classList.remove('hidden');
    }
}

/**
 * Generic function to get identifier value from a form
 * @param {string} formPrefix - Prefix for form element IDs
 * @returns {object} - Object with requestBody, isValid, and identifierType
 */
export function getIdentifierFromForm(formPrefix) {
    const identifierType = document.getElementById(`${formPrefix}-identifier-type`).value;
    let requestBody = {};
    let isValid = true;

    if (identifierType === 'email') {
        // Special case for invite form which has different container IDs
        const emailId = formPrefix === 'invite' ? 'invite-email' : `${formPrefix}-email`;
        const email = document.getElementById(emailId).value;
        if (!email) {
            alert('Please enter an email address');
            isValid = false;
        } else {
            requestBody.email = email;
        }
    } else {
        // Special case for invite form which has different container IDs
        const phoneId = formPrefix === 'invite' ? 'invite-phone' : `${formPrefix}-phone`;
        const phone = document.getElementById(phoneId).value;
        if (!phone) {
            alert('Please enter a phone number');
            isValid = false;
        } else {
            requestBody.phoneNumber = phone;
        }
    }

    return { requestBody, isValid, identifierType };
}

/**
 * Function to initiate recovery
 * @returns {Promise<void>}
 */
export async function initiateRecovery() {
    try {
        const { requestBody, isValid, identifierType } = getIdentifierFromForm('recovery');

        if (!isValid) return;

        const data = await makeApiRequest(
            API_ENDPOINTS.AUTH_RECOVERY_INITIATE,
            'POST',
            requestBody,
            'recovery-token-display',
            'recovery-result'
        );

        // Store the recovery token and user ID for later use
        document.getElementById('recovery-token').value = data.token;
        document.getElementById('user-id').value = data.personId;

        // Display instructions for the recovery token
        const displayElement = document.getElementById('recovery-token-display');
        const originalText = displayElement.textContent;
        displayElement.textContent = originalText + "\n\nA recovery link has been sent to your " +
            (identifierType === 'email' ? "email address" : "phone number") +
            ".\n\nIn a real implementation, user would click that link.\n\nFor testing, paste the link below to continue.";

        // Show the paste recovery link UI
        const pasteLinkContainer = document.getElementById('paste-recovery-link-container');
        if (pasteLinkContainer) {
            pasteLinkContainer.classList.remove('hidden');
        }
    } catch (error) {
        alert('Error initiating recovery: ' + error.message);
    }
}

/**
 * Function to register a passkey during recovery
 * @returns {Promise<void>}
 */
export async function registerPasskey() {
    if (!challengeData) {
        alert('No challenge data available. Please create a challenge first.');
        return;
    }

    try {

        console.log("registerPasskey - challengeData: ", challengeData);

        const publicKeyOptions = challengeData.publicKey;

        // Convert the challenge data to the format expected by the browser
        const publicKeyCredentialCreationOptions = prepareWebAuthnRegistrationOptions(publicKeyOptions);

        // Create the credential
        const credential = await navigator.credentials.create({
            publicKey: publicKeyCredentialCreationOptions
        });

        // Convert the credential to the format expected by the server
        const attestationResponse = formatRegistrationCredential(credential);

        // Send the attestation response to the server
        const data = await makeApiRequest(
            API_ENDPOINTS.AUTH_RECOVERY_REGISTER_VERIFY,
            'POST',
            {
                token: document.getElementById('recovery-token').value,
                credential: attestationResponse,
                isRecovery: true
            },
            'registration-display',
            'registration-result'
        );
    } catch (error) {
        alert('Error registering passkey: ' + error.message);
    }
}

/**
 * Function to authenticate with passkey
 * @returns {Promise<void>}
 */
export async function authenticateWithPasskey() {
    if (!challengeData) {
        alert('No challenge data available. Please get an authentication challenge first.');
        return;
    }

    try {
        // Use the pre-processed publicKeyCredentialRequestOptions from getAuthenticationChallenge
        const publicKeyOptions = challengeData.publicKeyCredentialRequestOptions;
        console.log("authenticateWithPasskey - challengeData: ", challengeData);

        // Get the credential from the browser
        const credential = await navigator.credentials.get({
            publicKey: publicKeyOptions
        });
        console.log("authenticateWithPasskey - credential: ", credential);

        // Convert the credential to the format expected by the server
        const assertionResponse = formatAuthenticationCredential(credential);
        console.log("authenticateWithPasskey - assertionResponse: ", assertionResponse);

        // Send the assertion response to the server
        const data = await makeApiRequest(
            API_ENDPOINTS.AUTH_LOGIN_VERIFY,
            'POST',
            {
                credential: assertionResponse
            },
            'auth-result-display',
            'auth-result'
        );
    } catch (error) {
        alert('Error authenticating with passkey: ' + error.message);
    }
}

/**
 * Function to create an invite
 * @returns {Promise<void>}
 */
export async function createInvite() {
    try {
        // Check if a person is selected
        const personId = document.getElementById('selected-person-id').value;
        if (!personId) {
            alert('Please select a person first');
            return;
        }

        const { requestBody, isValid } = getIdentifierFromForm('invite');

        if (!isValid) return;

        // Add expiresInDays to the request body
        const expiresInDays = parseInt(document.getElementById('invite-expires').value) || 7;
        requestBody.expiresInDays = expiresInDays;

        // Use our improved makeApiRequest function
        const data = await makeApiRequest(
            API_ENDPOINTS.AUTH_INVITE,
            'POST',
            requestBody,
            'invite-display',
            'invite-result'
        );

        // Store the invite token for later use
        // Support new format: peepsapp://{verb}/{token}
        try {
            const url = data.inviteUrl;
            // Remove protocol (peepsapp://)
            const path = url.replace(/^peepsapp:\/\//, '');
            // path is now {verb}/{token}
            const parts = path.split('/');
            const token = parts.length > 1 ? parts[1] : '';
            document.getElementById('invite-token').value = token;
        } catch (e) {
            document.getElementById('invite-token').value = '';
        }
    } catch (error) {
        alert('Error creating invite: ' + error.message);
    }
}

/**
 * Function to complete registration
 * @returns {Promise<void>}
 */
export async function completeRegistration() {
    if (!challengeData) {
        alert('No challenge data available. Please create a challenge first.');
        return;
    }

    try {
        const publicKeyOptions = challengeData.publicKey;
        console.log("completeRegistration - challengeData: ", challengeData);

        // Convert the challenge data to the format expected by the browser
        const publicKeyCredentialCreationOptions = prepareWebAuthnRegistrationOptions(publicKeyOptions);

        // Create the credential
        const credential = await navigator.credentials.create({
            publicKey: publicKeyCredentialCreationOptions
        });

        // Convert the credential to the format expected by the server
        const attestationResponse = formatRegistrationCredential(credential);

        // Send the attestation response to the server
        const data = await makeApiRequest(
            API_ENDPOINTS.AUTH_REGISTER_VERIFY,
            'POST',
            {
                credential: attestationResponse,
                token: document.getElementById('invite-token').value
            },
            'reg-result-display',
            'reg-result'
        );
    } catch (error) {
        alert('Error completing registration: ' + error.message);
    }
}

// Handle the 'Paste Recovery Link' UI
if (document.getElementById('use-recovery-link-btn')) {
    document.getElementById('use-recovery-link-btn').onclick = function() {
        const url = document.getElementById('recovery-link-input').value.trim();
        let token = null;
        try {
            if (url.startsWith('peepsapp://')) {
                // New format: peepsapp://{verb}/{token}
                const path = url.replace(/^peepsapp:\/\//, '');
                const parts = path.split('/');
                token = parts.length > 1 ? parts[1] : '';
            } else {
                const parsedUrl = new URL(url);
                token = parsedUrl.searchParams.get('token');
            }
        } catch (e) {
            // Not a valid URL
        }
        if (!token) {
            document.getElementById('recovery-link-error').textContent = "Invalid link. Please check and try again.";
            document.getElementById('recovery-link-error').style.display = "block";
            return;
        }
        document.getElementById('recovery-link-error').style.display = "none";
        document.getElementById('recovery-token').value = token;
        // Optionally, show the next step in the UI
        // document.getElementById('recovery-result').classList.remove('hidden');
    };
}

// Remove the 'Continue' button logic and instead validate the token when the user clicks 'Create Registration Challenge'.

// Helper to extract token from pasted link
function extractTokenFromRecoveryInput() {
    const url = document.getElementById('recovery-link-input').value.trim();
    let token = null;
    try {
        if (url.startsWith('peepsapp://')) {
            // New format: peepsapp://{verb}/{token}
            const path = url.replace(/^peepsapp:\/\//, '');
            const parts = path.split('/');
            token = parts.length > 1 ? parts[1] : '';
        } else {
            const parsedUrl = new URL(url);
            token = parsedUrl.searchParams.get('token');
        }
    } catch (e) {
        // Not a valid URL
    }
    return token;
}

// Patch the create recovery challenge button to validate the token
const createRecoveryChallengeButton = document.getElementById('create-recovery-challenge-btn');
if (createRecoveryChallengeButton) {
    createRecoveryChallengeButton.addEventListener('click', function(event) {
        // Only check if the recovery link UI is visible
        const pasteLinkContainer = document.getElementById('paste-recovery-link-container');
        if (pasteLinkContainer && !pasteLinkContainer.classList.contains('hidden')) {
            const token = extractTokenFromRecoveryInput();
            if (!token) {
                document.getElementById('recovery-link-error').textContent = "Invalid link. Please check and try again.";
                document.getElementById('recovery-link-error').style.display = "block";
                event.preventDefault();
                return false;
            }
            document.getElementById('recovery-link-error').style.display = "none";
            document.getElementById('recovery-token').value = token;
        }
        // Allow the normal flow to continue
    });
}
