/**
 * Identity management module
 * Handles person search, selection, and identity management
 */

import { makeApiRequest } from './api.js';
import { getCookie, setCookie, deleteCookie } from './utils.js';
import { API_ENDPOINTS } from './constants.js';
import { COOKIE_CONSTANTS } from './constants.js';
import { updateSidebarVisibility } from './ui.js';

/**
 * Function to search for people
 * @returns {Promise<void>}
 */
export async function searchPeople() {
    try {
        const searchAttribute = document.getElementById('search-attribute').value;
        const searchValue = document.getElementById('search-value').value;

        // Check if search value is provided
        if (!searchValue || !searchValue.trim()) {
            alert('Please enter a search value');
            return;
        }

        // Build query parameters - use the attribute as the parameter name
        const params = new URLSearchParams();
        params.append(searchAttribute, searchValue.trim());

        // Use our improved makeApiRequest function
        const people = await makeApiRequest(
            `${API_ENDPOINTS.PEOPLE_SEARCH}?${params.toString()}`,
            'GET',
            null,
            null,
            null
        );

        // Display search results
        const peopleList = document.getElementById('people-list');
        peopleList.innerHTML = '';

        if (people.length === 0) {
            peopleList.innerHTML = '<p>No people found matching your search criteria.</p>';
        } else {
            // Create a table for the results
            const table = document.createElement('table');
            table.style.width = '100%';
            table.style.borderCollapse = 'collapse';

            // Add table header
            const thead = document.createElement('thead');
            thead.innerHTML = `
                <tr>
                    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Name</th>
                    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Email</th>
                    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Phone</th>
                    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Action</th>
                </tr>
            `;
            table.appendChild(thead);

            // Add table body
            const tbody = document.createElement('tbody');
            people.forEach(person => {
                const tr = document.createElement('tr');

                // Get primary email and phone
                const primaryEmail = person.emails && person.emails.length > 0 ?
                    person.emails[0].address : 'N/A';

                const primaryPhone = person.phoneNumbers && person.phoneNumbers.length > 0 ?
                    person.phoneNumbers[0].number : 'N/A';

                // Get the lastName
                const lastName = person.lastName;

                tr.innerHTML = `
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${person.name} ${lastName}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${primaryEmail}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${primaryPhone}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">
                        <button class="select-person-btn" data-person-id="${person.id}">Select</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
            table.appendChild(tbody);

            peopleList.appendChild(table);

            // Add event listeners to select buttons
            const selectButtons = document.querySelectorAll('.select-person-btn');
            selectButtons.forEach(button => {
                button.addEventListener('click', function(event) {
                    event.preventDefault();
                    const personId = this.getAttribute('data-person-id');
                    selectPerson(personId);
                });
            });
        }

        // Show search results
        document.getElementById('search-results').classList.remove('hidden');

    } catch (error) {
        alert('Error searching for people: ' + error.message);
    }
}

/**
 * Function to select a person
 * @param {string} personId - ID of person to select
 * @returns {Promise<void>}
 */
export async function selectPerson(personId) {
    try {
        // Use our improved makeApiRequest function
        const getPersonEndpoint = API_ENDPOINTS.PEOPLE_GET.replace('{id}', personId);
        const person = await makeApiRequest(
            getPersonEndpoint,
            'GET',
            null,
            null,
            null
        );

        // Use the generic function to handle person selection
        handlePersonSelection(person);
    } catch (error) {
        alert('Error selecting person: ' + error.message);
    }
}

/**
 * Generic function to handle person selection and display
 * @param {object} person - Person object
 * @param {boolean} setCookieFlag - Whether to set a cookie
 */
export function handlePersonSelection(person, setCookieFlag = true) {
    // Get the lastName
    const lastName = person.lastName;

    // Format person data for display
    const personData = {
        id: person.id,
        name: person.name,
        lastName: lastName,
        emails: person.emails,
        phoneNumbers: person.phoneNumbers
    };

    const formattedPersonData = JSON.stringify(personData, null, 2);

    // Display selected person in both places
    document.getElementById('selected-person-display').textContent = formattedPersonData;
    document.getElementById('current-identity-display').textContent = formattedPersonData;

    // Store the person ID
    document.getElementById('selected-person-id').value = person.id;

    // Set a cookie if requested
    if (setCookieFlag) {
        // Create a cookie with the person ID that will work across the site
        setCookie(COOKIE_CONSTANTS.OVERRIDE_PERSON_ID_COOKIE_NAME, person.id);
    }

    // Show current identity and hide search interface
    document.getElementById('current-identity').classList.remove('hidden');
    document.getElementById('current-identity-content').classList.remove('hidden');
    document.getElementById('search-interface').classList.add('hidden');
    document.getElementById('search-results').classList.add('hidden');

    // Enable the create invite button
    document.getElementById('create-invite-btn').disabled = false;

    // Hide the warning
    document.getElementById('no-person-selected-warning').classList.add('hidden');

    // Hide the device management warning
    document.getElementById('device-management-warning').classList.add('hidden');

    // If we're currently on the device management section, load devices
    if (document.getElementById('device-info-section').classList.contains('active') ||
        !document.getElementById('device-info-section').classList.contains('hidden')) {
        // Import dynamically to avoid circular dependencies
        import('./devices.js').then(module => {
            module.fetchDevices();
        });
    }

    // Update sidebar to show conditional items
    updateSidebarVisibility(true);

    // Update the Peeps Identity nav text to show the person's name
    const peepIdentityNav = document.getElementById('peep-identity-nav');
    peepIdentityNav.textContent = `As ${person.name} ${lastName}`;
    peepIdentityNav.classList.add('person-selected');

    // Pre-fill forms with the person's contact information
    prefillContactForms(person);
}

/**
 * Function to pre-fill contact forms with person data
 * @param {object} person - Person object
 */
export function prefillContactForms(person) {
    const recoveryIdentifierType = document.getElementById('recovery-identifier-type');
    const loginIdentifierType = document.getElementById('login-identifier-type');

    if (person.emails && person.emails.length > 0) {
        // Set email for recovery form
        recoveryIdentifierType.value = 'email';
        document.getElementById('recovery-email').value = person.emails[0].address;

        // Trigger the toggle function
        const event = new Event('change');
        recoveryIdentifierType.dispatchEvent(event);

        // Set email for login form
        loginIdentifierType.value = 'email';
        document.getElementById('login-email').value = person.emails[0].address;
        loginIdentifierType.dispatchEvent(event);
    } else if (person.phoneNumbers && person.phoneNumbers.length > 0) {
        // Set phone for recovery form
        recoveryIdentifierType.value = 'phone';
        document.getElementById('recovery-phone').value = person.phoneNumbers[0].number;
        recoveryIdentifierType.dispatchEvent(event);

        // Set phone for login form
        loginIdentifierType.value = 'phone';
        document.getElementById('login-phone').value = person.phoneNumbers[0].number;
        loginIdentifierType.dispatchEvent(event);
    }
}

/**
 * Function to clear the identity
 */
export function logout() {
    // Delete the session token cookie
    deleteCookie(COOKIE_CONSTANTS.SESSION_TOKEN_COOKIE_NAME);
    deleteCookie(COOKIE_CONSTANTS.AZURE_AD_COOKIE_NAME);
    window.location.reload();
}

/**
 * Function to clear the identity
 */
export function clearIdentity() {
    // Delete the person ID cookie
    deleteCookie(COOKIE_CONSTANTS.OVERRIDE_PERSON_ID_COOKIE_NAME);

    // Hide current identity and show search interface
    document.getElementById('current-identity').classList.add('hidden');
    document.getElementById('current-identity-content').classList.add('hidden');
    document.getElementById('search-interface').classList.remove('hidden');

    // Clear selected person
    document.getElementById('selected-person-id').value = '';
    document.getElementById('selected-person').classList.add('hidden');

    // Disable create invite button
    document.getElementById('create-invite-btn').disabled = true;

    // Show warning
    document.getElementById('no-person-selected-warning').classList.remove('hidden');

    // Show the device management warning and hide content
    document.getElementById('device-management-warning').classList.remove('hidden');
    document.getElementById('device-management-content').classList.add('hidden');
    document.getElementById('devices-list-container').classList.add('hidden');
    document.getElementById('device-details-container').classList.add('hidden');

    // Update sidebar to hide conditional items
    updateSidebarVisibility(false);

    // Reset the Peeps Identity nav text and remove highlight
    const peepIdentityNav = document.getElementById('peep-identity-nav');
    peepIdentityNav.textContent = 'Peeps Identity';
    peepIdentityNav.classList.remove('person-selected');
}

/**
 * Function to show the search interface
 */
export function showSearchInterface() {
    document.getElementById('current-identity-content').classList.add('hidden');
    document.getElementById('search-interface').classList.remove('hidden');
}

/**
 * Function to check for existing identity
 * @returns {Promise<void>}
 */
export async function checkExistingIdentity() {
    const personId = getCookie(COOKIE_CONSTANTS.OVERRIDE_PERSON_ID_COOKIE_NAME);

    if (personId) {
        try {
            // Use our improved makeApiRequest function
            const getPersonEndpoint = API_ENDPOINTS.PEOPLE_GET.replace('{id}', personId);

            const person = await makeApiRequest(
                getPersonEndpoint,
                'GET',
                null,
                null,
                null
            );

            // Use the generic function to handle person selection
            // Don't set the cookie again since it already exists
            handlePersonSelection(person, false);
        } catch (error) {
            console.error('Error checking existing identity:', error);
            // If there's an error, clear the cookie and show search interface
            clearIdentity();
        }
    } else {
        // No person ID, ensure conditional items are hidden
        updateSidebarVisibility(false);
    }
}
