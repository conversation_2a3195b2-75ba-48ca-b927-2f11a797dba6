/**
 * API module for making requests to the server
 * Handles API base URL configuration and request formatting
 */

import { DOMAIN_CONSTANTS, API_ENDPOINTS } from './constants.js';

// Base URL for API - now configurable
let API_BASE_URL = DOMAIN_CONSTANTS.LOCAL;

/**
 * Function to determine the appropriate API base URL based on the current hostname
 * @returns {string} - The API base URL
 */
function determineApiBaseUrl() {
    const currentHostname = window.location.hostname;

    if (currentHostname.includes('local.peepsapp.ai')) {
        return DOMAIN_CONSTANTS.LOCAL;
    } else if (currentHostname.includes('dev.peepsapp.ai')) {
        return DOMAIN_CONSTANTS.DEV;
    } else if (currentHostname.includes('stage.peepsapp.ai')) {
        return DOMAIN_CONSTANTS.STAGE;
    } else if (currentHostname.includes('api.peepsapp.ai')) {
        return DOMAIN_CONSTANTS.PRODUCTION;
    }

    // Default to local for development
    return DOMAIN_CONSTANTS.LOCAL;
}

// Set the API base URL based on the current hostname
API_BASE_URL = determineApiBaseUrl();

/**
 * Utility function for making API requests
 * @param {string} endpoint - API endpoint to call
 * @param {string} method - HTTP method (GET, POST, PUT, DELETE)
 * @param {object} body - Request body (for non-GET requests)
 * @param {string} displayElementId - ID of element to display response in
 * @param {string} resultContainerId - ID of container to show/hide
 * @returns {Promise<any>} - Promise resolving to response data
 */
export async function makeApiRequest(endpoint, method, body, displayElementId, resultContainerId) {
    try {
        // Create request options
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include' // Include cookies for authentication
        };

        // Only add body for non-GET requests
        if (method !== 'GET' && body !== null) {
            options.body = JSON.stringify(body);
        }

        const response = await fetch(`${API_BASE_URL}${endpoint}`, options);

        // Check if the response is OK
        if (!response.ok) {
            let errorText = await response.text();
            let errorMessage = 'Unknown error occurred';
            let errorCode = 'UNKNOWN_ERROR';
            let errorType = 'unknown_error';
            let errorDetails = null;

            // Try to parse the error as JSON to extract standardized error fields
            try {
                const errorJson = JSON.parse(errorText);

                // Handle standardized error response format
                if (errorJson.message) {
                    errorMessage = errorJson.message;
                } else if (errorJson.detail) {
                    // Fallback for older error format
                    errorMessage = errorJson.detail;
                }

                if (errorJson.error_code) {
                    errorCode = errorJson.error_code;
                }

                if (errorJson.error_type) {
                    errorType = errorJson.error_type;
                }

                if (errorJson.details) {
                    errorDetails = errorJson.details;
                }
            } catch (e) {
                // If parsing fails, use the raw text
                console.log('Error parsing error response:', e);
                errorMessage = errorText;
            }

            // Create a formatted error message for display
            const formattedError = `Error: ${errorCode} (${response.status})\nType: ${errorType}\nMessage: ${errorMessage}`;

            // Add details if available
            const detailsText = errorDetails ? `\n\nDetails: ${JSON.stringify(errorDetails, null, 2)}` : '';

            if (displayElementId) {
                const displayElement = document.getElementById(displayElementId);
                displayElement.textContent = formattedError + detailsText;

                // Add error styling
                displayElement.classList.add('error-text');

                if (resultContainerId) {
                    document.getElementById(resultContainerId).classList.remove('hidden');
                }
            }

            // Create a custom error object with additional properties
            const error = new Error(errorMessage);
            error.code = errorCode;
            error.type = errorType;
            error.status = response.status;
            error.details = errorDetails;

            // Special handling for authentication errors
            if (errorType === 'authentication_error' && endpoint !== API_ENDPOINTS.AUTH_LOGIN_VERIFY && endpoint !== API_ENDPOINTS.AUTH_LOGIN_CHALLENGE) {
                console.warn('Authentication error detected. User may need to log in again.');
            }

            throw error;
        }

        const data = await response.json();

        if (displayElementId) {
            const displayElement = document.getElementById(displayElementId);
            displayElement.textContent = JSON.stringify(data, null, 2);

            // Remove error styling if it exists
            displayElement.classList.remove('error-text');

            if (resultContainerId) {
                document.getElementById(resultContainerId).classList.remove('hidden');
            }
        }

        return data;
    } catch (error) {
        console.error('API request failed:', error);

        if (displayElementId) {
            const displayElement = document.getElementById(displayElementId);

            // Format the error message based on available properties
            let errorMessage = `Error: ${error.message}`;
            if (error.code) {
                errorMessage = `Error ${error.code}: ${error.message}`;
            }

            displayElement.textContent = errorMessage;
            displayElement.classList.add('error-text');

            if (resultContainerId) {
                document.getElementById(resultContainerId).classList.remove('hidden');
            }
        }

        throw error;
    }
}

// Export the API_BASE_URL for use in other modules
export { API_BASE_URL };
