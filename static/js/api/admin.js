// Admin API utility functions for dashboard management
import { makeApiRequest } from '../api.js';

export async function fetchEmailStats(hours = 24) {
    return await makeApiRequest(`/dashboard/email?hours=${hours}`, 'GET');
}

export async function fetchInviteQuota(personId) {
    return await makeApiRequest(`/dashboard/quota/${personId}`, 'GET');
}

export async function setInviteQuota(personId, quota) {
    return await makeApiRequest(`/dashboard/quota/${personId}/set/${quota}`, 'POST');
}

export async function fetchInvites() {
    return await makeApiRequest('/auth/invite/active', 'GET');
}

export async function increaseInviteQuota(personId, increment) {
    return await makeApiRequest(`/dashboard/quota/${personId}/increase/${increment}`, 'POST');
}

export async function resendInvite(inviteId) {
    return await makeApiRequest(`/auth/invite/${inviteId}/resend`, 'POST');
}

export async function cancelInvite(inviteId) {
    return await makeApiRequest(`/auth/invite/${inviteId}`, 'DELETE');
}

export async function fetchRecoveryTokens() {
    return await makeApiRequest('/auth/recover/tokens', 'GET');
}

export async function cancelRecoveryToken(tokenId) {
    return await makeApiRequest(`/auth/recover/token/${tokenId}`, 'DELETE');
}
