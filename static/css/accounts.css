/**
 * Styles for the Account Management page
 */

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
    background-color: #f5f5f5;
}

.page-container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background-color: #333;
    color: white;
    padding: 20px 0;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.sidebar-header {
    padding: 0 20px 20px;
    border-bottom: 1px solid #444;
    margin-bottom: 20px;
}

.sidebar-header h2 {
    margin: 0;
    font-size: 20px;
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    padding: 0;
}

.sidebar-nav a {
    display: block;
    padding: 12px 20px;
    color: #ddd;
    text-decoration: none;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover, .sidebar-nav a.active {
    background-color: #444;
    color: white;
    border-left-color: #0078d4;
}

.person-selected {
    color: #0078d4 !important;
    text-shadow: 0 0 10px rgba(0, 120, 212, 0.5);
    font-weight: bold;
    background-color: rgba(0, 120, 212, 0.1) !important;
    border-left-color: #0078d4 !important;
    box-shadow: 0 0 8px rgba(0, 120, 212, 0.3);
}

.main-content {
    flex: 1;
    padding: 20px;
    margin-left: 250px;
    width: calc(100% - 250px);
}

.section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: white;
    position: relative;
}

.identity-actions {
    position: absolute;
    top: 20px;
    right: 20px;
}

@media (max-width: 768px) {
    .page-container {
        flex-direction: column;
    }
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    .main-content {
        margin-left: 0;
        width: 100%;
    }
}

button {
    padding: 10px 15px;
    background-color: #0078d4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
    margin-bottom: 10px;
}

button:hover {
    background-color: #106ebe;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.danger-button {
    background-color: #d9534f;
    color: white;
}

.danger-button:hover {
    background-color: #c9302c;
}

input, select {
    padding: 8px;
    margin-bottom: 10px;
    width: 100%;
    box-sizing: border-box;
}

pre {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}

.hidden {
    display: none;
}

.toggle-button {
    background-color: #555;
}

.toggle-button:hover {
    background-color: #333;
}

.warning {
    background-color: #fff3cd;
    color: #856404;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.success {
    background-color: #d4edda;
    color: #155724;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.error-text {
    color: #721c24;
    background-color: #f8d7da;
    padding: 10px;
    border-radius: 5px;
    white-space: pre-wrap;
    font-family: monospace;
}

/* Table styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

th, td {
    text-align: left;
    padding: 8px;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
}

tr:hover {
    background-color: #f5f5f5;
}

/* Dialog styles */
.dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.dialog-content {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 100%;
}

.dialog-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.dialog-buttons button {
    margin-left: 10px;
}

/* Device table styles */
.device-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.device-table th, .device-table td {
    text-align: left;
    padding: 8px;
    border-bottom: 1px solid #ddd;
}

.device-table th {
    background-color: #f2f2f2;
}

.device-table tr:hover {
    background-color: #f5f5f5;
    cursor: pointer;
}

.device-table tr.selected {
    background-color: #e0f0ff;
}

.device-status-active {
    color: #155724;
    background-color: #d4edda;
    padding: 3px 6px;
    border-radius: 3px;
}

.device-status-inactive {
    color: #721c24;
    background-color: #f8d7da;
    padding: 3px 6px;
    border-radius: 3px;
}

.device-management-actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

#device-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

#device-details-container {
    margin-top: 0;
}

/* Device details styling */
.device-details-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    background-color: #f9f9f9;
    border-radius: 6px;
    padding: 8px;
    margin-top: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Device detail row styling */
.device-detail-row {
    background-color: #f9f9f9;
}

.device-detail-row td {
    padding: 0;
    border-top: none;
}

.device-detail-item {
    display: flex;
    flex-direction: column;
    padding: 4px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.detail-label {
    font-weight: bold;
    color: #555;
    font-size: 0.85em;
    margin-bottom: 2px;
}

.detail-value {
    color: #333;
    font-size: 0.95em;
}

.device-id {
    font-family: monospace;
    font-size: 0.9em;
    word-break: break-all;
}

/* Tab styles */
.device-details-tabs {
    display: flex;
    margin-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.tab-button {
    padding: 8px 12px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    margin-right: 3px;
    color: #555;
    font-size: 0.9em;
}

.tab-button:hover {
    background-color: #e0e0e0;
}

.tab-button.active {
    background-color: #0078d4;
    color: white;
}

.tab-content {
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 0 0 6px 6px;
}

/* Sessions styles */
.sessions-info {
    margin-bottom: 10px;
    font-size: 0.95em;
    line-height: 1.4;
}

.sessions-info p {
    margin: 5px 0;
}

.sessions-actions {
    margin-bottom: 10px;
}

.secondary-button {
    background-color: #6c757d;
    color: white;
    padding: 6px 10px;
    font-size: 0.9em;
}

.secondary-button:hover {
    background-color: #5a6268;
}

.sessions-summary {
    background-color: white;
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    font-size: 0.95em;
}

.sessions-summary ul {
    margin-left: 15px;
    margin-bottom: 10px;
    padding-left: 5px;
}

.sessions-summary li {
    margin-bottom: 3px;
}

.collapsible-underline {
    border-bottom: 1px solid #444;
    margin: 0 20px 20px 20px;
}

.collapsible-header {
    padding: 10px;
}
