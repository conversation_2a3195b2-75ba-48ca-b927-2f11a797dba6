AZ<PERSON><PERSON>_KEY_VAULT_URL=https://peepsapp-vault-local.vault.azure.net/
AZURE_AD_CLIENT_ID=
AZURE_AD_CLIENT_SECRET=
AZURE_AD_TENANT_ID=
AZURE_AD_REDIRECT_URI=

WEBAUTHN_RP_ID=
WEBAUTHN_RP_NAME=
WEBAUTHN_RP_ORIGIN=

JWT_SECRET=

# Variables useful for local development
DEBUG_MODE=False

# Mock OpenAI responses
OPENAI_MOCK_ENABLED=true

# Log configuration, uncomment to override, see LOGGING.md for details
# LOG_LEVEL_ROOT=
# LOG_LEVEL=
# LOG_LEVEL_AZURE=
# LOG_FORMAT=
# LOG_CONTEXT=
# LOG_COLORED=
# LOG_SHORT_LEVELNAME=

# Jobs
JOBS_WAIT_BETWEEN_RESERVE_ATTEMPTS_SECONDS=3600
