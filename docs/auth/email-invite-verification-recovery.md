# Email Invite, Verification, and Recovery Flows

This document describes the core user flows that rely on one-time tokens.

## Token Generation
- **Invites** – tokens generated with `secrets.token_urlsafe(32)` and valid for up to 30 days.
- **Email Verification** – tokens generated with the same secure RNG and valid for 1 day.
- **Account Recovery** – tokens generated with the same RNG and expire after 1 hour.

All tokens are at least 32 characters and generated using a cryptographically secure random number generator. Expiry values are enforced by the backend when tokens are validated or consumed.

## HTTPS Links
All emails use `https` links. The helper in `peepsapi.auth.utils.constants.ensure_https` forces HTTPS regardless of the incoming protocol header.

## Endpoint Summary
- `POST /auth/invite` – create an invite. Silent success if email already registered.
- `POST /auth/verify/email/initiate` – start email verification.
- `GET /auth/verify/email/{token}` – verify email token.
- `POST /auth/recover/initiate` – begin recovery (always returns success message).
- `POST /auth/recover/challenge` – create a WebAuthn challenge from a recovery token.

Refer to `docs/api.md` for full request/response formats and error codes.
