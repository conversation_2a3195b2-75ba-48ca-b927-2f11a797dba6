# Email Template Management

Templates are stored under `static/templates`. Each template is a Jinja2 HTML file. To update or add templates:

1. Edit or add a template file in the directory.
2. Keep variables simple – `{{ user_name }}`, `{{ link }}`, and `{{ subject }}` are common.
3. Version templates by committing changes with descriptive commit messages.
4. Reload the application or redeploy to apply template updates.

Developers and operators can preview templates using `scripts/render_template.py` (not shown here) or by sending a test email in a non-production environment.
