{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"environmentName": {"value": "test"}, "enableMockDataUpload": {"value": "false"}, "logLevel": {"value": "INFO"}, "location": {"value": "west<PERSON>"}, "keyVaultName": {"value": "peepsapp-vault-test"}, "acesTags": {"value": {"environment": "test"}}, "aesDisplayName": {"value": "Peeps App Email Service - Test"}, "aesDomainName": {"value": "test.peepsapp.ai"}, "deployOpenAi": {"value": true}, "deployAzureAdSso": {"value": true}, "azureAdAppName": {"value": "Peeps App SSO Test"}, "azureAdRedirectUris": {"value": ["https://test.peepsapp.ai/auth/azure-ad/callback"]}, "azureAdLogoutUrl": {"value": "https://test.peepsapp.ai/auth/logout"}, "enableCustomDomain": {"value": true}, "customDomainName": {"value": "test.peepsapp.ai"}, "containerCertificateName": {"value": "test.peepsapp.ai-peepsapp-250524085312"}, "modelDeployments": {"value": [{"name": "gpt-4o", "model": {"name": "gpt-4o", "version": "2024-05-13", "format": "OpenAI"}, "skuName": "Standard", "capacity": 1}]}, "collections": {"value": [{"name": "communities", "partitionKeyPath": "/id"}, {"name": "people", "partitionKeyPath": "/id"}, {"name": "conversations", "partitionKeyPath": "/id"}, {"name": "events", "partitionKeyPath": "/id"}, {"name": "messages", "partitionKeyPath": "/id"}, {"name": "passkeys", "partitionKeyPath": "/person_id"}, {"name": "registration_challenges", "partitionKeyPath": "/person_id", "defaultTtl": 300}, {"name": "authentication_challenges", "partitionKeyPath": "/person_id", "defaultTtl": 300}, {"name": "invite_tokens", "partitionKeyPath": "/id", "uniqueKeys": [{"paths": ["/token"]}, {"paths": ["/email"]}]}, {"name": "invite_quotas", "partitionKeyPath": "/person_id"}, {"name": "recovery_tokens", "partitionKeyPath": "/id", "defaultTtl": 3600, "uniqueKeys": [{"paths": ["/token"]}, {"paths": ["/identifier_value"]}]}, {"name": "session_tokens", "partitionKeyPath": "/person_id", "defaultTtl": 2592000}, {"name": "email_verifications", "partitionKeyPath": "/id", "uniqueKeys": [{"paths": ["/token"]}]}, {"name": "audit_logs", "partitionKeyPath": "/id", "defaultTtl": 7776000}, {"name": "locks", "partitionKeyPath": "/type", "defaultTtl": 30, "excludedIndexPaths": [{"path": "/\"type\"/?"}]}, {"name": "posts", "partitionKeyPath": "/author_person_id", "excludedIndexPaths": [], "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"name": "comments", "partitionKeyPath": "/target_id", "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"name": "person_reactions", "partitionKeyPath": "/author_person_id", "uniqueKeys": [{"paths": ["/target_id"]}], "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"name": "reactions", "partitionKeyPath": "/target_id", "uniqueKeys": [{"paths": ["/author_person_id"]}], "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"name": "active_jobs", "partitionKeyPath": "/action"}, {"name": "feeds", "partitionKeyPath": "/person_id", "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"name": "pictures", "partitionKeyPath": "/parent_id", "compositeIndexes": [[{"path": "/parent_type", "order": "ascending"}, {"path": "/parent_id", "order": "ascending"}]]}, {"name": "notes", "partitionKeyPath": "/author_id", "defaultTtl": -1, "indexingPolicy": {"indexingMode": "consistent"}, "includedPaths": [{"path": "/object_id/?"}, {"path": "/created_at/?", "indexes": [{"dataType": "Number", "precision": -1, "kind": "Range", "order": "Descending"}]}, {"path": "/note_group_id/?"}], "excludedIndexPaths": [{"path": "/content/*"}]}]}, "fullTextCollections": {"value": [{"name": "connections", "partitionKeyPath": "/owner_person_id", "excludedIndexPaths": [], "compositeIndexes": [[{"path": "/status", "order": "ascending"}, {"path": "/requester_person_id", "order": "ascending"}], [{"path": "/status", "order": "ascending"}, {"path": "/requestee_person_id", "order": "ascending"}]], "fullTextPolicy": {"defaultLanguage": "en-US", "fullTextPaths": [{"path": "/person_preview/name", "language": "en-US"}, {"path": "/person_preview/last_name", "language": "en-US"}, {"path": "/person_preview/current_role", "language": "en-US"}, {"path": "/person_preview/current_company", "language": "en-US"}, {"path": "/person_preview/location", "language": "en-US"}]}, "fullTextIndexes": [{"path": "/person_preview/name"}, {"path": "/person_preview/last_name"}, {"path": "/person_preview/current_role"}, {"path": "/person_preview/current_company"}, {"path": "/person_preview/location"}]}]}}}