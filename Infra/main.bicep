@description('The environment name (dev, test, prod)')
param environmentName string = 'dev'

@description('Whether to upload mock data on a service start')
param enableMockDataUpload string = 'false'

@description('Log level for application logging (DEBUG, INFO, WARNING, ERROR, CRITICAL)')
param logLevel string = 'INFO'

@description('The Azure region for all resources')
param location string = resourceGroup().location

@description('The name of the Azure Container Registry')
param acrName string = 'peepsappcr${environmentName}'

@description('The name of the Azure Container App')
param containerAppName string = 'peepsapp-aca-${environmentName}'

@description('The certificate name for the custom domain')
param containerCertificateName string = ''

@description('The name of the Azure Key Vault')
param keyVaultName string = 'peepsapp-vault-${environmentName}'

@description('The name of the Azure Communication Service')
param acsName string = 'peepsapp-acs-${environmentName}'

@description('The name of the Azure Email Service')
param aesName string = 'peepsapp-aes-${environmentName}'

@description('The domain name for the sender email, e.g., "peepsapp.ai" for <EMAIL>')
param aesDomainName string

@description('The data location for the Communication and Email Service')
param acesDataLocation string = 'United States'

@description('Tags to apply to the Communication and Email Service')
param acesTags object = {}

@description('The username for sender email, e.g., "noreply" for <EMAIL>')
param aesUsername string = 'noreply'

@description('The display name for the sender email, e.g., "Peeps App Email Service" for <EMAIL>')
param aesDisplayName string

@description('The name of the Azure Cosmos DB account')
param cosmosDbAccountName string = 'peepsappcosmos${environmentName}'

@description('The name of the Cosmos DB database')
param databaseName string = 'peepsapp-cosmos-db-${environmentName}'

@description('Collections without full-text indexing')
param collections array

@description('Collections with full-text indexing')
param fullTextCollections array

@description('The name of the Storage account for pictures')
param picturesStorageAccountName string = 'peepsappblob${environmentName}'

@description('The container for original pictures')
param picturesOriginalsContainerName string = 'pictures-originals'

@description('The container for picture thumbnails')
param picturesThumbnailsContainerName string = 'pictures-thumbnails'

@description('The name of the Azure OpenAI resource')
param openAiName string = 'peepsapp-openai-${environmentName}'

@description('Whether to deploy Azure OpenAI')
param deployOpenAi bool = true

@description('The OpenAI model deployments to create')
@metadata({
  note: 'If not specified, the default values from openai.bicep will be used. Default is gpt-4o with version 2024-08-06.'
})
param modelDeployments array = []

// Note: Azure AD resources are managed separately via Azure CLI in the Makefile
// These parameters are kept for documentation purposes but not used in the Bicep deployment
@description('Whether to deploy Azure AD SSO')
param deployAzureAdSso bool = true

@description('The name of the Azure AD application')
param azureAdAppName string = 'Peeps App SSO ${environmentName}'

@description('The redirect URIs for the Azure AD application')
param azureAdRedirectUris array = []

@description('The logout URL for the Azure AD application')
param azureAdLogoutUrl string = ''

@description('Whether to enable custom domain')
param enableCustomDomain bool = false

@description('The custom domain name')
param customDomainName string = ''

// Deploy Azure Container Registry
module acr 'container-registry.bicep' = {
  name: 'acrDeployment'
  params: {
    acrName: acrName
    location: location
  }
}

// Deploy Azure Communication Service
module acs 'acs.bicep' = {
  name: 'CommunicationAndEmailServiceDeployment'
  params: {
    acsName: acsName
    aesName: aesName
    acesDataLocation: acesDataLocation
    acesTags: acesTags
    aesUsername: aesUsername
    aesDisplayName: aesDisplayName
    aesDomainName: aesDomainName
  }
}

// Deploy Azure Key Vault
module keyVault 'key-vault.bicep' = {
  name: 'keyVaultDeployment'
  params: {
    keyVaultName: keyVaultName
    location: location
  }
}

// Deploy Managed Identity
module managedIdentity 'managed-identity.bicep' = {
  name: 'managedIdentityDeployment'
  params: {
    managedIdentityName: 'peepsapp-mid-${environmentName}'
    location: location
  }
}

// Deploy Blob Storage for pictures
module picturesStorage 'azure-blob.bicep' = {
  name: 'picturesStorageDeployment'
  params: {
    storageAccountName: picturesStorageAccountName
    location: location
    originalsContainerName: picturesOriginalsContainerName
    thumbnailsContainerName: picturesThumbnailsContainerName
  }
}

// Deploy Cosmos DB Collections
module cosmosDb 'all-collections.bicep' = {
  name: 'cosmosDbDeployment'
  params: {
    cosmosDbAccountName: cosmosDbAccountName
    databaseName: databaseName
    collections: collections
    fullTextCollections: fullTextCollections
  }
}

// Deploy Azure OpenAI with models
module openAi 'openai.bicep' = if (deployOpenAi) {
  name: 'openAiDeployment'
  params: {
    openAiName: openAiName
    location: location
    modelDeployments: modelDeployments
    skuName: 'S0'
  }
}

// Deploy Container App
module containerApp 'container-app.bicep' = {
  name: 'containerAppDeployment'
  params: {
    containerAppName: containerAppName
    location: location
    acrName: acrName
    managedIdentityId: managedIdentity.outputs.managedIdentityId
    managedIdentityClientId: managedIdentity.outputs.managedIdentityClientId
    keyVaultName: keyVaultName
    environmentName: environmentName
    enableMockDataUpload: enableMockDataUpload
    logLevel: logLevel
    enableCustomDomain: enableCustomDomain
    customDomainName: customDomainName
    containerCertificateName: containerCertificateName
  }
  dependsOn: [
    acr
    keyVault
  ]
}

module secrets 'secrets.bicep' = {
  name: 'secretsDeployment'
  params: {
    keyVaultName: keyVaultName
    acsName: acsName
    aesUsername: aesUsername
    aesDomainName: aesDomainName
    blobConnectionString: picturesStorage.outputs.connectionString
  }
  dependsOn: [
    keyVault
    acs
  ]
}
