@description('The name of the Azure Cosmos DB account')
param cosmosDbAccountName string

@description('The name of the Cosmos DB database')
param databaseName string

@description('The list of all collections to deploy')
param collections array

@description('The list of all full-text collections to deploy')
param fullTextCollections array

resource cosmosDbAccount 'Microsoft.DocumentDB/databaseAccounts@2025-05-01-preview' = {
  name: cosmosDbAccountName
  location: resourceGroup().location
  properties: {
    databaseAccountOfferType: 'Standard'
    consistencyPolicy: {
      defaultConsistencyLevel: 'Session'
    }
    locations: [
      {
        locationName: resourceGroup().location
        failoverPriority: 0
        isZoneRedundant: false
      }
    ]
    capabilities: [
      {
        name: 'EnableServerless'
      }
    ]
  }
}

resource cosmosDbDatabase 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases@2023-11-15-preview' = {
  parent: cosmosDbAccount
  name: databaseName
  properties: {
    resource: {
      id: databaseName
    }
  }
}

module containerModule 'container.bicep' = [
  for (col, i) in collections: {
    name: 'container-std-${col.name}-${i}'
    params: {
      cosmosDbAccountName: cosmosDbAccountName
      databaseName: databaseName
      containerName: col.name
      partitionKeyPath: col.partitionKeyPath
      defaultTtl: contains(col, 'defaultTtl') ? col.defaultTtl ?? -1 : -1
      uniqueKeys: contains(col, 'uniqueKeys') ? col.uniqueKeys ?? [] : []
      excludedIndexPaths: contains(col, 'excludedIndexPaths') ? col.excludedIndexPaths ?? [] : []
      compositeIndexes: contains(col, 'compositeIndexes') ? col.compositeIndexes ?? [] : []
    }
    dependsOn: [
      cosmosDbDatabase
    ]
  }
]

module containerFullTextModule 'container-fulltext.bicep' = [
  for (col, i) in fullTextCollections: {
    name: 'container-ft-${col.name}-${i}'
    params: {
      cosmosDbAccountName: cosmosDbAccountName
      databaseName: databaseName
      containerName: col.name
      partitionKeyPath: col.partitionKeyPath
      defaultTtl: contains(col, 'defaultTtl') ? col.defaultTtl ?? -1 : -1
      uniqueKeys: contains(col, 'uniqueKeys') ? col.uniqueKeys ?? [] : []
      excludedIndexPaths: contains(col, 'excludedIndexPaths') ? col.excludedIndexPaths ?? [] : []
      compositeIndexes: contains(col, 'compositeIndexes') ? col.compositeIndexes ?? [] : []
      fullTextPolicy: contains(col, 'fullTextPolicy') ? col.fullTextPolicy ?? {} : {}
      fullTextIndexes: contains(col, 'fullTextIndexes') ? col.fullTextIndexes ?? [] : []
    }
    dependsOn: [
      cosmosDbDatabase
    ]
  }
]

output cosmosDbEndpoint string = 'https://${cosmosDbAccount.name}.documents.azure.com:443/'
