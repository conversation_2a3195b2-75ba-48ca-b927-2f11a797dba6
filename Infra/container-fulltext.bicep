@description('The name of the Cosmos DB account')
param cosmosDbAccountName string

@description('The name of the database')
param databaseName string

@description('The name of the container')
param containerName string

@description('The partition key path')
param partitionKeyPath string

@description('The default TTL for the container')
param defaultTtl int = -1

@description('The unique keys for the container')
param uniqueKeys array = []

@description('The excluded index paths for the container')
param excludedIndexPaths array = []

@description('The composite indexes for the container')
param compositeIndexes array = []

@description('The full-text policy for the container')
param fullTextPolicy object = {}

@description('The full-text indexes for the container')
param fullTextIndexes array = []

resource cosmosDbAccount 'Microsoft.DocumentDB/databaseAccounts@2025-05-01-preview' existing = {
  name: cosmosDbAccountName
}

resource database 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases@2025-05-01-preview' existing = {
  parent: cosmosDbAccount
  name: databaseName
}

resource container 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers@2025-05-01-preview' = {
  parent: database
  name: containerName
  properties: {
    resource: {
      id: containerName
      partitionKey: {
        paths: [partitionKeyPath]
        kind: 'Hash'
      }
      indexingPolicy: union(
        {
          indexingMode: 'consistent'
          automatic: true
          includedPaths: [
            {
              path: '/*'
            }
          ]
          excludedPaths: excludedIndexPaths
          compositeIndexes: compositeIndexes
        },
        !empty(fullTextIndexes) ? { fullTextIndexes: fullTextIndexes } : {}
      )
      defaultTtl: defaultTtl
      uniqueKeyPolicy: length(uniqueKeys) > 0
        ? {
            uniqueKeys: uniqueKeys
          }
        : null
      fullTextPolicy: !empty(fullTextPolicy) && contains(fullTextPolicy, 'fullTextPaths') ? fullTextPolicy : null
    }
  }
}
