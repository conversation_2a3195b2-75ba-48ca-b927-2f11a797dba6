@description('The name of the Azure Communication Service')
param acsName string

@description('The name of the Azure Email Service')
param aesName string

@description('The data location for the Communication and Email Service')
param acesDataLocation string

@description('Tags to apply to the Communication and Email Service')
param acesTags object = {}

@description('The username for sender email, e.g., "noreply" for <EMAIL>')
param aesUsername string

@description('The display name for the sender email, e.g., "Peeps App Email Service" for <EMAIL>')
param aesDisplayName string

@description('The domain name for the sender email, e.g., "peepsapp.ai" for <EMAIL>')
param aesDomainName string

resource azureEmailService 'Microsoft.Communication/emailServices@2023-04-01' = {
  name: aesName
  location: 'global'
  tags: acesTags
  properties: {
    dataLocation: acesDataLocation
  }
}

resource azureEmailServicesDomain 'Microsoft.Communication/emailServices/domains@2023-04-01' = {
  name: aesDomainName
  parent: azureEmailService
  location: 'global'
  properties: {
    domainManagement: 'CustomerManaged'
    userEngagementTracking: 'Disabled'
  }
}

resource emailServicesSendAddresses 'Microsoft.Communication/emailServices/domains/senderUsernames@2023-04-01' = {
  parent: azureEmailServicesDomain
  name: aesUsername
  properties: {
    username: aesUsername
    displayName: aesDisplayName
  }
}

resource communicationServices 'Microsoft.Communication/communicationServices@2023-04-01' = {
  name: acsName
  location: 'global'
  tags: acesTags
  properties: {
    dataLocation: acesDataLocation
    linkedDomains: [
      azureEmailServicesDomain.id
    ]
  }
}
