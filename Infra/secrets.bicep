param keyVaultName string
param acsName string
param aesUsername string
param aesDomainName string
param blobConnectionString string

resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' existing = {
  name: keyVaultName
}

var acsKeys = listKeys(resourceId('Microsoft.Communication/communicationServices', acsName), '2023-04-01')

resource acsConnectionStringKeyValue 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: 'ACS-CONNECTION-STRING'
  parent: keyVault
  properties: {
    value: acsKeys.primaryConnectionString
  }
}

resource acsSenderAddressValue 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: 'ACS-SENDER-ADDRESS'
  parent: keyVault
  properties: {
    value: '${aesUsername}@${aesDomainName}'
  }
}

resource blobConnectionStringSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: 'AZURE-BLOB-CONNECTION-STRING'
  parent: keyVault
  properties: {
    value: blobConnectionString
  }
}
