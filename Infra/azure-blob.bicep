@description('The name of the Azure Storage account')
param storageAccountName string

@description('The Azure region for the storage account')
param location string = resourceGroup().location

@description('The name of the originals container')
param originalsContainerName string = 'pictures-originals'

@description('The name of the thumbnails container')
param thumbnailsContainerName string = 'pictures-thumbnails'

resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' = {
  name: storageAccountName
  location: location
  sku: {
    name: 'Standard_LRS'
  }
  kind: 'StorageV2'
  properties: {
    allowBlobPublicAccess: false
    minimumTlsVersion: 'TLS1_2'
    accessTier: 'Hot'
  }
}

resource originalsContainer 'Microsoft.Storage/storageAccounts/blobServices/containers@2023-01-01' = {
  name: '${storageAccount.name}/default/${originalsContainerName}'
  properties: {
    publicAccess: 'None'
  }
}

resource thumbnailsContainer 'Microsoft.Storage/storageAccounts/blobServices/containers@2023-01-01' = {
  name: '${storageAccount.name}/default/${thumbnailsContainerName}'
  properties: {
    publicAccess: 'None'
  }
}

var accountKey = listKeys(resourceId('Microsoft.Storage/storageAccounts', storageAccount.name), '2023-01-01').keys[0].value
var connectionString = 'DefaultEndpointsProtocol=https;AccountName=${storageAccount.name};AccountKey=${accountKey};EndpointSuffix=${environment().suffixes.storage}'

output connectionString string = connectionString
