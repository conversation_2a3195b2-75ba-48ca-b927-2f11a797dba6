"""Routes for invite quota management in the admin dashboard."""

from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException

from peepsapi.auth.services import audit_logger
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.invite_service import invite_service
from peepsapi.services.cosmos_containers import (
    get_invite_quotas_container,
    get_invite_tokens_container,
    get_people_container,
)
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.decorators import handle_exceptions

router = APIRouter(tags=["invite-quota"], include_in_schema=False)


@router.post("/quota/{person_id}/set/{quota}")
@handle_exceptions(error_code_prefix="DASHBOARD")
async def set_quota(
    person_id: UUID,
    quota: int,
    auth_source: str = Depends(auth_service.get_auth_source),
    actor_id: UUID = Depends(auth_service.get_current_person),
    quota_container: CosmosContainer = Depends(get_invite_quotas_container),
    people_container: CosmosContainer = Depends(get_people_container),
):
    """Set an absolute invite quota for a user.

    Args:
        person_id: ID of the user whose quota is being set.
        quota: New quota value.
        auth_source: Authentication source for the request.
        actor_id: Authenticated admin performing the action.
        quota_container: Container storing quota records.
        people_container: Container for people records.

    Returns:
        The updated quota information.
    """
    if auth_source != "azure_ad":
        raise HTTPException(status_code=403, detail="Unauthorized")
    if quota < 0:
        raise HTTPException(
            status_code=400, detail="Quota must be a non-negative integer."
        )
    invite_service.set_quota(
        person_id, quota, actor_id, quota_container, people_container
    )
    audit_logger.log_event(
        event_type="invite_quota_set",
        user_id=actor_id,
        details={"person_id": str(person_id), "quota": quota},
    )
    return {"person_id": str(person_id), "quota": quota}


@router.post("/quota/{person_id}/increase/{increment}")
@handle_exceptions(error_code_prefix="DASHBOARD")
async def increase_quota(
    person_id: UUID,
    increment: int,
    auth_source: str = Depends(auth_service.get_auth_source),
    actor_id: UUID = Depends(auth_service.get_current_person),
    quota_container: CosmosContainer = Depends(get_invite_quotas_container),
    people_container: CosmosContainer = Depends(get_people_container),
):
    """Increase a user's invite quota by a given amount.

    Args:
        person_id: ID of the user to modify.
        increment: Amount to add to the existing quota.
        auth_source: Authentication source for the request.
        actor_id: Authenticated admin performing the action.
        quota_container: Container storing quota records.
        people_container: Container for people records.

    Returns:
        The updated quota after incrementing.
    """
    if auth_source != "azure_ad":
        raise HTTPException(status_code=403, detail="Unauthorized")
    if increment <= 0:
        raise HTTPException(
            status_code=400, detail="Increment must be a positive integer."
        )
    quota = invite_service.increase_quota(
        person_id, increment, actor_id, quota_container, people_container
    )
    audit_logger.log_event(
        event_type="invite_quota_increase",
        user_id=actor_id,
        details={"person_id": str(person_id), "increment": increment, "quota": quota},
    )
    return {"person_id": str(person_id), "quota": quota}


@router.get("/quota/{person_id}")
@handle_exceptions(error_code_prefix="DASHBOARD")
async def get_quota_stats(
    person_id: UUID,
    auth_source: str = Depends(auth_service.get_auth_source),
    invite_container: CosmosContainer = Depends(get_invite_tokens_container),
    people_container: CosmosContainer = Depends(get_people_container),
):
    """Retrieve quota and usage statistics for a user.

    Args:
        person_id: ID of the user being queried.
        auth_source: Authentication source for the request.
        invite_container: Container with invite tokens for usage info.
        people_container: Container for people records.

    Returns:
        Remaining quota and used invite count.
    """
    if auth_source != "azure_ad":
        raise HTTPException(status_code=403, detail="Unauthorized")
    remaining = invite_service.get_quota(person_id, people_container)
    used = invite_service.get_usage(person_id, invite_container)
    return {"person_id": str(person_id), "quota": remaining, "used": used}
