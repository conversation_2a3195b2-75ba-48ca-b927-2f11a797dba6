"""Authentication monitoring routes for the dashboard.

This module provides API endpoints for monitoring authentication activities,
including login attempts, success rates, authentication methods, and rate limiting.
"""

from typing import Optional

from fastapi import APIR<PERSON>er, <PERSON><PERSON>, Depends, Request
from fastapi.responses import HTMLResponse, RedirectResponse

from peepsapi.auth.services.auth_monitoring import auth_monitoring
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.azure_ad_service import (
    AZURE_AD_COOKIE_NAME,
    azure_ad_service,
)
from peepsapi.auth.services.email_verification_service import email_verification_service
from peepsapi.auth.services.invite_service import invite_service
from peepsapi.auth.services.rate_limiter import (
    get_all_rate_limiter_statistics,
    reset_all_rate_limiter_statistics,
)
from peepsapi.auth.services.recovery_service import recovery_service
from peepsapi.auth.services.token_service import token_service
from peepsapi.models import now
from peepsapi.services.cosmos_containers import (
    get_email_verifications_container,
    get_invite_tokens_container,
    get_recovery_tokens_container,
)
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import ServerError
from peepsapi.utils.error_monitoring import get_error_statistics
from peepsapi.utils.logging import get_logger
from peepsapi.utils.templates import render_template

router = APIRouter(tags=["monitoring"], include_in_schema=False)
logger = get_logger(__name__)


@router.get("/auth-stats")
@handle_exceptions(error_code_prefix="DASHBOARD")
async def get_auth_statistics(
    request: Request,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
):
    """Get authentication statistics.

    This endpoint requires authentication and returns detailed statistics
    about authentication attempts, success rates, and suspicious activities.

    Args:
        request (Request): The FastAPI request object
        session_token (Optional[str]): The JWT session token
        azure_ad_token (Optional[str]): The Azure AD session token

    Returns:
        Dict: Authentication statistics or redirect to login page
    """
    logger.info(
        "📝 Authentication statistics requested",
        request=request,
        extra={"client_ip": request.client.host if request.client else "unknown"},
    )

    # Use the centralized authentication function
    is_authenticated = await auth_service.authenticate_request(
        request=request,
        session_token=session_token,
        azure_ad_token=azure_ad_token,
    )

    # Redirect to login page if not authenticated
    if not is_authenticated:
        logger.warning(
            "⚠️ User not authenticated, redirecting to login page",
            request=request,
        )
        return RedirectResponse(url="/dashboard/login", status_code=302)

    # Get statistics
    try:
        stats = auth_monitoring.get_statistics()
        logger.info(
            "✅ Authentication statistics retrieved successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"auth_source": getattr(request.state, "auth_source", "unknown")},
        )
        return stats
    except Exception as e:
        logger.error(
            f"Error retrieving authentication statistics: {e}",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"error": str(e)},
        )
        raise ServerError(
            message="Error retrieving authentication statistics",
            error_code="STATS_RETRIEVAL_ERROR",
            details={"error": str(e)},
        )


@router.get("/auth-stats-embed", response_class=HTMLResponse)
@handle_exceptions(error_code_prefix="DASHBOARD")
async def get_auth_statistics_embed(
    request: Request,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
):
    """Get authentication statistics as an HTML page for embedding.

    This endpoint requires authentication and returns detailed statistics
    about authentication attempts, success rates, and suspicious activities
    formatted as an HTML page.

    Args:
        request (Request): The FastAPI request object
        session_token (Optional[str]): The JWT session token
        azure_ad_token (Optional[str]): The Azure AD session token

    Returns:
        HTMLResponse: The authentication statistics as an HTML page or redirect to login page
    """
    logger.info(
        "📝 Authentication statistics HTML requested",
        request=request,
        extra={"client_ip": request.client.host if request.client else "unknown"},
    )

    # Use the centralized authentication function
    is_authenticated = await auth_service.authenticate_request(
        request=request,
        session_token=session_token,
        azure_ad_token=azure_ad_token,
    )

    # Redirect to login page if not authenticated
    if not is_authenticated:
        logger.warning(
            "User not authenticated, redirecting to login page",
            request=request,
        )
        return RedirectResponse(url="/dashboard/login", status_code=302)

    try:
        # Get statistics
        stats = await auth_monitoring.get_statistics_protected(request.state.person_id)
        logger.info(
            "✅ Authentication statistics retrieved successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"auth_source": getattr(request.state, "auth_source", "unknown")},
        )

        # Prepare data for the template
        total_attempts = sum(
            stats.get("authentication", {}).get("total_attempts", {}).values()
        )
        successful_attempts = sum(
            stats.get("authentication", {}).get("successful_attempts", {}).values()
        )
        failed_attempts = sum(
            stats.get("authentication", {}).get("failed_attempts", {}).values()
        )

        overall_success_rate = 0
        if total_attempts > 0:
            overall_success_rate = (successful_attempts / total_attempts) * 100

        # Prepare method statistics
        methods = []
        for method, attempts in (
            stats.get("authentication", {}).get("total_attempts", {}).items()
        ):
            success_rate = (
                stats.get("authentication", {}).get("success_rates", {}).get(method, 0)
            )
            methods.append(
                {
                    "name": method.upper(),
                    "attempts": attempts,
                    "success_rate": round(success_rate, 1),
                }
            )

        # Prepare recent events
        recent_events = []
        for event in stats.get("authentication", {}).get("recent_events", []):
            recent_events.append(
                {
                    "timestamp": event.get("timestamp", "")
                    .replace("T", " ")
                    .split(".")[0],
                    "auth_method": event.get("auth_method", "unknown").upper(),
                    "success": event.get("success", False),
                    "identifier": event.get("identifier", "unknown"),
                    "ip_address": event.get("ip_address", "unknown"),
                }
            )

        # Prepare suspicious IPs
        suspicious_ips = stats.get("authentication", {}).get("suspicious_ips", [])

        # Create context for template rendering
        context = {
            "generated_at": stats.get("generated_at", "")
            .replace("T", " ")
            .split(".")[0],
            "total_attempts": total_attempts,
            "successful_attempts": successful_attempts,
            "failed_attempts": failed_attempts,
            "overall_success_rate": round(overall_success_rate, 1),
            "methods": methods,
            "recent_events": recent_events,
            "suspicious_ips": suspicious_ips,
        }

        # Render the template
        template_content = render_template("auth-stats.html", context)
        if not template_content:
            # Fallback if template is not found
            logger.error(
                "Authentication statistics template not found",
                request=request,
                person_id=getattr(request.state, "person_id", None),
            )
            return HTMLResponse(
                content="<h1>Authentication statistics not available</h1>"
            )

        logger.info(
            "✅ Authentication statistics HTML rendered successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
        )
        return HTMLResponse(content=template_content)
    except Exception as e:
        logger.error(
            f"Error generating authentication statistics HTML: {e}",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"error": str(e)},
        )
        raise ServerError(
            message="Error generating authentication statistics HTML",
            error_code="STATS_HTML_ERROR",
            details={"error": str(e)},
        )


@router.get("/error-stats")
@handle_exceptions(error_code_prefix="DASHBOARD")
async def get_error_statistics_endpoint(
    request: Request,
    hours: int = 24,
    include_details: bool = False,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
):
    """Get error statistics.

    This endpoint requires authentication and returns detailed statistics
    about errors in the application.

    Args:
        request (Request): The FastAPI request object
        hours (int): Number of hours to include in statistics
        include_details (bool): Whether to include error details
        session_token (Optional[str]): The JWT session token
        azure_ad_token (Optional[str]): The Azure AD session token

    Returns:
        Dict: Error statistics or redirect to login page
    """
    logger.info(
        "📝 Error statistics requested",
        request=request,
        extra={"client_ip": request.client.host if request.client else "unknown"},
    )

    # Check if the user is authenticated
    is_authenticated = False

    # Check JWT token
    if session_token:
        logger.debug("🔧 JWT token found, validating...", request=request)
        jwt_payload = token_service.validate_token(session_token)
        if jwt_payload:
            logger.info(
                "✅ JWT token valid",
                request=request,
                person_id=jwt_payload.get("sub"),
                extra={"auth_source": "jwt"},
            )
            is_authenticated = True
            # Add person ID to request state
            request.state.person_id = jwt_payload["sub"]
            request.state.override_person_id = (
                None  # For JWT auth, override_person_id is None
            )
            request.state.auth_source = "jwt"
        else:
            logger.warning("JWT token validation failed", request=request)

    # Check Azure AD token if JWT token is not valid
    if not is_authenticated and azure_ad_token:
        logger.debug("🔧 Azure AD token found, validating...", request=request)
        azure_ad_payload = azure_ad_service.validate_session_token(azure_ad_token)
        if azure_ad_payload:
            logger.info(
                "✅ Azure AD token valid",
                request=request,
                person_id=azure_ad_payload.get("sub"),
                extra={
                    "email": azure_ad_payload.get("email"),
                    "auth_source": "azure_ad",
                },
            )
            is_authenticated = True
            # Add person ID to request state
            request.state.person_id = azure_ad_payload["sub"]
            request.state.auth_source = "azure_ad"
            request.state.user_email = azure_ad_payload.get("email")
            request.state.user_name = azure_ad_payload.get("name")

            # Check if there's an x-peeps-id cookie that should override the Azure AD user ID
            person_id_cookie = request.cookies.get("x-peeps-id")
            if person_id_cookie:
                logger.info(
                    "📝 x-peeps-id cookie found, overriding person ID",
                    request=request,
                    person_id=azure_ad_payload.get("sub"),
                    extra={"override_person_id": person_id_cookie},
                )
                request.state.override_person_id = person_id_cookie
            else:
                request.state.override_person_id = None
        else:
            logger.warning("Azure AD token validation failed", request=request)

    # Redirect to login page if not authenticated
    if not is_authenticated:
        logger.warning(
            "User not authenticated, redirecting to login page",
            request=request,
        )
        return RedirectResponse(url="/dashboard/login", status_code=302)

    # Get statistics
    try:
        stats = get_error_statistics(hours=hours, include_details=include_details)
        logger.info(
            "✅ Error statistics retrieved successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"auth_source": getattr(request.state, "auth_source", "unknown")},
        )
        return stats
    except Exception as e:
        logger.error(
            f"Error retrieving error statistics: {e}",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"error": str(e)},
        )
        raise ServerError(
            message="Error retrieving error statistics",
            error_code="ERROR_STATS_RETRIEVAL_ERROR",
            details={"error": str(e)},
        )


@router.get("/rate-limit-stats")
@handle_exceptions(error_code_prefix="DASHBOARD")
async def get_rate_limit_statistics_endpoint(
    request: Request,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
):
    """Get rate limiting statistics.

    This endpoint requires authentication and returns detailed statistics
    about rate limiting, including request counts, block rates, and active blocks.

    Args:
        request (Request): The FastAPI request object
        session_token (Optional[str]): The JWT session token
        azure_ad_token (Optional[str]): The Azure AD session token

    Returns:
        Dict: Rate limiting statistics or redirect to login page
    """
    logger.info(
        "📝 Rate limiting statistics requested",
        request=request,
        extra={"client_ip": request.client.host if request.client else "unknown"},
    )

    # Use the centralized authentication function
    is_authenticated = await auth_service.authenticate_request(
        request=request,
        session_token=session_token,
        azure_ad_token=azure_ad_token,
    )

    # Redirect to login page if not authenticated
    if not is_authenticated:
        logger.warning(
            "⚠️ User not authenticated, redirecting to login page",
            request=request,
        )
        return RedirectResponse(url="/dashboard/login", status_code=302)

    # Get statistics
    try:
        stats = get_all_rate_limiter_statistics()
        logger.info(
            "✅ Rate limiting statistics retrieved successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"auth_source": getattr(request.state, "auth_source", "unknown")},
        )
        return stats
    except Exception as e:
        logger.error(
            f"Error retrieving rate limiting statistics: {e}",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"error": str(e)},
        )
        raise ServerError(
            message="Error retrieving rate limiting statistics",
            error_code="RATE_LIMIT_STATS_RETRIEVAL_ERROR",
            details={"error": str(e)},
        )


@router.post("/reset-rate-limit-stats")
@handle_exceptions(error_code_prefix="DASHBOARD")
async def reset_rate_limit_statistics_endpoint(
    request: Request,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
):
    """Reset rate limiting statistics.

    This endpoint requires authentication and resets all rate limiting statistics.
    This does not affect active blocks, only the counters.

    Args:
        request (Request): The FastAPI request object
        session_token (Optional[str]): The JWT session token
        azure_ad_token (Optional[str]): The Azure AD session token

    Returns:
        Dict: Success message or redirect to login page
    """
    logger.info(
        "🧹 Rate limiting statistics reset requested",
        request=request,
        extra={"client_ip": request.client.host if request.client else "unknown"},
    )

    # Use the centralized authentication function
    is_authenticated = await auth_service.authenticate_request(
        request=request,
        session_token=session_token,
        azure_ad_token=azure_ad_token,
    )

    # Redirect to login page if not authenticated
    if not is_authenticated:
        logger.warning(
            "⚠️ User not authenticated, redirecting to login page",
            request=request,
        )
        return RedirectResponse(url="/dashboard/login", status_code=302)

    # Reset statistics
    try:
        reset_all_rate_limiter_statistics()
        logger.info(
            "✅ Rate limiting statistics reset successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"auth_source": getattr(request.state, "auth_source", "unknown")},
        )
        return {
            "success": True,
            "message": "Rate limiting statistics reset successfully",
        }
    except Exception as e:
        logger.error(
            f"Error resetting rate limiting statistics: {e}",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"error": str(e)},
        )
        raise ServerError(
            message="Error resetting rate limiting statistics",
            error_code="RATE_LIMIT_STATS_RESET_ERROR",
            details={"error": str(e)},
        )


@router.get("/rate-limit-stats-embed", response_class=HTMLResponse)
@handle_exceptions(error_code_prefix="DASHBOARD")
async def get_rate_limit_statistics_embed(
    request: Request,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
):
    """Get rate limiting statistics as an HTML page for embedding.

    This endpoint requires authentication and returns detailed statistics
    about rate limiting formatted as an HTML page.

    Args:
        request (Request): The FastAPI request object
        session_token (Optional[str]): The JWT session token
        azure_ad_token (Optional[str]): The Azure AD session token

    Returns:
        HTMLResponse: The rate limiting statistics as an HTML page or redirect to login page
    """
    logger.info(
        "📝 Rate limiting statistics HTML requested",
        request=request,
        extra={"client_ip": request.client.host if request.client else "unknown"},
    )

    # Use the centralized authentication function
    is_authenticated = await auth_service.authenticate_request(
        request=request,
        session_token=session_token,
        azure_ad_token=azure_ad_token,
    )

    # Redirect to login page if not authenticated
    if not is_authenticated:
        logger.warning(
            "User not authenticated, redirecting to login page",
            request=request,
        )
        return RedirectResponse(url="/dashboard/login", status_code=302)

    try:
        # Get statistics
        stats = get_all_rate_limiter_statistics()
        logger.info(
            "✅ Rate limiting statistics retrieved successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"auth_source": getattr(request.state, "auth_source", "unknown")},
        )

        # Prepare data for the template
        rate_limiters = []
        for limiter in stats.get("rate_limiters", []):
            rate_limiters.append(
                {
                    "name": limiter.get("name", "unknown"),
                    "requests": limiter.get("total_requests", 0),
                    "blocked": limiter.get("blocked_requests", 0),
                    "block_rate": limiter.get("block_rate_percent", 0),
                    "active_blocks": limiter.get("active_ip_blocks", 0)
                    + limiter.get("active_user_blocks", 0),
                    "window_seconds": limiter.get("window_seconds", 0),
                    "max_requests": limiter.get("max_requests", 0),
                    "block_duration_minutes": limiter.get("block_duration_minutes", 0),
                }
            )

        # Create context for template rendering
        context = {
            "generated_at": stats.get("generated_at", "")
            .replace("T", " ")
            .split(".")[0],
            "total_requests": stats.get("total_requests", 0),
            "total_blocked_requests": stats.get("total_blocked_requests", 0),
            "overall_block_rate_percent": stats.get("overall_block_rate_percent", 0),
            "rate_limiters": rate_limiters,
            "reset_url": "/dashboard/reset-rate-limit-stats",
        }

        # Render the template
        template_content = render_template("rate-limit-stats.html", context)
        if not template_content:
            # Fallback if template is not found
            logger.error(
                "Rate limiting statistics template not found",
                request=request,
                person_id=getattr(request.state, "person_id", None),
            )
            return HTMLResponse(
                content="<h1>Rate limiting statistics not available</h1>"
            )

        logger.info(
            "✅ Rate limiting statistics HTML rendered successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
        )
        return HTMLResponse(content=template_content)
    except Exception as e:
        logger.error(
            f"Error generating rate limiting statistics HTML: {e}",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"error": str(e)},
        )
        raise ServerError(
            message="Error generating rate limiting statistics HTML",
            error_code="RATE_LIMIT_STATS_HTML_ERROR",
            details={"error": str(e)},
        )


@router.get("/error-stats-embed", response_class=HTMLResponse)
@handle_exceptions(error_code_prefix="DASHBOARD")
async def get_error_statistics_embed(
    request: Request,
    hours: int = 24,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
):
    """Get error statistics as an HTML page for embedding.

    This endpoint requires authentication and returns detailed statistics
    about errors in the application formatted as an HTML page.

    Args:
        request (Request): The FastAPI request object
        hours (int): Number of hours to include in statistics
        session_token (Optional[str]): The JWT session token
        azure_ad_token (Optional[str]): The Azure AD session token

    Returns:
        HTMLResponse: The error statistics as an HTML page or redirect to login page
    """
    logger.info(
        "📝 Error statistics HTML requested",
        request=request,
        extra={"client_ip": request.client.host if request.client else "unknown"},
    )

    # Use the centralized authentication function
    is_authenticated = await auth_service.authenticate_request(
        request=request,
        session_token=session_token,
        azure_ad_token=azure_ad_token,
    )

    # Redirect to login page if not authenticated
    if not is_authenticated:
        logger.warning(
            "User not authenticated, redirecting to login page",
            request=request,
        )
        return RedirectResponse(url="/dashboard/login", status_code=302)

    try:
        # Get statistics
        stats = get_error_statistics(hours=hours, include_details=False)
        logger.info(
            "Error statistics retrieved successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"auth_source": getattr(request.state, "auth_source", "unknown")},
        )

        # Prepare data for the template
        total_errors = stats.get("total_errors", 0)
        error_counts = stats.get("error_counts", {})
        endpoint_counts = stats.get("endpoint_counts", {})
        status_code_counts = stats.get("status_code_counts", {})

        # Prepare error types
        error_types = []
        for error_type, count in error_counts.items():
            error_types.append(
                {"name": error_type.replace("_", " ").title(), "count": count}
            )

        # Prepare top endpoints
        top_endpoints = []
        for endpoint, count in sorted(
            endpoint_counts.items(), key=lambda x: x[1], reverse=True
        )[:10]:
            top_endpoints.append({"endpoint": endpoint, "count": count})

        # Prepare status codes
        status_codes = []
        for status_code, count in sorted(
            status_code_counts.items(), key=lambda x: x[1], reverse=True
        ):
            status_codes.append({"code": status_code, "count": count})

        # Prepare recent errors
        recent_error_list = []
        for error in stats.get("recent_errors", [])[-20:]:  # Show last 20 errors
            recent_error_list.append(
                {
                    "timestamp": error.get("timestamp", "")
                    .replace("T", " ")
                    .split(".")[0],
                    "error_type": error.get("error_type", "unknown"),
                    "error_code": error.get("error_code", "unknown"),
                    "path": error.get("path", "unknown"),
                    "method": error.get("method", ""),
                }
            )

        # Format the timestamp
        formatted_timestamp = (
            stats.get("generated_at", "").replace("T", " ").split(".")[0]
        )

        # Set the selected time range
        time_range_options = {
            "time_range_6": "",
            "time_range_12": "",
            "time_range_24": "",
            "time_range_48": "",
            "time_range_168": "",
        }
        time_range_options[f"time_range_{hours}"] = "selected"

        # Create context for template rendering
        context = {
            "generated_at": formatted_timestamp,
            "total_errors": total_errors,
            "error_types": error_types,
            "top_endpoints": top_endpoints,
            "status_codes": status_codes,
            "recent_errors": recent_error_list,
            "suspicious_ips": stats.get("suspicious_ips", []),
            **time_range_options,
        }

        # Render the template
        template_content = render_template("error-stats.html", context)
        if not template_content:
            # Fallback if template is not found
            logger.error(
                "Error statistics template not found",
                request=request,
                person_id=getattr(request.state, "person_id", None),
            )
            return HTMLResponse(content="<h1>Error statistics not available</h1>")

        logger.info(
            "✅ Error statistics HTML rendered successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"auth_source": getattr(request.state, "auth_source", "unknown")},
        )
        return HTMLResponse(content=template_content)
    except Exception as e:
        logger.error(
            f"Error generating error statistics HTML: {e}",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"error": str(e)},
        )
        raise ServerError(
            message="Error generating error statistics HTML",
            error_code="ERROR_STATS_HTML_ERROR",
            details={"error": str(e)},
        )


@router.get("/email-stats-embed", response_class=HTMLResponse)
@handle_exceptions(error_code_prefix="DASHBOARD")
async def get_email_statistics_embed(
    request: Request,
    hours: int = 24,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
    invite_tokens_container=Depends(get_invite_tokens_container),
    email_verifications_container=Depends(get_email_verifications_container),
    recovery_tokens_container=Depends(get_recovery_tokens_container),
):
    """Get email statistics as an HTML page for embedding."""
    logger.info(
        "📝 Email statistics HTML requested",
        request=request,
        extra={"client_ip": request.client.host if request.client else "unknown"},
    )

    is_authenticated = await auth_service.authenticate_request(
        request=request,
        session_token=session_token,
        azure_ad_token=azure_ad_token,
    )
    if not is_authenticated:
        logger.warning(
            "User not authenticated, redirecting to login page", request=request
        )
        return RedirectResponse(url="/dashboard/login", status_code=302)

    try:
        invite_stats = invite_service.get_email_stats(invite_tokens_container, hours)
        verification_stats = email_verification_service.get_email_stats(
            email_verifications_container, hours
        )
        recovery_stats = recovery_service.get_email_stats(
            recovery_tokens_container, hours
        )

        time_range_options = {
            "time_range_6": "",
            "time_range_12": "",
            "time_range_24": "",
            "time_range_48": "",
            "time_range_168": "",
        }
        time_range_options[f"time_range_{hours}"] = "selected"

        context = {
            "generated_at": now().isoformat().replace("T", " ").split(".")[0],
            "invite": invite_stats,
            "verification": verification_stats,
            "recovery": recovery_stats,
            **time_range_options,
        }

        template_content = render_template("email-stats.html", context)
        if not template_content:
            logger.error("Email statistics template not found", request=request)
            return HTMLResponse(content="<h1>Email statistics not available</h1>")

        logger.info(
            "✅ Email statistics HTML rendered successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
        )
        return HTMLResponse(content=template_content)
    except Exception as e:
        logger.error(
            f"Error generating email statistics HTML: {e}",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"error": str(e)},
        )
        raise ServerError(
            message="Error generating email statistics HTML",
            error_code="EMAIL_STATS_HTML_ERROR",
            details={"error": str(e)},
        )
