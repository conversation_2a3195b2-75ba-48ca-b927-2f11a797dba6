from fastapi import APIRouter, Depends, HTTPException

from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.email_verification_service import email_verification_service
from peepsapi.auth.services.invite_service import invite_service
from peepsapi.auth.services.recovery_service import recovery_service
from peepsapi.services.cosmos_containers import (
    get_email_verifications_container,
    get_invite_tokens_container,
    get_recovery_tokens_container,
)
from peepsapi.services.cosmos_db import CosmosContainer

router = APIRouter(tags=["dashboard-stats"], include_in_schema=False)


@router.get("/email")
async def email_stats(
    auth_source: str = Depends(auth_service.get_auth_source),
    invite_container: CosmosContainer = Depends(get_invite_tokens_container),
    verification_container: CosmosContainer = Depends(
        get_email_verifications_container
    ),
    recovery_container: CosmosContainer = Depends(get_recovery_tokens_container),
    hours: int = 24,
):
    if auth_source != "azure_ad":
        raise HTTPException(status_code=403, detail="Unauthorized")
    return {
        "invite": invite_service.get_email_stats(invite_container, hours),
        "verification": email_verification_service.get_email_stats(
            verification_container, hours
        ),
        "recovery": recovery_service.get_email_stats(recovery_container, hours),
    }
