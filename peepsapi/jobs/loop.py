import asyncio
import os
from datetime import timedelta

from peepsapi.models.datetime import UTCDateTime
from peepsapi.models.job import Job
from peepsapi.services.cosmos_containers import get_active_jobs_container
from peepsapi.services.cosmos_db import CosmosContainerAsync
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

active_jobs_container: CosmosContainerAsync = get_active_jobs_container()
stop_loop_event = asyncio.Event()
waiting_for_reserve = None

JOBS_MAX_RESERVED = int(os.getenv("JOBS_MAX_RESERVED", 10))
JOBS_RESERVE_PERIOD_SECONDS = int(os.getenv("JOBS_RESERVE_PERIOD_SECONDS", 30))
JOBS_WAIT_BETWEEN_RESERVE_ATTEMPTS_SECONDS = int(
    os.getenv("JOBS_WAIT_BETWEEN_RESERVE_ATTEMPTS_SECONDS", 30)
)

# Active tasks executed by the job worker
jobs = set()


async def reserve_job(
    job: Job,
    active_jobs_container: Cosmos<PERSON>ontainerAsync,
) -> Job:
    # Extend the reservation period for the job
    try:
        now = UTCDateTime.now()
        extention = job.reserved_until > now

        job = await active_jobs_container.patch_model(
            item=job.id,
            partition_key=job.action,
            update_fields={
                "reserved_until": UTCDateTime.now()
                + timedelta(seconds=JOBS_RESERVE_PERIOD_SECONDS)
            },
            if_match=job.etag,
            model_class=Job,
        )  # type: ignore

        if extention:
            logger.info(
                f"🕒 | {job.id} | Reservation extended until {job.reserved_until}"
            )
        else:
            logger.info(
                f"✅ | {job.id} | Background job ({job.action}) reserved until {job.reserved_until}"
            )

        return job
    except Exception as e:
        if extention:
            logger.warning(
                f"⚠️ | {job.id} | Failed to extend reservation for {JOBS_RESERVE_PERIOD_SECONDS} seconds",
                exc_info=e,
            )
        raise


async def execute(
    job: Job,
    active_jobs_container: CosmosContainerAsync,
    container_dependencies: dict[str, CosmosContainerAsync],
    job_handlers: dict,
):
    try:
        if job.action not in job_handlers:
            logger.error(
                f"❌ | {job.id} | Background job has unknown action: {job.action}"
            )
            return

        result = await job_handlers[job.action](
            job=job,
            active_jobs_container=active_jobs_container,
            container_dependencies=container_dependencies,
        )
    except Exception as e:
        logger.error(f"❌ | {job.id} | Background job execution failed: ", exc_info=e)
        return

    if result == True:
        logger.info(f"✅ | {job.id}  | Background job finished successfully")
        await active_jobs_container.delete_item(item=job.id, partition_key=job.action)
        logger.info(f"🧹 | {job.id} | Completed background job removed")
        return

    logger.error(f"❌ | {job.id} | Background job execution failed")


async def reserve_and_start_job(
    active_jobs_container: CosmosContainerAsync,
    container_dependencies: dict[str, CosmosContainerAsync],
    job_handlers: dict,
) -> bool:
    # Collect the list of completed jobs
    completed_jobs = [job for job in jobs if job.done()]

    # Remove completed jobs from the original set
    if completed_jobs:
        for job in completed_jobs:
            jobs.remove(job)

    if len(jobs) >= JOBS_MAX_RESERVED:
        # Reached the max number of simultaneous jobs
        logger.info(
            f"📝 Reached the max number of simultaneous jobs - {JOBS_MAX_RESERVED}. Skip reservation attempt"
        )
        return False

    # Query for the next not reserved job
    result: list[Job] = await active_jobs_container.query_models(
        query="SELECT * FROM c WHERE c.reserved_until < @reserved_until OFFSET 0 LIMIT 1",
        parameters=[
            {"name": "@reserved_until", "value": UTCDateTime.now().to_iso_string()}
        ],
        model_class=Job,
    )  # type: ignore

    if len(result) > 0:
        job = result[0]
        logger.info(
            f"📝 | {job.id}  | Retrieved not reserved background job, etag: {job.etag}"
        )
    else:
        # No jobs to reserve
        logger.info(
            f"📝 No background jobs to reserve; running: {len(jobs)}; limit: {JOBS_MAX_RESERVED}"
        )
        return False

    try:
        # Try to reserve the job for JOBS_RESERVE_PERIOD_SECONDS
        job = await reserve_job(job=job, active_jobs_container=active_jobs_container)
    except Exception as e:
        logger.warning(f"⚠️ | {job.id} | Failed to reserve background job", exc_info=e)
        return False

    # Start the job execution
    jobs.add(
        asyncio.create_task(
            execute(
                job=job,
                active_jobs_container=active_jobs_container,
                container_dependencies=container_dependencies,
                job_handlers=job_handlers,
            )
        )
    )
    return True


async def start_loop(
    job_handlers: dict,
    container_dependencies: dict[str, CosmosContainerAsync],
    active_jobs_container: CosmosContainerAsync = active_jobs_container,
):
    logger.info("🎯 Starting background jobs processing loop")
    global waiting_for_reserve

    while not stop_loop_event.is_set():
        try:
            is_reserved = await reserve_and_start_job(
                active_jobs_container=active_jobs_container,
                container_dependencies=container_dependencies,
                job_handlers=job_handlers,
            )
            if is_reserved:
                continue

            if waiting_for_reserve == False:
                # An external logic indicated that there might be a new job,
                # thus, we will not wait for the next reserve attempt
                waiting_for_reserve = None
                continue

            waiting_for_reserve = asyncio.create_task(
                asyncio.sleep(JOBS_WAIT_BETWEEN_RESERVE_ATTEMPTS_SECONDS)
            )
            try:
                await waiting_for_reserve
                waiting_for_reserve = None
            except asyncio.CancelledError:
                logger.info(
                    "📝 Cancelled waiting before the next attempt to reserve a job"
                )
        except Exception as e:
            logger.error("❌ Exception occurred in the background jobs loop", exc_info=e)
            await asyncio.sleep(5)

    logger.info(
        f"🧹 Background jobs processing loop shutdown, draining running jobs: {len(jobs)}"
    )
    if jobs:
        await asyncio.gather(*jobs, return_exceptions=True)

    logger.info("🧹 Background jobs processing loop shutdown complete")


def stop_loop():
    if isinstance(waiting_for_reserve, asyncio.Task) and not waiting_for_reserve.done():
        waiting_for_reserve.cancel()

    stop_loop_event.set()


def reserve():
    global waiting_for_reserve
    if isinstance(waiting_for_reserve, asyncio.Task) and not waiting_for_reserve.done():
        waiting_for_reserve.cancel()
        return

    waiting_for_reserve = False
