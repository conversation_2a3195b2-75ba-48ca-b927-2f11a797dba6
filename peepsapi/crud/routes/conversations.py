"""API endpoints for conversation and message management.

This module provides CRUD operations for conversations and messages, including:
- Listing all conversations and messages
- Getting specific conversations and messages
- Creating new conversations and messages
- Updating existing conversations and messages
- Deleting conversations and messages
"""

from typing import List

from azure.cosmos.exceptions import CosmosResourceNotFoundError
from fastapi import APIRouter, Depends

from peepsapi.crud.models.conversation import Conversation, Message
from peepsapi.services.cosmos_containers import (
    get_conversations_container,
    get_messages_container,
)
from peepsapi.services.cosmos_db import CosmosContainerAsync
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import ResourceNotFoundError, ServerError
from peepsapi.utils.logging import get_logger

# TODO: renable when officially implemented
router = APIRouter(
    prefix="/conversations", tags=["conversations"], include_in_schema=False
)
logger = get_logger(__name__)


@router.get("/", response_model=List[Conversation])
@handle_exceptions(error_code_prefix="CONVERSATION")
async def list_conversations(
    conversations_container: CosmosContainerAsync = Depends(
        get_conversations_container
    ),
):
    """Get all conversations.

    Returns:
        List[Conversation]: A list of all conversations
    """
    logger.info("🎯 Listing all conversations")

    try:
        conversations = [
            item async for item in conversations_container.read_all_items()
        ]
        logger.info(f"✅ Found {len(conversations)} conversations")
        return conversations
    except Exception as e:
        logger.error("❌ Error listing conversations", extra={"error": str(e)})
        raise ServerError(
            message="Error listing conversations",
            error_code="LIST_ERROR",
            details={"error": str(e)},
        )


@router.get("/{conversation_id}", response_model=Conversation)
@handle_exceptions(error_code_prefix="CONVERSATION")
async def get_conversation(
    conversation_id: str,
    conversations_container: CosmosContainerAsync = Depends(
        get_conversations_container
    ),
):
    """Get a specific conversation by ID.

    Args:
        conversation_id (str): The ID of the conversation to retrieve
        conversations_container: The Cosmos DB container dependency

    Returns:
        Conversation: The requested conversation

    Raises:
        ResourceNotFoundError: If the conversation is not found
    """
    logger.info(f"🎯 Getting conversation {conversation_id}")

    try:
        conversation = await conversations_container.read_item(
            item=conversation_id, partition_key=conversation_id
        )
        return conversation
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Conversation {conversation_id} not found")
        raise ResourceNotFoundError(
            message="Conversation not found",
            error_code="CONVERSATION_NOT_FOUND",
        )


@router.post("/", response_model=Conversation)
@handle_exceptions(error_code_prefix="CONVERSATION")
async def create_conversation(
    conversation: Conversation,
    conversations_container: CosmosContainerAsync = Depends(
        get_conversations_container
    ),
):
    """Create a new conversation.

    Args:
        conversation (Conversation): The conversation to create
        conversations_container: The Cosmos DB container dependency

    Returns:
        Conversation: The created conversation
    """
    logger.info(
        f"🎯 Creating conversation {conversation.id if conversation.id else '[auto-generated]'}"
    )

    try:
        result = await conversations_container.create_model(conversation)
        logger.info(f"✅ Successfully created conversation {result['id']}")
        return conversation
    except Exception as e:
        logger.error("❌ Error creating conversation", extra={"error": str(e)})
        raise ServerError(
            message="Error creating conversation",
            error_code="CREATE_ERROR",
            details={"error": str(e)},
        )


@router.put("/{conversation_id}", response_model=Conversation)
@handle_exceptions(error_code_prefix="CONVERSATION")
async def update_conversation(
    conversation_id: str,
    conversation: Conversation,
    conversations_container: CosmosContainerAsync = Depends(
        get_conversations_container
    ),
):
    """Update an existing conversation.

    Args:
        conversation_id (str): The ID of the conversation to update
        conversation (Conversation): The updated conversation data
        conversations_container: The Cosmos DB container dependency

    Returns:
        Conversation: The updated conversation

    Raises:
        ResourceNotFoundError: If the conversation is not found
    """
    logger.info(f"🎯 Updating conversation {conversation_id}")

    try:
        # Check if conversation exists
        await conversations_container.read_item(
            item=conversation_id, partition_key=conversation_id
        )
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Conversation {conversation_id} not found for update")
        raise ResourceNotFoundError(
            message="Conversation not found",
            error_code="CONVERSATION_NOT_FOUND",
        )

    # Update the conversation
    try:
        await conversations_container.upsert_model(conversation)
        logger.info(f"✅ Successfully updated conversation {conversation_id}")
        return conversation
    except Exception as e:
        logger.error(
            f"❌ Error updating conversation {conversation_id}", extra={"error": str(e)}
        )
        raise ServerError(
            message="Error updating conversation",
            error_code="UPDATE_ERROR",
            details={"error": str(e)},
        )


@router.delete("/{conversation_id}")
@handle_exceptions(error_code_prefix="CONVERSATION")
async def delete_conversation(
    conversation_id: str,
    conversations_container: CosmosContainerAsync = Depends(
        get_conversations_container
    ),
):
    """Delete a conversation.

    Args:
        conversation_id (str): The ID of the conversation to delete
        conversations_container: The Cosmos DB container dependency

    Returns:
        dict: A confirmation message

    Raises:
        ResourceNotFoundError: If the conversation is not found
    """
    logger.info(f"🎯 Deleting conversation {conversation_id}")

    try:
        await conversations_container.delete_item(
            item=conversation_id, partition_key=conversation_id
        )
        logger.info(f"✅ Successfully deleted conversation {conversation_id}")
        return {"ok": True}
    except CosmosResourceNotFoundError:
        logger.warning(
            "Conversation not found for deletion",
            extra={"conversation_id": conversation_id},
        )
        raise ResourceNotFoundError(
            message="Conversation not found",
            error_code="CONVERSATION_NOT_FOUND",
        )


# Message endpoints


@router.get("/message/", response_model=List[Message])
@handle_exceptions(error_code_prefix="MESSAGE")
async def list_messages(
    messages_container: CosmosContainerAsync = Depends(get_messages_container),
):
    """Get all messages.

    Returns:
        List[Message]: A list of all messages
    """
    logger.info("🎯 Listing all messages")

    try:
        messages = [item async for item in messages_container.read_all_items()]
        logger.info(f"✅ Found {len(messages)} messages")
        return messages
    except Exception as e:
        logger.error("❌ Error listing messages", extra={"error": str(e)})
        raise ServerError(
            message="Error listing messages",
            error_code="LIST_ERROR",
            details={"error": str(e)},
        )


@router.get("/message/{message_id}", response_model=Message)
@handle_exceptions(error_code_prefix="MESSAGE")
async def get_message(
    message_id: str,
    messages_container: CosmosContainerAsync = Depends(get_messages_container),
):
    """Get a specific message by ID.

    Args:
        message_id (str): The ID of the message to retrieve
        messages_container: The Cosmos DB container dependency

    Returns:
        Message: The requested message

    Raises:
        ResourceNotFoundError: If the message is not found
    """
    logger.info(f"🎯 Getting message {message_id}")

    try:
        message = await messages_container.read_item(
            item=message_id, partition_key=message_id
        )
        return message
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Message {message_id} not found")
        raise ResourceNotFoundError(
            message="Message not found",
            error_code="MESSAGE_NOT_FOUND",
        )


@router.post("/message/", response_model=Message)
@handle_exceptions(error_code_prefix="MESSAGE")
async def create_message(
    message: Message,
    messages_container: CosmosContainerAsync = Depends(get_messages_container),
):
    """Create a new message.

    Args:
        message (Message): The message to create
        messages_container: The Cosmos DB container dependency

    Returns:
        Message: The created message
    """
    logger.info(
        f"🎯 Creating message {message.id if message.id else '[auto-generated]'}"
    )

    try:
        result = await messages_container.create_model(message)
        logger.info(f"✅ Successfully created message {result['id']}")
        return message
    except Exception as e:
        logger.error("❌ Error creating message", extra={"error": str(e)})
        raise ServerError(
            message="Error creating message",
            error_code="CREATE_ERROR",
            details={"error": str(e)},
        )


@router.put("/message/{message_id}", response_model=Message)
@handle_exceptions(error_code_prefix="MESSAGE")
async def update_message(
    message_id: str,
    message: Message,
    messages_container: CosmosContainerAsync = Depends(get_messages_container),
):
    """Update an existing message.

    Args:
        message_id (str): The ID of the message to update
        message (Message): The updated message data
        messages_container: The Cosmos DB container dependency

    Returns:
        Message: The updated message

    Raises:
        ResourceNotFoundError: If the message is not found
    """
    logger.info(f"🎯 Updating message {message_id}")

    try:
        # Check if message exists
        await messages_container.read_item(item=message_id, partition_key=message_id)
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Message {message_id} not found for update")
        raise ResourceNotFoundError(
            message="Message not found",
            error_code="MESSAGE_NOT_FOUND",
        )

    # Update the message
    try:
        await messages_container.upsert_model(message)
        logger.info(f"✅ Successfully updated message {message_id}")
        return message
    except Exception as e:
        logger.error(f"❌ Error updating message {message_id}", extra={"error": str(e)})
        raise ServerError(
            message="Error updating message",
            error_code="UPDATE_ERROR",
            details={"error": str(e)},
        )


@router.delete("/message/{message_id}")
@handle_exceptions(error_code_prefix="MESSAGE")
async def delete_message(
    message_id: str,
    messages_container: CosmosContainerAsync = Depends(get_messages_container),
):
    """Delete a message.

    Args:
        message_id (str): The ID of the message to delete
        messages_container: The Cosmos DB container dependency

    Returns:
        dict: A confirmation message

    Raises:
        ResourceNotFoundError: If the message is not found
    """
    logger.info(f"🎯 Deleting message {message_id}")

    try:
        await messages_container.delete_item(item=message_id, partition_key=message_id)
        logger.info(f"✅ Successfully deleted message {message_id}")
        return {"ok": True}
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Message {message_id} not found for deletion")
        raise ResourceNotFoundError(
            message="Message not found",
            error_code="MESSAGE_NOT_FOUND",
        )
