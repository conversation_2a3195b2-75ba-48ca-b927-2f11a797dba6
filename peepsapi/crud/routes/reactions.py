from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Query

from peepsapi.auth.services import auth_service
from peepsapi.crud.models.reaction import (
    Reaction,
    ReactionInput,
    ReactionsResponse,
    ReactionTargetType,
)
from peepsapi.crud.services import reaction_service
from peepsapi.crud.services.reaction_service import get_reactions_page
from peepsapi.services.cosmos_containers import (
    get_comments_container,
    get_people_container,
    get_person_reactions_container,
    get_posts_container,
    get_reactions_container,
)
from peepsapi.services.cosmos_db import CosmosContainerAsync
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/reactions", tags=["reactions"])
logger = get_logger(__name__)


@router.post("/{target_type}/{target_parent_id}/{target_id}", response_model=Reaction)
@handle_exceptions(error_code_prefix="REACTION")
async def create_reaction(
    target_type: ReactionTargetType,
    target_parent_id: UUID,
    target_id: UUID,
    reaction_input: ReactionInput,
    current_person_id: UUID = Depends(auth_service.get_current_person),
    reactions_container: CosmosContainerAsync = Depends(get_reactions_container),
    person_reactions_container: CosmosContainerAsync = Depends(
        get_person_reactions_container
    ),
    posts_container: CosmosContainerAsync = Depends(get_posts_container),
    comments_container: CosmosContainerAsync = Depends(get_comments_container),
) -> Reaction:
    """Create a new reaction."""
    if target_type == "post":
        target_container = posts_container
    elif target_type == "comment":
        target_container = comments_container
    else:
        raise ValueError(f"Unsupported target_type")

    return await reaction_service.create_reaction(
        target_id=target_id,
        target_parent_id=target_parent_id,
        reaction_input=reaction_input,
        current_person_id=current_person_id,
        reactions_container=reactions_container,
        person_reactions_container=person_reactions_container,
        target_container=target_container,
    )


@router.get("/{target_id}", response_model=ReactionsResponse)
@handle_exceptions(error_code_prefix="REACTION")
async def get_reactions(
    target_id: UUID,
    next_page: Optional[str] = None,
    type: Optional[str] = Query(None, description="Filter by reaction type"),
    reactions_container: CosmosContainerAsync = Depends(get_reactions_container),
    people_container: CosmosContainerAsync = Depends(get_people_container),
):
    """Get a page of reactions for a given target_id, optionally filtered by type."""
    where_clause = None
    where_clause_parameters = []
    if type:
        where_clause = "c.type = @type"
        where_clause_parameters = [{"name": "@type", "value": type}]

    return await get_reactions_page(
        target_id=target_id,
        next_page_token=next_page,
        reactions_container=reactions_container,
        people_container=people_container,
        where_clause=where_clause,
        where_clause_parameters=where_clause_parameters,
    )


@router.delete("/{target_type}/{target_parent_id}/{target_id}")
@handle_exceptions(error_code_prefix="REACTION")
async def delete_reaction(
    target_type: ReactionTargetType,
    target_parent_id: UUID,
    target_id: UUID,
    current_person_id: UUID = Depends(auth_service.get_current_person),
    auth_source: str = Depends(auth_service.get_auth_source),
    reactions_container: CosmosContainerAsync = Depends(get_reactions_container),
    person_reactions_container: CosmosContainerAsync = Depends(
        get_person_reactions_container
    ),
    posts_container: CosmosContainerAsync = Depends(get_posts_container),
    comments_container: CosmosContainerAsync = Depends(get_comments_container),
):
    """Delete a reaction by the current user on a post or comment."""
    if target_type == "post":
        target_container = posts_container
    elif target_type == "comment":
        target_container = comments_container
    else:
        raise ValueError(f"Unsupported target_type")

    return await reaction_service.delete_reaction(
        target_parent_id=target_parent_id,
        target_id=target_id,
        current_person_id=current_person_id,
        auth_source=auth_source,
        reactions_container=reactions_container,
        person_reactions_container=person_reactions_container,
        target_container=target_container,
    )
