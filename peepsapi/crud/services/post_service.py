import os
from typing import List, Optional
from uuid import UUID

from azure.cosmos.exceptions import CosmosResourceNotFoundError

from peepsapi.crud.models.person import Person
from peepsapi.crud.models.post import Post, PostsResponse
from peepsapi.crud.services.pagination_service import get_page
from peepsapi.crud.services.people_services import get_unique_person_previews
from peepsapi.crud.services.reaction_service import set_reactions
from peepsapi.crud.services.soft_delete_service import soft_delete_item
from peepsapi.services.cosmos_db import CosmosContainerAsync
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


async def get_posts_page(
    owner_person_id: UUID,
    next_page_token: Optional[str],
    posts_container: CosmosContainerAsync,
    people_container: CosmosContainerAsync,
    where_clause: Optional[str] = None,
    where_clause_parameters: List[dict] = [],
    skip_soft_deleted=False,
    get_person_previews: bool = True,
    fields: Optional[List[str]] = None,
    current_person_id: Optional[UUID] = None,
    person_reactions_container: Optional[CosmosContainerAsync] = None,
):
    """
    Get a page of posts for a specific person.
    """
    items, next_page = await get_page(
        partition_key=owner_person_id,
        next_page_token=next_page_token,
        model_class=Post,
        model_class_container=posts_container,
        page_size=int(os.getenv("POSTS_LIST_PAGE_SIZE", 10)),
        where_clause=where_clause,
        where_clause_parameters=where_clause_parameters,
        skip_soft_deleted=skip_soft_deleted,
        fields=fields,
    )

    # Sanitize output
    for item in items:
        # Hide original post if the post was deleted
        if item.deleted:
            item.content = ""
            item.media = []

        # Set URI used to POST or DELETE reactions
        item.reaction_uri = (
            f"/reactions/post/{str(item.author_person_id)}/{str(item.id)}"
        )

    if current_person_id and person_reactions_container:
        await set_reactions(
            items=items,
            person_id=current_person_id,
            person_reactions_container=person_reactions_container,
        )

    people = []
    if get_person_previews and people_container:
        people = await get_unique_person_previews(
            people_ids={i.author_person_id for i in items},
            people_container=people_container,
        )

    return PostsResponse(
        people=people,
        items=items,
        next_page=next_page,
    )


async def soft_delete_post(
    id: UUID,
    author_person_id: UUID,
    actor_person_id: UUID,
    actor_auth_source: str,
    posts_container: CosmosContainerAsync,
    active_jobs_container: CosmosContainerAsync,
):
    """
    Soft-delete a post by marking it as deleted and logging the action.

    Args:
        post_id (UUID): The ID of the post to be soft-deleted.
        owner_person_id (UUID): The ID of the person who owns the post.
        actor_person_id (UUID): The ID of the person performing the deletion.
        posts_container (CosmosContainer): The Cosmos DB container for posts.
        active_jobs_container (CosmosContainer): The Cosmos DB container for active jobs.

    Raises:
        ResourceNotFoundError: If the post to be deleted is not found.
    """
    await soft_delete_item(
        id=id,
        partition_key=author_person_id,
        author_person_id=author_person_id,
        actor_person_id=actor_person_id,
        actor_auth_source=actor_auth_source,
        model_class=Post,
        model_class_container=posts_container,
    )

    # NOTE: The code below is commented out to show deleted posts in the feed
    #       without content and media
    #
    # Create a job to remove the post from feeds
    # await jobs.create(
    #     action="remove_post_from_feed",
    #     params=RemovePostFromFeedParams(
    #         post_id=post_id,
    #         author_person_id=current_person_id,
    #     ),
    #     active_jobs_container=active_jobs_container,
    # )
