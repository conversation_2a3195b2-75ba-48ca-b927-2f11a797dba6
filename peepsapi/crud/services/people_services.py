"""User activity service for authentication.

This module provides functionality for tracking and querying user activity,
including login times, device usage, and session information.
"""

from typing import List, Optional, Set, Union
from uuid import UUID

from peepsapi.crud.models.base import PersonPreview
from peepsapi.crud.models.person import Person
from peepsapi.crud.services.picture_service import build_picture_url
from peepsapi.models.base import IdentifierType
from peepsapi.services.cosmos_containers import get_people_container
from peepsapi.services.cosmos_db import CosmosContainerAsync
from peepsapi.utils.error_handling import ServerError
from peepsapi.utils.logging import get_caller_context, get_logger

# Configure logger
logger = get_logger(__name__)


class PeopleService:
    """Service for People shared methods."""

    async def create_person(
        self, person: Person, people_container: CosmosContainerAsync
    ) -> Person:
        """
        Create a new person.

        Args:
            person (Person): The person to create

        Returns:
            Person: The created person
        """
        logger.info(f"🎯 Creating person {person.id}")
        try:
            await people_container.create_model(person)
            logger.info(f"✅ Successfully created person {person.id}")
            return person
        except Exception as e:
            logger.error(
                f"❌ Error creating person {person.id}", extra={"error": str(e)}
            )
            raise ServerError(
                message="Error creating person",
                error_code="CREATE_ERROR",
                details={"error": str(e)},
            )

    async def get_people_by_identifier(
        self,
        identifier_type: IdentifierType,
        identifier_value: str,
        people_container: CosmosContainerAsync,
    ) -> List[Person]:
        """
        Get a person by their identifier.

        Args:
            identifier_type (IdentifierType): The type of identifier
            identifier_value (str): The identifier value

        Returns:
            List[Person]: The person if found, None otherwise
        """
        caller = get_caller_context()
        # Find user by identifier
        identifier_field = (
            "emails" if identifier_type == IdentifierType.EMAIL else "phone_numbers"
        )
        value_key = "address" if identifier_type == IdentifierType.EMAIL else "number"
        query = f"SELECT * FROM c WHERE ARRAY_CONTAINS(c.{identifier_field}, {{ '{value_key}': @value }}, true)"
        params = [{"name": "@value", "value": identifier_value}]
        # Execute the query
        try:
            people: List[Person] = await people_container.query_models(
                query=query, parameters=params, model_class=Person
            )
        except Exception as query_err:
            logger.error(
                f"❌ Failed to execute query: {query} and params: {params} - error: {query_err}",
                extra={"caller": caller},
            )
            return []

        # Log results for debugging
        if not people:
            logger.debug(
                f"📝 {caller} - No person found with {identifier_type}={identifier_value}",
                extra={"caller": caller},
            )
            return []

        return people


# Create a global instance of the user activity service
people_service = PeopleService()


def create_person_preview(person: Person) -> PersonPreview:
    """Create a PersonPreview object from a Person object.

    Args:
        person (Person): The Person object to convert.

    Returns:
        PersonPreview: A preview of the person containing limited fields.
    """
    return PersonPreview(
        id=person.id,
        name=person.name,
        last_name=person.last_name,
        current_role=person.current_role,
        current_company=person.current_company,
        location=person.location,
        profile_pic_thumb=build_picture_url(person),
    )


async def get_unique_person_previews(
    people_ids: Union[Set[UUID], List[UUID]],
    people_container: CosmosContainerAsync,
) -> List[PersonPreview]:
    """
    Fetch unique PersonPreview objects for a given set of person IDs.

    Args:
        people_ids (Union[Set[UUID], List[UUID]]): A set or list of person UUIDs.
        people_container (CosmosContainer): The Cosmos DB container for people.

    Returns:
        List[PersonPreview]: A list of unique PersonPreview objects corresponding to the provided IDs.
    """
    # Extract unique author_person_ids from the posts
    unique_person_ids = {person_id for person_id in people_ids}

    # Fetch all unique persons from the people_container
    person_previews = []
    for person_id in unique_person_ids:
        person = await people_container.read_model(
            id=person_id, partition_key=person_id, model_class=Person
        )  # type: ignore
        person_previews.append(create_person_preview(person))
    return person_previews
