import os
from typing import List, Optional
from uuid import UUID

from azure.cosmos.exceptions import CosmosHttpResponseError

from peepsapi.crud.models.feed import Feed
from peepsapi.crud.models.person import Person
from peepsapi.crud.models.post import Post, PostsResponse
from peepsapi.crud.services.post_service import get_posts_page
from peepsapi.models.datetime import UTCDateTime
from peepsapi.services.cosmos_db import CosmosContainerAsync
from peepsapi.utils.logging import get_logger
from peepsapi.utils.pagination import (
    chronological_pagination_build_query,
    chronological_pagination_encode_next_page_token,
)

logger = get_logger(__name__)


async def add_post_to_feed(
    feed_owner_person_id: UUID,
    post_id: UUID,
    author_person_id: UUID,
    created_at: UTCDateTime,
    feeds_container: CosmosContainerAsync,
    people_container: CosmosContainerAsync,
) -> int:
    """
    Add a post to the feed for a person.
    """
    feed_model = Feed(
        id=post_id,
        person_id=feed_owner_person_id,
        author_person_id=author_person_id,
        created_at=created_at,
    )

    try:
        logger.info(
            f"📥 Adding post {post_id} to feed for person {feed_owner_person_id}",
            extra={
                "feed_owner_person_id": feed_owner_person_id,
                "post_id": post_id,
                "author_person_id": author_person_id,
            },
        )
        await feeds_container.create_model(feed_model, model_class=Feed)

        # Increment the "feed_size" field for the person
        updated_item: Person = await people_container.patch_model(
            item=feed_owner_person_id,
            partition_key=feed_owner_person_id,
            patch_operations=[{"op": "incr", "path": "/feed_size", "value": 1}],
            model_class=Person,
        )  # type: ignore
        new_feed_size: int = updated_item.feed_size
        logger.info(
            f"✅ Post {post_id} added to feed for person {feed_owner_person_id}",
            extra={
                "feed_owner_person_id": feed_owner_person_id,
                "post_id": post_id,
                "new_feed_size": new_feed_size,
            },
        )
        return new_feed_size
    except CosmosHttpResponseError as e:
        if e.status_code == 409:  # Conflict
            logger.warning(
                f"⚠️ Post {post_id} already in the feed for person {feed_owner_person_id}",
                extra={"person_id": feed_owner_person_id, "post_id": post_id},
            )
            return 0
        else:
            logger.error(
                f"❌ Error adding post {post_id} to feed for person {feed_owner_person_id}: {e}",
                extra={
                    "person_id": feed_owner_person_id,
                    "post_id": post_id,
                    "error": str(e),
                },
                exc_info=True,
            )
            raise


async def remove_post_from_feed(
    feed_owner_person_id: UUID,
    post_id: UUID,
    feeds_container: CosmosContainerAsync,
    people_container: CosmosContainerAsync,
) -> int:
    """
    Remove a post from the feed for a person.
    """
    try:
        logger.info(
            f"🧹 Removing post {post_id} from feed for person {feed_owner_person_id}",
            extra={"feed_owner_person_id": feed_owner_person_id, "post_id": post_id},
        )
        # Delete the post from the feed
        await feeds_container.delete_item(
            item=post_id, partition_key=feed_owner_person_id
        )

        # Decrement the "feed_size" field for the person
        updated_item: Person = await people_container.patch_model(
            item=feed_owner_person_id,
            partition_key=feed_owner_person_id,
            patch_operations=[{"op": "incr", "path": "/feed_size", "value": -1}],
            model_class=Person,
        )  # type: ignore
        new_feed_size: int = updated_item.feed_size
        logger.info(
            f"✅ Post {post_id} removed from feed for person {feed_owner_person_id}",
            extra={
                "feed_owner_person_id": feed_owner_person_id,
                "post_id": post_id,
                "new_feed_size": new_feed_size,
            },
        )
        return new_feed_size
    except CosmosHttpResponseError as e:
        if e.status_code == 404:  # Not Found
            logger.warning(
                f"⚠️ Post {post_id} not found in the feed for person {feed_owner_person_id}",
                extra={
                    "feed_owner_person_id": feed_owner_person_id,
                    "post_id": post_id,
                },
            )
            return 0
        else:
            logger.error(
                f"❌ Error removing post {post_id} from feed for person {feed_owner_person_id}: {e}",
                extra={
                    "feed_owner_person_id": feed_owner_person_id,
                    "post_id": post_id,
                    "error": str(e),
                },
                exc_info=True,
            )
            raise


async def get_feed_page(
    owner_person_id: UUID,
    next_page_token: Optional[str],
    feeds_container: CosmosContainerAsync,
    posts_container: Optional[CosmosContainerAsync] = None,
    people_container: Optional[CosmosContainerAsync] = None,
    person_reactions_container: Optional[CosmosContainerAsync] = None,
    get_posts: bool = True,
    where_clause: Optional[str] = None,
    where_clause_parameters: List[dict] = [],
    current_person_id: Optional[UUID] = None,
) -> PostsResponse:
    page_size = int(os.getenv("POSTS_LIST_PAGE_SIZE", 10))

    # Build query
    query, parameters = chronological_pagination_build_query(
        page_size=page_size,
        next_page_token=next_page_token,
        order_field="created_at",
        where_clause=where_clause,
        where_clause_parameters=where_clause_parameters,
    )

    # Execute query
    feed_items: list = await feeds_container.query_models(
        query=query,
        parameters=parameters,
        model_class=Feed,
        partition_key=owner_person_id,
    )  # type: ignore

    # Prepare next_page_token
    if len(feed_items) == page_size:
        last = feed_items[-1]
        next_page_token = chronological_pagination_encode_next_page_token(
            last.created_at, last.id
        )
    else:
        next_page_token = None

    if not get_posts:
        # Minimal Post instances from feed_items only
        minimal_posts = [
            Post(
                id=item.id,
                author_person_id=item.author_person_id,
                created_at=item.created_at,
            )
            for item in feed_items
        ]
        return PostsResponse(
            people=[],
            items=minimal_posts,
            next_page=next_page_token,
        )

    if posts_container is None or people_container is None:
        raise ValueError("Both posts_container and people_container must be provided.")

    from collections import defaultdict

    author_to_post_ids = defaultdict(list)
    for item in feed_items:
        author_to_post_ids[item.author_person_id].append(item.id)
    all_post_ids = [item.id for item in feed_items]

    if not all_post_ids:
        return PostsResponse(people=[], items=[], next_page=next_page_token)

    # Fetch posts for each author_person_id
    all_posts = []
    all_people = []
    for author_id, post_ids in author_to_post_ids.items():
        where_clause = (
            f"c.id IN ({', '.join(['@id'+str(i) for i in range(len(post_ids))])})"
        )
        where_clause_parameters = [
            {"name": f"@id{i}", "value": str(post_id)}
            for i, post_id in enumerate(post_ids)
        ]
        try:
            posts_response = await get_posts_page(
                owner_person_id=author_id,
                next_page_token=None,  # Pagination is handled by feed_items
                posts_container=posts_container,
                people_container=people_container,
                where_clause=where_clause,
                where_clause_parameters=where_clause_parameters,
                skip_soft_deleted=False,
                get_person_previews=True,
                current_person_id=current_person_id,
                person_reactions_container=person_reactions_container,
            )
        except Exception as e:
            logger.error("Error fetching posts page", exc_info=True)
            raise
        all_posts.extend(posts_response.items)
        all_people.extend(posts_response.people)

    # Remove duplicate people
    unique_people = {person.id: person for person in all_people}.values()

    # Sort posts chronologically based on feed_items[].created_at order
    post_id_to_post = {post.id: post for post in all_posts}
    sorted_posts = [
        post_id_to_post[feed_id]
        for feed_id in all_post_ids
        if feed_id in post_id_to_post
    ]

    return PostsResponse(
        people=list(unique_people),
        items=sorted_posts,
        next_page=next_page_token,
    )
