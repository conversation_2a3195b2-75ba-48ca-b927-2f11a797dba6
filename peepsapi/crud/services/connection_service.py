"""
Connection service for managing social network connections between people.

Provides functionality for connection status management, querying, locking,
and integration with Azure Cosmos DB for persistent storage.
"""

import traceback
import uuid
from os import environ
from typing import Any, List, Optional, Tuple
from uuid import UUID

from azure.cosmos import ContainerProxy
from azure.cosmos.exceptions import CosmosHttpResponseError, CosmosResourceNotFoundError
from fastapi import Depends, HTTPException

from peepsapi.crud.models.base import PersonPreview
from peepsapi.crud.models.connection import (
    Connection,
    ConnectionsResponse,
    ConnectionStatus,
)
from peepsapi.crud.models.person import Person
from peepsapi.crud.services.people_services import create_person_preview
from peepsapi.crud.services.picture_service import build_picture_url
from peepsapi.crud.utils.constants import CONNECTION_NAMESPACE
from peepsapi.models.job import (
    AddEachOthersPostsToFeedsParams,
    RemoveEachOthersPostsFromFeedsParams,
)
from peepsapi.models.lock import Lock, LockType
from peepsapi.services.cosmos_containers import get_connections_container
from peepsapi.services.cosmos_db import CosmosContainerAsync
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


async def get_active_connection_person_ids(
    owner_person_id: UUID,
    connections_container: CosmosContainerAsync,
) -> List[UUID]:
    """
    Retrieve all active connection person IDs for a given owner_person_id.
    """
    query = "SELECT c.person_preview.id FROM c WHERE c.status = @status"
    parameters = [{"name": "@status", "value": ConnectionStatus.ACCEPTED}]
    results = [
        item
        async for item in connections_container.query_items(
            query=query, parameters=parameters, partition_key=owner_person_id
        )
    ]
    return [UUID(item["id"]) for item in results]


def gen_connection_id(
    requester_person_id: UUID,
    requestee_person_id: UUID,
) -> UUID:
    """
    Generate a unique connection ID based on the requester and requestee person IDs.

    Args:
        requester_person_id (UUID): The ID of the requester person.
        requestee_person_id (UUID): The ID of the requestee person.

    Returns:
        UUID: Connection ID in the format "<person_id1>-<person_id2>" sorted in ascending order.
    """
    connection_str = "-".join(
        sorted([str(requester_person_id), str(requestee_person_id)])
    )
    return uuid.uuid5(CONNECTION_NAMESPACE, connection_str)


async def update_person_connection_preview(
    person: Person,
    connections_container: CosmosContainerAsync,
):
    """
    Update the connection preview for a person.

    This function updates the connection preview for all connections associated
    with a given person. It retrieves all visible connections for the person,
    creates a connection preview from the person's data, and updates the
    connections with the new preview.

    Args:
        person (Person): The person object containing the updated data.
        connections_container (CosmosContainerAsync): The Cosmos DB container dependency for connections.

    Returns:
        None
    """
    try:
        logger.debug(
            f"🔧 Updating connection previews for person {person.id} visible connections",
            extra={"person_id": str(person.id)},
        )
        # Find all visible connections for the person
        query = (
            "SELECT c.person_preview.id FROM c WHERE c.status IN (@status1, @status2)"
        )
        parameters = [
            {"name": "@status1", "value": ConnectionStatus.REQUESTED},
            {"name": "@status2", "value": ConnectionStatus.ACCEPTED},
        ]
        results = [
            item
            async for item in connections_container.query_items(
                query=query, parameters=parameters, partition_key=person.id
            )
        ]
        connection_person_ids = [item["id"] for item in results]

        if connection_person_ids:
            logger.debug(
                f"🔧 Found {len(connection_person_ids)} connections to update for person {person.id}",
                extra={
                    "person_id": str(person.id),
                    "connection_person_ids": connection_person_ids,
                },
            )
        else:
            logger.info(
                f"📝 No connections to update for person {person.id}",
                extra={"person_id": str(person.id)},
            )
            return

        # Create ConnectionPreview from the person object
        connection_preview = create_person_preview(person).model_dump(mode="json")

        logger.debug(
            f"🔧 Generated connection preview for person {person.id}",
            extra={
                "person_id": str(person.id),
                "connection_preview": connection_preview,
            },
        )

        # Update all connections with the connection preview
        for connection_person_id in connection_person_ids:
            connection_id = gen_connection_id(person.id, connection_person_id)
            try:
                await connections_container.patch_model(
                    item=connection_id,
                    partition_key=connection_person_id,
                    update_fields={"person_preview": connection_preview},
                    model_class=Connection,
                )
            except Exception as e:
                logger.error(
                    f"❌ Failed to update connection preview for connection {connection_id}: {e}",
                    extra={
                        "person_id": str(person.id),
                        "connection_person_id": str(connection_person_id),
                        "connection_id": str(connection_id),
                        "error": str(e),
                    },
                    exc_info=True,
                )

        logger.info(
            f"✅ Updated {len(connection_person_ids)} connection previews for connections of {person.id}",
            extra={"person_id": str(person.id)},
        )
    except Exception as e:
        logger.error(
            f"❌ Error in update_person_connection_preview for person {person.id}: {e}",
            extra={"person_id": str(person.id), "error": str(e)},
            exc_info=True,
        )
        raise


async def get_connection_status_info(
    person_id_1: UUID,
    person_id_2: UUID,
    connections_container: CosmosContainerAsync,
) -> Tuple[Optional[ConnectionStatus], Optional[bool]]:
    """
    Retrieve the connection status information between two people.

    This function returns the status of a connection between two people and its direction.
    If no connection exists, it returns [None, None].

    Args:
        person_id_1 (UUID): The ID of the first person.
        person_id_2 (UUID): The ID of the second person.
        connections_container (CosmosContainerAsync): The Cosmos DB container dependency for connections.

    Returns:
        Tuple[ConnectionStatus, bool]: A tuple containing a connection status and a boolean
                                        indicating if the first person is the requester, or [None, None]
                                        if no connection exists.
    """
    if person_id_1 == person_id_2:
        return None, None

    id = gen_connection_id(person_id_1, person_id_2)
    result = [
        item
        async for item in connections_container.query_items(
            query="SELECT c.status, c.requester_person_id FROM c WHERE c.id = @id",
            parameters=[{"name": "@id", "value": id}],
            partition_key=person_id_1,
        )
    ]
    if not len(result):
        return None, None

    logger.info(f"connection status info {result}")

    logger.debug(
        f"Connection status of {person_id_2} and {person_id_1}: {result[0]['status']}"
    )

    return (
        result[0]["status"],
        result[0]["requester_person_id"] == str(person_id_1),
    )


async def get_connections_page(
    owner_person_id: UUID,
    sub_query: str,
    sub_query_parameters: List[dict],
    next_page_token: Optional[str],
    connections_container: CosmosContainerAsync,
) -> ConnectionsResponse:
    """
    Get connections for a specific person.

    Args:
    - owner_person_id (str): The ID of the person whose connections are being retrieved.
    - sub_query (str): The sub-query to filter connections.
    - sub_query_parameters (List[dict]): The parameters for the sub-query.
    - next_page_token (str): The token for fetching the next page of results.
    - connections_container: The Cosmos DB container dependency for connections.

    Returns:
    - dict: A dictionary containing the list of connections and the next page token.
    """
    logger.debug(f"📝 Getting connections for {owner_person_id}")
    limit = int(environ.get("CONNECTION_LIST_PAGE_SIZE", 100))
    parameters = [{"name": "@limit", "value": limit}]
    pagination_query = ""
    if next_page_token:
        pagination_query = "AND c._rid > @next_page"
        parameters.append({"name": "@next_page", "value": next_page_token})
    query = (
        f"SELECT * FROM c WHERE {sub_query} {pagination_query} OFFSET 0 LIMIT @limit"
    )
    parameters.extend(sub_query_parameters)

    items = [
        item
        async for item in connections_container.query_items(
            query=query, parameters=parameters, partition_key=owner_person_id
        )
    ]
    next_page = items[-1]["_rid"] if len(items) >= limit else None
    connections = [Connection(**item) for item in items]
    return ConnectionsResponse(
        items=connections,
        next_page=next_page,
    )


async def lock_connection(
    id: UUID,
    locks_container: CosmosContainerAsync,
):
    """
    Acquire a lock on a connection.

    This function attempts to create a lock for a connection in the locks container.
    The lock ensures that no other operation can modify the connection while it is locked.

    Args:
        id (str): The unique identifier for the connection to lock.
        locks_container (CosmosContainerAsync): The Cosmos DB container dependency for locks.

    Returns:
        dict: The lock object if successfully created.

    Raises:
        HTTPException:
            - 423: If the connection is already locked.
    """
    try:
        lock = Lock(
            id=id,
            type=LockType.CONNECTION,
            ttl=15,
        )

        res = await locks_container.create_item(
            dict(
                id=str(lock.id),
                type=lock.type.value,
                ttl=lock.ttl,
            )
        )
        lock.etag = res["_etag"]  # TODO: figure this out
        return lock
    except CosmosHttpResponseError as e:
        if e.status_code == 409:
            # Item already locked
            raise HTTPException(status_code=423, detail="Connection is locked")

        raise e


async def unlock_connection(
    lock: Lock,
    locks_container: CosmosContainerAsync,
):
    """Unlock the connection by removing it from the lock container."""
    try:
        await locks_container.delete_item(
            item=lock.id, partition_key=lock.type.value, if_match=lock.etag
        )

    except CosmosHttpResponseError as e:
        # Log the exception
        print(f"Error unlocking connection: {id}: {str(e)}")
    except Exception as e:  # Catch any other exceptions
        print(f"An unexpected error occurred: {id}: {e}")


async def get_connection_person_preview_by_person_id(
    person_id: UUID,
    people_container: CosmosContainerAsync,
) -> PersonPreview:
    """
    Retrieve a preview of a person's connection profile.

    This function fetches a person's data from the database and converts it into
    a `PersonPreview` object, which contains a subset of the person's details
    relevant for connection purposes.

    Args:
        person_id (str): The ID of the person whose connection preview is being retrieved.
        people_container (CosmosContainerAsync): The Cosmos DB container dependency for people.

    Returns:
        PersonPreview: A preview of the person's connection profile.

    Raises:
        HTTPException: If the person is not found (404).
    """
    try:
        # Create the requester connection profile
        person = await people_container.read_model(
            id=person_id, partition_key=person_id, model_class=Person
        )
        return create_person_preview(person)
    except CosmosResourceNotFoundError:
        raise HTTPException(
            status_code=404, detail="Requester or requestee person not found"
        )


async def update_connection_status(
    id: UUID,
    actor_person_id: UUID,
    new_status: ConnectionStatus,
    locks_container: CosmosContainerAsync,
    connections_container: CosmosContainerAsync,
    people_container: CosmosContainerAsync,
    active_jobs_container: Optional[CosmosContainerAsync] = None,
    skip_lock: bool = False,
    create_job_method=None,
):
    """
    Update the status of a connection.

    This function handles the logic for updating the status of a connection
    between two people. It ensures that the status transition is valid and
    updates the connection in the database.

    Args:
        id (UUID): The unique identifier for the connection.
        actor_person_id (UUID): The ID of the person initiating the status update.
        new_status (ConnectionStatus): The new status to update the connection to.
        locks_container (CosmosContainerAsync): The Cosmos DB container dependency for connection locks.
        connections_container (CosmosContainerAsync): The Cosmos DB container dependency for connections.
        people_container (CosmosContainerAsync): The Cosmos DB container dependency for people.
        skip_lock (bool): Whether to skip acquiring a lock on the connection.

    Raises:
        HTTPException:
            - 400: If the status transition is invalid.
            - 404: If the connection or the person is not found.
            - 423: If the connection is locked.

    Returns:
        None
    """
    # Aquire a lock on the connection
    if not skip_lock:
        lock = await lock_connection(id, locks_container)

    try:
        # Get connection
        try:
            connnection = await connections_container.read_item(
                item=id, partition_key=actor_person_id
            )
            connection = Connection(**connnection)
        except CosmosResourceNotFoundError:
            raise HTTPException(status_code=404, detail="Connection not found")

        requester_person_id = connection.requester_person_id
        requestee_person_id = connection.requestee_person_id

        # Validate status transitions
        current_status = connection.status
        if (
            current_status == ConnectionStatus.REQUESTED
            and new_status in [ConnectionStatus.ACCEPTED, ConnectionStatus.REJECTED]
            and actor_person_id == requestee_person_id
        ):
            pass
        elif (
            current_status == ConnectionStatus.REJECTED
            and new_status == ConnectionStatus.REQUESTED
            and actor_person_id == requestee_person_id
        ):
            pass
        elif (
            current_status == ConnectionStatus.REQUESTED
            and new_status == ConnectionStatus.RESCINDED
            and actor_person_id == requester_person_id
        ):
            pass
        elif (
            current_status == ConnectionStatus.ACCEPTED
            and new_status == ConnectionStatus.REMOVED
        ):
            pass
        elif (
            current_status in [ConnectionStatus.REMOVED, ConnectionStatus.RESCINDED]
            and new_status == ConnectionStatus.REQUESTED
        ):
            pass
        else:
            raise HTTPException(status_code=400, detail="Invalid status transition")

        # Prepare update object
        update_fields: dict[str, Any] = {"status": new_status}
        update_fields_requester = {}
        update_fields_requestee = {}

        if new_status == ConnectionStatus.REQUESTED:
            requestee_person_id = (
                requestee_person_id
                if actor_person_id == requester_person_id
                else requester_person_id
            )
            requester_person_id = actor_person_id

            update_fields["requester_person_id"] = requester_person_id
            update_fields["requestee_person_id"] = requestee_person_id

            update_fields_requester["person_preview"] = (
                await get_connection_person_preview_by_person_id(
                    requestee_person_id, people_container
                )
            ).model_dump(mode="json")
            update_fields_requestee["person_preview"] = (
                await get_connection_person_preview_by_person_id(
                    requester_person_id, people_container
                )
            ).model_dump(mode="json")

        # Update the requester connection
        await connections_container.patch_model(
            item=id,
            partition_key=requester_person_id,
            update_fields=update_fields | update_fields_requester,
            model_class=Connection,
        )

        # Update the requestee connection
        await connections_container.patch_model(
            item=id,
            partition_key=requestee_person_id,
            update_fields=update_fields | update_fields_requestee,
            model_class=Connection,
        )

        # If removing an accepted connection, schedule a job to remove each other's posts from feeds
        if current_status == ConnectionStatus.ACCEPTED:
            if not create_job_method:
                logger.warning("⚠️create_job_method was expected but not provided")
            else:
                await create_job_method(
                    action="remove_each_others_posts_from_feeds",
                    params=RemoveEachOthersPostsFromFeedsParams(
                        person_id_1=requester_person_id,
                        person_id_2=requestee_person_id,
                    ),
                    active_jobs_container=active_jobs_container,
                )

        # If accepting a connection, schedule a job to add each other's posts to feeds
        if new_status == ConnectionStatus.ACCEPTED:
            if not create_job_method:
                logger.warning("⚠️create_job_method was expected but not provided")
            else:
                await create_job_method(
                    action="add_each_others_posts_to_feeds",
                    params=AddEachOthersPostsToFeedsParams(
                        person_id_1=requester_person_id,
                        person_id_2=requestee_person_id,
                    ),
                    active_jobs_container=active_jobs_container,
                )
    finally:
        # Release the lock on the connection
        if not skip_lock:
            await unlock_connection(lock, locks_container)


class ConnectionService:
    """Service for Connection shared methods."""


# Create a global instance of the user activity service
connection_service = ConnectionService()


# @router.get("/query", response_model=List[Connection])
async def query_connections(
    person_id: UUID,  # connection owner person ID
    query: Optional[str] = None,
    name: Optional[str] = None,
    last_name: Optional[str] = None,
    current_role: Optional[str] = None,
    current_company: Optional[str] = None,
    location: Optional[str] = None,
    limit: int = 1,
    connections_container: CosmosContainerAsync = get_connections_container(),
):
    """
    Query connections with full-text search filtering across multiple person fields.

    This function supports two search modes:
    1. Multi-field full-text search using the 'query' parameter (searches across all fields)
    2. Individual field searches using specific field parameters

    Search types are controlled by the QUERY_FUZZY_SEARCH_ENABLED environment variable:
    - When "false": Basic full-text search using FullTextContains (default)
    - When "true": Fuzzy search using FullTextContains with distance tolerance for typos

    When both query and specific field parameters are provided, they are combined with AND logic.
    If only specific field parameters are provided, only those fields are searched.
    If only query parameter is provided, all fields are searched using full-text search.

    Args:
        person_id (str): The ID of the person whose connections are being queried
        query (Optional[str]): Query string for full-text search across all person_preview fields
        name (Optional[str]): Search specifically in the name field
        last_name (Optional[str]): Search specifically in the last_name field
        current_role (Optional[str]): Search specifically in the current_role field
        current_company (Optional[str]): Search specifically in the current_company field
        location (Optional[str]): Search specifically in the location field
        connections_container: The Cosmos DB container dependency for connections

    Returns:
        dict: A dictionary containing items list of connections that match the search criteria
    """
    try:
        # Get search type from environment variable
        fuzzy_search_enabled = environ.get(
            "QUERY_FUZZY_SEARCH_ENABLED", "false"
        ).lower()

        # Build search parameters info for logging
        search_params = {
            "query": query,
            "name": name,
            "last_name": last_name,
            "current_role": current_role,
            "current_company": current_company,
            "location": location,
        }
        active_params = {k: v for k, v in search_params.items() if v is not None}

        if not active_params:
            logger.warning(
                f"No search parameters provided for person with ID {person_id}"
            )
            return {"people": []}

        search_type_info = (
            "fuzzy" if fuzzy_search_enabled == "true" else "exact case-insensitive"
        )

        parameters = [{"name": "@status", "value": ConnectionStatus.ACCEPTED.value}]

        # Build search conditions based on search type
        search_conditions = []

        # Add multi-field full-text search if query parameter is provided
        if query:
            search_query = query.strip()
            parameters.append({"name": "@search_query", "value": search_query})

            # Choose search format based on fuzzy search setting
            search_query_str = (
                "@search_query"
                if fuzzy_search_enabled != "true"
                else '{"term": @search_query, "distance": 2}'
            )

            multi_field_condition = f"""(
                FullTextContains(c.person_preview.name, {search_query_str})
                OR FullTextContains(c.person_preview.last_name, {search_query_str})
                OR FullTextContains(c.person_preview.current_role, {search_query_str})
                OR FullTextContains(c.person_preview.current_company, {search_query_str})
                OR FullTextContains(c.person_preview.location, {search_query_str})
            )"""
            search_conditions.append(multi_field_condition)

        # Add individual field searches if specific parameters are provided
        field_mappings = {
            "name": ("@name_search", "c.person_preview.name"),
            "last_name": ("@last_name_search", "c.person_preview.last_name"),
            "current_role": ("@role_search", "c.person_preview.current_role"),
            "current_company": ("@company_search", "c.person_preview.current_company"),
            "location": ("@location_search", "c.person_preview.location"),
        }

        for field_name, (param_name, field_path) in field_mappings.items():
            field_value = search_params.get(field_name)
            if field_value:
                field_search = field_value.strip()
                parameters.append({"name": param_name, "value": field_search})

                # Choose search format based on fuzzy search setting
                search_param_str = (
                    param_name
                    if fuzzy_search_enabled != "true"
                    else f'{{"term": {param_name}, "distance": 2}}'
                )

                search_conditions.append(
                    f"FullTextContains({field_path}, {search_param_str})"
                )

        # Build the query based on search type
        combined_conditions = " AND ".join(search_conditions)
        parameters.append({"name": "@limit", "value": limit})
        sql_query = f"""
        SELECT TOP @limit *
        FROM c
        WHERE c.status = @status
        AND {combined_conditions}
        ORDER BY c._ts DESC
        """

        if fuzzy_search_enabled == "true":
            search_type_description = "Fuzzy search with FullTextContains"
        else:
            search_type_description = "Exact FullTextContains"

        logger.debug(f"SQL QUERY: {sql_query}")

        field_search_info = "multi-field" if query else "specific-field"
        logger.info(
            f"🔍 Querying connections for person {person_id} using {search_type_description} {field_search_info} search "
            f"with {len(search_conditions)} condition(s) and parameters: {active_params}"
        )

        # Doesn't work with query_model (Confirmed with Fedor)
        results = [
            item
            async for item in connections_container.query_items(
                query=sql_query, parameters=parameters, partition_key=str(person_id)
            )
        ]
        # logger.info(f"QUERY RESULTS BEFORE Connection(): {results}")
        results = [Connection(**item) for item in results]

        logger.debug(f"QUERY RESULTS: {results}")
        return results

    except Exception as e:
        # Print stacktrace to console for debugging
        logger.debug(f"🚨 STACKTRACE for person {person_id}:")
        logger.debug(f"{traceback.format_exc()}\n")

        logger.error(
            f"❌ Error querying connections for person {person_id}: {str(e)}",
            extra={
                "person_id": person_id,
                "search_params": active_params
                if "active_params" in locals()
                else "unknown",
                "error": str(e),
                "traceback": traceback.format_exc(),
            },
        )
        raise
