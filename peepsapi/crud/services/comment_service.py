import os
from typing import List, Literal, Optional
from uuid import UUID, uuid4

from azure.cosmos.exceptions import CosmosResourceNotFoundError

from peepsapi.crud.models.comment import Comment, CommentInput, CommentsResponse
from peepsapi.crud.models.post import Post
from peepsapi.crud.services.pagination_service import get_page
from peepsapi.crud.services.people_services import get_unique_person_previews
from peepsapi.crud.services.reaction_service import set_reactions
from peepsapi.crud.services.soft_delete_service import soft_delete_item
from peepsapi.jobs import job_utils
from peepsapi.models.datetime import UTCDateTime
from peepsapi.models.job import IncrementCommentCountersParams
from peepsapi.services.cosmos_db import CosmosContainerAsync
from peepsapi.utils.error_handling import ResourceNotFoundError, ServerError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


async def create_post_comment(
    post_author_person_id: UUID,
    post_id: UUID,
    comment_input: CommentInput,
    author_person_id: UUID,
    posts_container: CosmosContainerAsync,
    comments_container: CosmosContainerAsync,
    active_jobs_container: CosmosContainerAsync,
    parent_comment_id: Optional[UUID] = None,
) -> Comment:
    """Create a comment for a post."""
    comment = Comment(
        id=uuid4(),
        target_type="post",
        target_id=post_id,
        parent_comment_id=parent_comment_id,
        author_person_id=author_person_id,
        created_at=UTCDateTime.now(),
        **comment_input.model_dump(),
    )

    # Validate that the post exists
    try:
        post: Post = await posts_container.read_model(
            id=post_id,
            partition_key=post_author_person_id,
            model_class=Post,
        )  # type: ignore
    except CosmosResourceNotFoundError:
        logger.error(
            "❌ Post not found or author mismatch",
            extra={
                "post_id": str(post_id),
                "author_person_id": str(post_author_person_id),
            },
        )
        raise ResourceNotFoundError(
            message="Post not found",
            details={
                "post_id": str(post_id),
                "author_person_id": str(post_author_person_id),
            },
        )

    if not post or post.author_person_id != post_author_person_id:
        logger.error(
            "❌ Post not found or author mismatch",
            extra={
                "post_id": str(post_id),
                "author_person_id": str(post_author_person_id),
            },
        )
        raise ResourceNotFoundError(
            message="Post not found or author mismatch",
            details={
                "post_id": str(post_id),
                "author_person_id": str(post_author_person_id),
            },
        )

    try:
        # Insert the comment
        await comments_container.create_model(model=comment, model_class=Comment)
        # Create a job to update feeds
        await job_utils.create(
            action="increment_comment_counters",
            params=IncrementCommentCountersParams(
                post_id=post_id,
                post_author_person_id=post_author_person_id,
                parent_comment_id=parent_comment_id,
            ),
            active_jobs_container=active_jobs_container,
        )
        logger.info(f"✅ Successfully created comment {comment.id}")
    except Exception as e:
        logger.error("❌ Error creating comment", extra={"error": str(e)})
        raise ServerError(
            message="Error creating comment",
            error_code="CREATE_ERROR",
            details={"error": str(e)},
        )

    return comment


async def get_comments_page(
    target_id: UUID,
    target_type: Literal["post", "event"],  # NOTE: ignored for now, "post" assumed
    next_page_token: Optional[str],
    comments_container: CosmosContainerAsync,
    people_container: CosmosContainerAsync,
    where_clause: Optional[str] = None,
    where_clause_parameters: List[dict] = [],
    skip_soft_deleted=False,
    get_person_previews: bool = True,
    fields: Optional[List[str]] = None,
    current_person_id: Optional[UUID] = None,
    person_reactions_container: Optional[CosmosContainerAsync] = None,
) -> CommentsResponse:
    """
    Get a page of comments for a specific target (e.g., post or event).

    Args:
        target_id (UUID): The ID of the target (e.g., post or event) to retrieve comments for.
        target_type (Literal["post", "event"]): The type of the target (currently ignored, "post" assumed).
        next_page_token (Optional[str]): The token for the next page of results, if any.
        comments_container (CosmosContainer): The Cosmos DB container for comments.
        people_container (CosmosContainer): The Cosmos DB container for people data.
        where_clause (Optional[str]): An optional SQL WHERE clause to filter comments.
        where_clause_parameters (List[dict]): Parameters for the WHERE clause.
        skip_soft_deleted (bool): Whether to skip comments marked as soft-deleted.
        get_person_previews (bool): Whether to include person preview data in the response.
        fields (Optional[List[str]]): Specific fields to include in the response.
        current_person_id (Optional[UUID]): The ID of the current person (for reactions).
        person_reactions_container (Optional[CosmosContainerAsync]): Container for person reactions.

    Returns:
        CommentsResponse: A response object containing people data, comments, and the next page token.
    """
    items, next_page = await get_page(
        partition_key=target_id,
        next_page_token=next_page_token,
        model_class=Comment,
        model_class_container=comments_container,
        page_size=int(os.getenv("POSTS_LIST_PAGE_SIZE", 10)),
        where_clause=where_clause,
        where_clause_parameters=where_clause_parameters,
        skip_soft_deleted=skip_soft_deleted,
        fields=fields,
    )

    # Sanitize output
    for item in items:
        # Hide original comment if the comment was deleted
        if item.deleted:
            item.content = ""
            item.media = []
        # Set URI used to POST or DELETE reactions
        item.reaction_uri = f"/reactions/comment/{str(item.target_id)}/{str(item.id)}"

    if current_person_id and person_reactions_container:
        await set_reactions(
            items=items,
            person_id=current_person_id,
            person_reactions_container=person_reactions_container,
        )

    people = []
    if get_person_previews and people_container:
        people = await get_unique_person_previews(
            people_ids={i.author_person_id for i in items},
            people_container=people_container,
        )

    return CommentsResponse(
        people=people,
        items=items,
        next_page=next_page,
    )


async def get_comment_author_person_id(
    id: UUID,
    target_id: UUID,
    target_type: Literal["post", "event"],  # NOTE: ignored for now, "post" assumed
    comments_container: CosmosContainerAsync,
) -> Optional[str]:
    """
    Get comment's author person ID.

    Args:
        id (UUID): The ID of the comment to check.
        target_id (UUID): The ID of the target (ie post) the comment belongs to.
        comments_container (CosmosContainer): The Cosmos DB container for comments.

    Returns:
        str: Comment's author person ID.
    """
    # Check if the comment exists and is authored by the current person
    res = [
        item
        async for item in comments_container.query_items(
            query="SELECT c.author_person_id FROM c WHERE c.id = @id OFFSET 0 LIMIT 1",
            parameters=[{"name": "@id", "value": str(id)}],
            partition_key=target_id,
        )
    ]
    return res[0]["author_person_id"] if len(res) else None


async def soft_delete_comment(
    id: UUID,
    target_id: UUID,
    target_type: Literal["post", "event"],  # NOTE: ignored for now, "post" assumed
    author_person_id: UUID,
    actor_person_id: UUID,
    actor_auth_source: str,
    comments_container: CosmosContainerAsync,
):
    """
    Soft delete a comment.

    Args:
        id (UUID): The ID of the comment to soft delete.
        target_id (UUID): The ID of the target (e.g., post or event) the comment belongs to.
        target_type (Literal["post", "event"]): The type of the target (currently ignored, "post" assumed).
        author_person_id (UUID): The ID of the person who authored the comment.
        actor_person_id (UUID): The ID of the person performing the soft delete action.
        actor_auth_source (str): The authentication source of the actor.
        comments_container (CosmosContainer): The Cosmos DB container for comments.

    Raises:
        ServerError: If an error occurs during the soft delete operation.
    """
    await soft_delete_item(
        id=id,
        partition_key=target_id,
        author_person_id=author_person_id,
        actor_person_id=actor_person_id,
        actor_auth_source=actor_auth_source,
        model_class=Comment,
        model_class_container=comments_container,
    )
