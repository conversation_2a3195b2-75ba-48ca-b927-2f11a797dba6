"""Case conversion middleware for FastAPI.

This middleware automatically converts incoming JSON payloads from camelCase to snake_case
and outgoing JSON responses from snake_case to camelCase.
"""

import json
from typing import Callable

from fastapi import Request, Response
from fastapi.routing import APIRoute
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request

from peepsapi.utils.case_conversion import (
    convert_dict_keys_to_snake_case,
    to_snake_case,
)
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class CaseConversionMiddleware(BaseHTTPMiddleware):
    """Middleware to convert case between camelCase and snake_case.

    This middleware:
    1. Converts incoming JSON request bodies from camelCase to snake_case
    2. Lets Pydantic handle the conversion of outgoing responses from snake_case to camelCase
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process the request and response.

        Args:
            request (Request): The incoming request
            call_next (Callable): The next middleware or route handler

        Returns:
            Response: The response
        """
        # Only process JSON content
        content_type = request.headers.get("content-type", "")
        if "application/json" in content_type:
            try:
                # Read the request body
                body = await request.body()
                if body:
                    # Parse the JSON body
                    json_body = json.loads(body)
                    # Convert all keys to snake_case
                    snake_case_body = convert_dict_keys_to_snake_case(json_body)
                    # Create a new request with the converted body
                    body_str = json.dumps(snake_case_body).encode()

                    # Override the request body
                    async def receive():
                        return {"type": "http.request", "body": body_str}

                    request._receive = receive

                    logger.debug(
                        "🔧 Converted request body from camelCase to snake_case",
                        request=request,
                        extra={"original": json_body, "converted": snake_case_body},
                    )
            except Exception as e:
                logger.warning(
                    f"Error converting request body: {str(e)}",
                    request=request,
                    extra={"error": str(e)},
                )

        # Process the request and get the response
        response = await call_next(request)

        # No need to modify the response as Pydantic's alias_generator already
        # converts snake_case to camelCase in the response

        return response


class CaseConversionAPIRoute(APIRoute):
    """APIRoute that converts camelCase query params to snake_case before endpoint execution."""

    def get_route_handler(self):
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request):
            # Convert query params to snake_case
            query_params = dict(request.query_params)
            logger.info(f"🔍 Query params: {query_params}")
            snake_case_params = {to_snake_case(k): v for k, v in query_params.items()}
            # Patch request._query_params (Starlette private API)
            request._query_params = snake_case_params
            return await original_route_handler(request)

        return custom_route_handler


# Usage example for routers:
# from peepsapi.services.middleware.case_conversion import CaseConversionAPIRoute
# router = APIRouter(route_class=CaseConversionAPIRoute)
