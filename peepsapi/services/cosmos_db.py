"""Database connection and container management for Cosmos DB.

This module provides classes for managing connections to Azure Cosmos DB,
including a singleton database connection and a container dependency for FastAPI.
"""

from base64 import urlsafe_b64encode
from typing import AsyncIterator, Iterator, Optional, Type, TypeVar, Union
from uuid import UUID

from azure.cosmos import ContainerP<PERSON>xy, CosmosClient, DatabaseProxy
from azure.cosmos.aio import ContainerProxy as ContainerProxyAsync
from azure.cosmos.aio import CosmosClient as CosmosClientAsync
from azure.cosmos.aio import DatabaseProxy as DatabaseProxyAsync
from pydantic import BaseModel

from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class DatabaseConnection:
    """Singleton class for managing database connections to Azure Cosmos DB.

    This class implements the Singleton pattern to ensure only one connection
    to the database is created and reused throughout the application.

    It uses lazy initialization to delay database connection until actually needed,
    allowing the application to load configuration and secrets first.
    """

    _instance: Optional["DatabaseConnection"] = None
    _client: Optional[CosmosClient] = None
    _database: Optional[DatabaseProxy] = None
    _initialized: bool = False
    _initializing: bool = False

    @classmethod
    def _new_cosmos_client(cls, *args):
        return CosmosClient(*args)

    @classmethod
    def initialize(cls):
        """Initialize database connection with current environment variables."""
        # Prevent recursive initialization
        if cls._initializing:
            return

        # Skip if already initialized
        if cls._initialized:
            return

        cls._initializing = True

        try:
            # Get configuration values (should be loaded by start.py or Key Vault)
            from peepsapi import config

            cosmos_endpoint = config.get("COSMOS_ENDPOINT")
            cosmos_key = config.get("COSMOS_KEY")
            cosmos_db = config.get("COSMOS_DB")

            if not cosmos_endpoint:
                logger.error("❌ COSMOS_ENDPOINT configuration is not set")
                return

            if not cosmos_key:
                logger.error("❌ COSMOS_KEY configuration is not set")
                return

            if not cosmos_db:
                logger.error("❌ COSMOS_DB configuration is not set")
                return

            try:
                cls._client = cls._new_cosmos_client(cosmos_endpoint, cosmos_key)
                cls._database = cls._client.get_database_client(cosmos_db)
                cls._instance = cls()
                cls._initialized = True
            except Exception as e:
                logger.error(
                    f"❌ Error during CosmosDB client initialization",
                    extra={"error": str(e)},
                )
                raise
        except Exception as e:
            logger.error(
                f"❌ Error during database initialization", extra={"error": str(e)}
            )
            raise
        finally:
            cls._initializing = False

    @classmethod
    def get_instance(cls):
        """Get singleton instance, initializing if needed."""
        # Try to initialize if not already initialized
        if not cls._initialized:
            cls.initialize()

            # If still not initialized after attempt, try once more
            if not cls._initialized:
                cls.initialize()

                # If still not initialized, raise error
                if not cls._initialized:
                    raise RuntimeError(
                        "Database connection could not be initialized. "
                        "Make sure COSMOS_ENDPOINT, COSMOS_KEY, and COSMOS_DB configurations are available."
                    )

        return cls._instance

    def get_container(self, container_name: str):
        """Get a container client."""
        if not self._database:
            raise RuntimeError(
                "Database not initialized. Call DatabaseConnection.initialize() first"
            )
        return self._database.get_container_client(container_name)


class DatabaseConnectionAsync(DatabaseConnection):
    """Async version of DatabaseConnection using CosmosClientAsync and DatabaseProxyAsync."""

    _instance: Optional["DatabaseConnectionAsync"] = None
    _client: Optional[CosmosClientAsync] = None
    _database: Optional[DatabaseProxyAsync] = None
    _initialized: bool = False
    _initializing: bool = False

    @classmethod
    def _new_cosmos_client(cls, *args):
        return CosmosClientAsync(*args)

    @classmethod
    async def close_connection(cls):
        """Destructor to close the CosmosClientAsync connection."""
        if cls._client:
            await cls._client.close()
            cls._client = None


# Type variable for Pydantic models
T = TypeVar("T", bound=BaseModel)


class CosmosContainer:
    """FastAPI dependency for Cosmos DB containers.

    This class provides a callable dependency that can be used with FastAPI's
    Depends() to inject a container client into route handlers.

    It also provides convenience methods that proxy to the underlying container client,
    allowing direct use of the container methods without having to call the dependency.

    It implements lazy initialization to delay database connection until actually needed,
    allowing the application to load configuration and secrets first.

    Enhanced methods are provided to automatically handle Pydantic model serialization.
    """

    def __init__(self, container_name: str):
        """Initialize the container dependency.

        Args:
            container_name (str): The name of the Cosmos DB container
        """
        self.container_name = container_name
        self.db = None
        self.container: ContainerProxy = None

    def _new_database_connection(self):
        return DatabaseConnection.get_instance()

    def _ensure_initialized(self):
        """Ensure the database connection and container are initialized."""
        if self.container is None:
            try:
                self.db = self._new_database_connection()
                self.container = self.db.get_container(self.container_name)
            except Exception as e:
                logger.error(
                    f"❌ Failed to initialize container {self.container_name}",
                    extra={"error": str(e)},
                )
                raise

    def _serialize_model(self, model: BaseModel) -> dict:
        """Serialize a Pydantic model to a JSON-safe dictionary.

        This method handles UTCDateTime objects by converting them to ISO format strings.

        Args:
            model (BaseModel): The Pydantic model to serialize

        Returns:
            dict: A JSON-safe dictionary representation of the model
        """
        # Import necessary modules
        import datetime
        import json

        from peepsapi.models import UTCDateTime
        from peepsapi.models.datetime import to_iso_string

        # Define a custom JSON encoder for UTCDateTime, UUID and bytes
        class CosmosSafeEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, UTCDateTime):
                    return to_iso_string(obj)
                elif isinstance(obj, datetime.datetime):
                    return obj.isoformat().replace("+00:00", "Z")
                elif isinstance(obj, UUID):
                    return str(obj)
                elif isinstance(obj, bytes):
                    return urlsafe_b64encode(obj).decode("utf-8").rstrip("=")
                return super().default(obj)

        # Get the model as a dictionary
        model_dict = model.model_dump()

        # Convert the dictionary to a JSON string with the custom encoder
        model_json = json.dumps(model_dict, cls=CosmosSafeEncoder)

        # Convert back to a dictionary
        model_dict_safe = json.loads(model_json)

        return model_dict_safe

    def _auto_serialize_value(self, value):
        """Automatically serialize a value based on its type.

        This method handles known types like UTCDateTime, datetime, and bytes.
        For other types, it returns the value as-is.

        Args:
            value: The value to serialize

        Returns:
            The serialized value that is JSON-safe
        """
        import datetime
        from base64 import urlsafe_b64encode
        from uuid import UUID

        from pydantic import BaseModel

        from peepsapi.models import UTCDateTime
        from peepsapi.models.datetime import to_iso_string

        if isinstance(value, dict):
            return {k: self._auto_serialize_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [self._auto_serialize_value(v) for v in value]
        elif isinstance(value, UTCDateTime):
            return to_iso_string(value)
        elif isinstance(value, datetime.datetime):
            return value.isoformat().replace("+00:00", "Z")
        elif isinstance(value, UUID):
            return str(value)
        elif isinstance(value, bytes):
            return urlsafe_b64encode(value).decode("utf-8").rstrip("=")
        elif isinstance(value, BaseModel):
            return self._serialize_model(value)
        return value

    @staticmethod
    def _normalize_parameters(parameters: Optional[list[dict]]) -> list[dict]:
        """Convert UUIDs in parameters to strings for CosmosDB compatibility."""
        if not parameters:
            return []
        return [
            {
                "name": p["name"],
                "value": str(p["value"])
                if isinstance(p["value"], UUID)
                else p["value"],
            }
            for p in parameters
        ]

    def __call__(self):
        """Make the class callable to work as a FastAPI dependency.

        Returns:
            ContainerProxy: The Cosmos DB container client
        """
        self._ensure_initialized()
        return self.container

    @staticmethod
    def _model_validate_wrapper(item, model_class):
        if "etag" in model_class.model_fields:
            item["etag"] = item["_etag"]
        return model_class.model_validate(item)

    @staticmethod
    def _call_op_on_model(op: str):
        def decorator(method):
            """Decorator to call op and apply model validator"""

            def wrapper(self, *args, quiet=False, **kwargs):
                self._ensure_initialized()
                model_class = kwargs.get("model_class", None)
                try:
                    op_kwargs = method(self, *args, **kwargs)
                    logger.info(
                        f"calling {op} on {self.container_name} (sync)",
                        extra={"op_kwargs": op_kwargs},
                    )
                    op_result = getattr(self.container, op)(**op_kwargs)
                    is_iterator = isinstance(op_result, Iterator)
                    logger.info(
                        f"{op} on {self.container_name} (sync) result is iterator: {is_iterator}"
                    )
                    if not model_class:
                        return list(op_result) if is_iterator else op_result

                    if not is_iterator:
                        return (
                            self._model_validate_wrapper(op_result, model_class)
                            if op_result
                            else op_result
                        )

                    return [
                        self._model_validate_wrapper(item, model_class)
                        for item in op_result
                    ]
                except Exception as e:
                    if not quiet:
                        logger.error(
                            f"❌ Failed to run {method.__name__} from {self.container_name}: {str(e)}",
                            extra={"error": str(e)},
                            exc_info=e,
                        )
                    raise

            wrapper._original_method = method
            wrapper._cosmos_op = op
            return wrapper

        return decorator

    @_call_op_on_model(op="query_items")
    def query_models(
        self,
        *,
        query: str,
        model_class: Type[T],  # required by @call_op_on_model
        parameters: Optional[list[dict]] = None,
        partition_key: Optional[Union[str, UUID]] = None,
    ):
        """Run a SQL query and return the results as a list of Pydantic models.

        Automatically enables cross-partition query if no partition_key is provided.

        Args:
            query (str): SQL query to run.
            model_class (Type[T]): Pydantic model class to deserialize results.
            parameters (Optional[list[dict]]): Query parameters.
            partition_key (Optional[str]): Restrict query to this partition.

        Returns:
            list[T]: List of deserialized Pydantic model instances. Returns an empty list if no results found.
        """
        parameters = self._normalize_parameters(parameters)
        query_args = {
            "query": query,
            "parameters": parameters or [],
        }

        logger.info(f"calling query_models from {self.__class__.__name__}")
        if self.__class__.__name__ == "CosmosContainer":
            if partition_key:
                query_args["partition_key"] = str(partition_key)
            else:
                query_args["enable_cross_partition_query"] = True
        else:
            query_args["partition_key"] = str(partition_key) if partition_key else None

        return query_args

    @_call_op_on_model(op="read_item")
    def read_model(
        self,
        id: Union[str, UUID],
        partition_key: Union[str, UUID],
        model_class: Optional[Type[T]] = None,  # required by @call_op_on_model
    ):
        """Read a single item from the container and return it as a Pydantic model.

        Args:
            id (str): The ID of the item to read.
            partition_key (str): The partition key value of the item.
            model_class (Type[T]): The Pydantic model class to deserialize the item into.

        Returns:
            T: An instance of the Pydantic model.
        """
        return dict(item=str(id), partition_key=str(partition_key))

    @_call_op_on_model(op="read_all_items")
    def read_models_by_partition_key(
        self,
        partition_key: Union[str, UUID],
        model_class: Type[T],  # required by @call_op_on_model
    ):
        """Read all items matching the partition key and return them as a list of Pydantic models.

        Args:
            partition_key (str): The partition key value to query.
            model_class (Type[T]): The Pydantic model class to deserialize the items into.

        Returns:
            list[T]: A list of model instances matching the partition key.
        """
        return dict(partition_key=str(partition_key))

    @_call_op_on_model(op="create_item")
    def create_model(
        self,
        model: BaseModel,
        quiet=False,
        model_class: Optional[Type[T]] = None,  # required by @call_op_on_model
    ):
        """Create an item in the container from a Pydantic model and return it as a BaseModel.

        Args:
            model (BaseModel): The Pydantic model to create.
            model_class (Optional[Type[BaseModel]]): Optional model class to return validated instance.

        Returns:
            BaseModel or dict: The created item as a model instance if model_class is provided; else raw dict.
        """
        return dict(body=self._serialize_model(model))

    @_call_op_on_model(op="replace_item")
    def replace_model(
        self,
        model: BaseModel,
        model_class: Optional[Type[T]] = None,  # required by @call_op_on_model
    ):
        """Replace an item in the container with a Pydantic model.

        This method automatically serializes the model to handle UTCDateTime objects.

        Args:
            item (str): The ID of the item to replace
            model (BaseModel): The Pydantic model with the new data

        Returns:
            dict: The replaced item
        """
        return dict(body=self._serialize_model(model), item=str(model.id))

    @_call_op_on_model(op="patch_item")
    def patch_model(
        self,
        *,
        item: Union[str, UUID],
        partition_key: Union[str, UUID],
        patch_operations: Optional[list[dict]] = None,
        if_match: Optional[str] = None,
        update_fields: Optional[dict] = None,
        value_serializer: Optional[callable] = None,
        model_class: Optional[Type[T]] = None,  # required by @call_op_on_model
    ) -> Union[dict, T]:
        """Patch an item using patch ops or update_fields with optional model validation.
        If patch operation fails due to non-existent paths, falls back to read-modify-write.

        Args:
            item (str): Cosmos item ID.
            partition_key (str): Partition key.
            patch_operations (Optional[list[dict]]): Raw patch operations.
            if_match (Optional[str]): Expected ETag value of the target item.
            update_fields (Optional[dict]): Dict of fields to patch. Supports nested paths (e.g., 'device_info.last_used_at').
            model_class (Optional[Type[BaseModel]]): Pydantic model to validate top-level field names.
            value_serializer (Optional[callable]): Optional value serializer.

        Returns:
            T | dict: Patched item from Cosmos DB.
        """
        operations = patch_operations[:] if patch_operations else []

        if update_fields:
            if model_class:
                allowed_top_fields = set(model_class.model_fields.keys())
                for path in update_fields:
                    top_level = path.split(".", 1)[0]
                    if top_level not in allowed_top_fields:
                        raise ValueError(
                            f"Field '{top_level}' is not defined in model '{model_class.__name__}'"
                        )

            for path, value in update_fields.items():
                if value_serializer:
                    value = value_serializer(value)
                else:
                    value = self._auto_serialize_value(value)

                # Convert dotted path to JSON Pointer format
                json_pointer = "/" + path.replace(".", "/")
                operations.append(
                    {
                        "op": "add",
                        "path": json_pointer,
                        "value": value,
                    }
                )

        # Ensure all operations are properly serialized (fix bug)
        operations = self._auto_serialize_value(operations)

        return dict(
            item=str(item),
            partition_key=str(partition_key),
            patch_operations=operations,
            if_match=if_match,
        )

    @staticmethod
    def _call_op_on_item(method):
        op = method.__name__
        logger.info(f"creating decorator for {op}")
        """Decorator to call op on item"""

        def wrapper(self, *args, **kwargs):
            self._ensure_initialized()
            op_kwargs = method(self, *args, **kwargs)
            logger.info(
                f"calling {op} on {self.container_name} (item, sync)",
                extra={"op_kwargs": op_kwargs},
            )
            return getattr(self.container, op)(**op_kwargs)

        return wrapper

    @_call_op_on_item
    def read_item(self, item: Union[str, UUID], partition_key: Union[str, UUID]):
        """Read an item from the container.

        Args:
            item (str): The ID of the item to read
            partition_key (str): The partition key of the item

        Returns:
            dict: The item data
        """
        return dict(item=str(item), partition_key=str(partition_key))

    @_call_op_on_item
    def create_item(self, body):
        """Create an item in the container.

        Args:
            body (dict): The item data to create

        Returns:
            dict: The created item
        """
        return dict(body=body)

    @_call_op_on_item
    def replace_item(self, item: Union[str, UUID], body):
        """Replace an item in the container.

        Args:
            item (str): The ID of the item to replace
            body (dict): The new item data

        Returns:
            dict: The replaced item
        """
        return dict(item=str(item), body=body)

    @_call_op_on_item
    def upsert_item(self, body):
        """Upsert an item in the container.

        Args:
            body (dict): The item data to upsert

        Returns:
            dict: The upserted item
        """
        return dict(body=body)

    @_call_op_on_item
    def delete_item(
        self,
        item: Union[str, UUID],
        partition_key: Union[str, UUID],
        if_match: Optional[Union[str, UUID]] = None,
    ):
        """Delete an item from the container.

        Args:
            item (str): The ID of the item to delete
            partition_key (str): The partition key of the item
            if_match (Optional[str]): Optional ETag value for conditional deletion

        Returns:
            None
        """
        self._ensure_initialized()
        if if_match:
            if_match = str(if_match)
        return dict(item=str(item), partition_key=str(partition_key), if_match=if_match)

    @_call_op_on_item
    def query_items(
        self,
        query,
        parameters=None,
        partition_key=None,
        enable_cross_partition_query=None,
    ):
        """Query items from the container.

        Args:
            query (str): The query to execute
            parameters (list, optional): The query parameters
            enable_cross_partition_query (bool, optional): Whether to enable cross-partition query

        Returns:
            ItemPaged: The query results
        """
        parameters = self._normalize_parameters(parameters)
        query_args = {
            "query": query,
            "parameters": parameters or [],
        }

        if self.__class__.__name__ == "CosmosContainer":
            if partition_key:
                query_args["partition_key"] = str(partition_key)
            else:
                query_args["enable_cross_partition_query"] = True
        else:
            query_args["partition_key"] = str(partition_key) if partition_key else None

        return query_args

    @_call_op_on_item
    def read_all_items(self):
        """Read all items from the container.

        Returns:
            ItemPaged: All items in the container
        """
        return dict()

    def get_container_client(self):
        """Get the underlying container client.

        Returns:
            ContainerProxy: The Cosmos DB container client
        """
        self._ensure_initialized()
        return self.container

    @staticmethod
    def list_all_containers():
        """List all containers in the database.

        This is a diagnostic method to help identify issues with container initialization.

        Returns:
            list: A list of container names in the database
        """
        try:
            db = DatabaseConnection.get_instance()
            if not db or not db._database:
                return []

            # List all containers in the database
            containers = list(db._database.list_containers())
            return [c["id"] for c in containers]
        except Exception:
            return []

    @staticmethod
    def create_container_if_not_exists(container_name, partition_key_path="/id"):
        """Create a container if it doesn't exist.

        This is a utility method to ensure a container exists in the database.

        Args:
            container_name (str): The name of the container to create
            partition_key_path (str): The partition key path for the container

        Returns:
            bool: True if the container was created or already exists, False otherwise
        """
        from peepsapi.utils.logging import get_logger

        logger = get_logger(__name__)

        try:
            db = DatabaseConnection.get_instance()
            if not db or not db._database:
                return False

            # Check if the container exists
            containers = CosmosContainer.list_all_containers()
            if container_name in containers:
                return True

            # Create the container
            logger.info(f"Creating container {container_name}")
            db._database.create_container(
                id=container_name,
                partition_key={"paths": [partition_key_path], "kind": "Hash"},
            )
            return True
        except Exception as e:
            logger.error(f"Error creating container {container_name}: {e}")
            return False


class CosmosContainerAsync(CosmosContainer):
    """Async version of CosmosContainer."""

    def __init__(self, container_name: str):
        """Initialize the container dependency.

        Args:
            container_name (str): The name of the Cosmos DB container
        """
        self.container_name = container_name
        self.db = None
        self.container: ContainerProxyAsync = None

    def _new_database_connection(self):
        return DatabaseConnectionAsync.get_instance()

    @staticmethod
    def _call_op_on_model(op: str):
        def decorator(method):
            """Decorator to call op and apply model validator"""

            async def wrapper(self, *args, quiet=False, **kwargs):
                self._ensure_initialized()
                model_class = kwargs.get("model_class", None)
                try:
                    op_kwargs = method(self, *args, **kwargs)
                    logger.info(
                        f"calling {op} on {self.container_name} (async)",
                        extra={"op_kwargs": op_kwargs},
                    )
                    op_result = getattr(self.container, op)(**op_kwargs)
                    is_iterator = isinstance(op_result, AsyncIterator)
                    logger.info(
                        f"{op} on {self.container_name} (async) result is iterator: {is_iterator}"
                    )
                    if not model_class:
                        return (
                            [item async for item in op_result]
                            if is_iterator
                            else await op_result
                        )

                    if not is_iterator:
                        op_result = await op_result
                        return (
                            self._model_validate_wrapper(op_result, model_class)
                            if op_result
                            else op_result
                        )

                    return [
                        self._model_validate_wrapper(item, model_class)
                        async for item in op_result
                    ]
                except Exception as e:
                    if not quiet:
                        logger.error(
                            f"❌ Failed to run {method.__name__} (async) from {self.container_name}: {str(e)}",
                            extra={"error": str(e)},
                            exc_info=e,
                        )
                    raise

            wrapper._original_method = method
            wrapper._cosmos_op = op
            return wrapper

        return decorator

    async def read_item(self, *args, **kwargs):
        """Async, version of CosmosContainer.read_item see sync version for interface."""
        co = super().read_item(*args, **kwargs)
        return await co

    async def create_item(self, *args, **kwargs):
        """Async, version of CosmosContainer.create_item see sync version for interface."""
        co = super().create_item(*args, **kwargs)
        return await co

    async def replace_item(self, *args, **kwargs):
        """Async, version of CosmosContainer.replace_item see sync version for interface."""
        co = super().replace_item(*args, **kwargs)
        return await co

    async def upsert_item(self, *args, **kwargs):
        """Async, version of CosmosContainer.upsert_item see sync version for interface."""
        co = super().upsert_item(*args, **kwargs)
        return await co

    async def delete_item(self, *args, **kwargs):
        """Async, version of CosmosContainer.delete_item see sync version for interface."""
        co = super().delete_item(*args, **kwargs)
        return await co

    # Definitions below are overwritten, they are here for linter
    async def query_models(self, *args, **kwargs):
        pass

    async def read_model(self, *args, **kwargs):
        pass

    async def read_models_by_partition_key(self, *args, **kwargs):
        pass

    async def create_model(self, *args, **kwargs):
        pass

    async def replace_model(self, *args, **kwargs):
        pass

    async def patch_model(self, *args, **kwargs):
        pass

    # query_items doesn't require async version to work with async client
    # read_all_items doesn't require async version to work with async client


# Dynamically wrap parent model methods with async decorator
for name in [
    "query_models",
    "read_model",
    "read_models_by_partition_key",
    "create_model",
    "replace_model",
    "patch_model",
]:
    wrapper = getattr(CosmosContainer, name)
    # print(dir(wrapper))
    method = wrapper._original_method
    op = wrapper._cosmos_op
    logger.info(f"Async re-wrap of {method.__name__} with {op} op")
    setattr(
        CosmosContainerAsync, name, CosmosContainerAsync._call_op_on_model(op)(method)
    )
