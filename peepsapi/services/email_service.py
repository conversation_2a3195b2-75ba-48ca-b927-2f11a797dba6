import os
import time
from typing import Any, Dict, Optional

from email_validator import EmailNotValidError, validate_email
from jinja2 import Environment, FileSystemLoader, select_autoescape

from peepsapi.utils.error_handling import ServerError, ValidationError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

DEFAULT_TEMPLATE_DIR = os.path.join("static", "email_templates")
MAX_RETRY_ATTEMPTS = 3  # Maximum number of retry attempts for email sending


class EmailService:
    """Service for sending transactional emails using ACS."""

    def __init__(self, template_dir: str = DEFAULT_TEMPLATE_DIR) -> None:
        self.template_dir = template_dir
        self.env = Environment(
            loader=FileSystemLoader(self.template_dir),
            autoescape=select_autoescape(["html", "xml"]),
            trim_blocks=True,
            lstrip_blocks=True,
        )
        self._client = None

    # Client creation is isolated for easier mocking
    def _get_client(self):  # pragma: no cover - thin wrapper
        if self._client is not None:
            return self._client
        connection_string = os.getenv("ACS_CONNECTION_STRING")
        sender_address = os.getenv("ACS_SENDER_ADDRESS", "<EMAIL>")

        if not connection_string:
            raise ValueError("ACS_CONNECTION_STRING not configured")
        try:
            from azure.communication.email import EmailClient

            self._client = EmailClient.from_connection_string(connection_string)
            return self._client
        except Exception as exc:  # pragma: no cover - best effort
            logger.error("❌ Failed to create ACS client", extra={"error": str(exc)})
            raise

    def _load_template(self, name: str, version: str = "v1"):
        filename = f"{name}_{version}.html"
        try:
            return self.env.get_template(filename)
        except Exception as exc:
            logger.error(
                "❌ Template not found",
                extra={"template": filename, "error": str(exc)},
            )
            raise FileNotFoundError(filename)

    def _render_template(self, template, context: Dict[str, Any]) -> str:
        try:
            return template.render(**context)
        except Exception as exc:
            logger.error("❌ Error rendering template", extra={"error": str(exc)})
            raise

    def _validate_email(self, address: str) -> None:
        try:
            validate_email(address, check_deliverability=True)
        except EmailNotValidError as exc:
            logger.warning(
                "⚠️ Invalid email rejected",
                extra={"email": address, "error": str(exc)},
            )
            raise ValidationError(message=str(exc), error_code="INVALID_EMAIL")
        if self._is_bounced(address):
            logger.warning("⚠️ Bounced email rejected", extra={"email": address})
            raise ValidationError(
                message="Email previously bounced", error_code="BOUNCE"
            )

    def _is_bounced(self, address: str) -> bool:
        # Placeholder for bounce check integration
        return False

    def _send_via_acs(
        self,
        recipient: str,
        subject: str,
        body: str,
        max_retries: int = MAX_RETRY_ATTEMPTS,
    ) -> None:
        """Send email via ACS with retry logic."""
        self._send_with_retry(recipient, subject, body, max_retries)

    def _send_with_retry(
        self, recipient: str, subject: str, body: str, max_retries: int
    ) -> None:
        """Send email with retry attempts and exponential backoff."""
        client = self._get_client()
        last_exception = None

        for attempt in range(1, max_retries + 1):
            try:
                # Use a simple call signature compatible with azure.communication.email
                sender_address = os.getenv("ACS_SENDER_ADDRESS", "<EMAIL>")
                email_msg = {
                    "senderAddress": sender_address,
                    "content": {"subject": subject, "html": body},
                    "recipients": {"to": [{"address": recipient}]},
                }
                try:
                    poller = client.begin_send(message=email_msg)
                    result = poller.result()
                    if (
                        hasattr(result, "status")
                        and result.status.lower() != "succeeded"
                    ):
                        logger.error(
                            f"❌ Email send operation did not succeed: {getattr(result, 'status', None)}",
                            extra={"recipient": recipient, "result": str(result)},
                        )
                        raise Exception(
                            f"Email send operation failed with status: {getattr(result, 'status', None)}"
                        )
                except Exception as send_exc:
                    logger.error(f"❌ ACS send error details: {str(send_exc)}")
                    raise send_exc

                if attempt > 1:
                    logger.info(
                        f"📤 Sent email successfully on attempt {attempt}",
                        extra={
                            "recipient": recipient,
                            "attempt": attempt,
                            "total_attempts": max_retries,
                        },
                    )
                else:
                    logger.info(
                        "📤 Sent email on first attempt", extra={"recipient": recipient}
                    )
                return  # Success, exit the retry loop

            except Exception as exc:
                last_exception = exc
                if attempt < max_retries:
                    # Calculate exponential backoff delay (1s, 2s, 4s, etc.)
                    delay = 2 ** (attempt - 1)
                    logger.warning(
                        "⚠️ Email sent failed, retrying",
                        extra={
                            "recipient": recipient,
                            "attempt": attempt,
                            "max_attempts": max_retries,
                            "delay_seconds": delay,
                            "error": str(exc),
                        },
                    )
                    time.sleep(delay)
                else:
                    # Final attempt failed
                    logger.error(
                        "❌ Email sent failed on all attempts",
                        extra={
                            "recipient": recipient,
                            "total_attempts": max_retries,
                            "error": str(exc),
                        },
                    )
                    raise ServerError(
                        message=f"Failed to send email to {recipient} after {max_retries} attempts.",
                        error_code="EMAIL_DELIVERY_FAILED",
                    ) from last_exception

    def send_email(
        self,
        recipient: str,
        template_name: str,
        context: Optional[Dict[str, Any]] = None,
        *,
        version: str = "v1",
    ) -> None:
        """Validate, render, and send an email with default retry configuration."""
        self.send_email_with_retry(
            recipient,
            template_name,
            context,
            version=version,
            max_retries=MAX_RETRY_ATTEMPTS,
        )

    def send_email_with_retry(
        self,
        recipient: str,
        template_name: str,
        context: Optional[Dict[str, Any]] = None,
        *,
        version: str = "v1",
        max_retries: int = MAX_RETRY_ATTEMPTS,
    ) -> None:
        """Validate, render, and send an email with customizable retry attempts."""
        context = context or {}
        self._validate_email(recipient)
        template = self._load_template(template_name, version)
        body = self._render_template(template, context)
        subject = context.get("subject", "Peeps Notification")

        self._send_via_acs(recipient, subject, body, max_retries)
