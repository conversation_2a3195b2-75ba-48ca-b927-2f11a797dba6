"""Authentication package for PeepsAPI.

This package contains modules for authentication, including passkey authentication,
JWT token management, Azure AD SSO, and middleware.
"""

from fastapi import APIRouter

from peepsapi.auth.routes import (
    azure_ad,
    devices,
    email_verification,
    invite,
    login,
    monitoring,
    recovery,
    registration,
    session,
    token,
)
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.challenge_service import challenge_service
from peepsapi.auth.services.device_service import device_service
from peepsapi.auth.services.rate_limiter import (
    auth_rate_limiter,
    login_rate_limiter,
    registration_rate_limiter,
)
from peepsapi.auth.services.security_service import audit_logger, brute_force_protection
from peepsapi.auth.services.token_service import token_service
from peepsapi.auth.utils.constants import (
    OVERRIDE_PERSON_ID_COOKIE_NAME,
    SESSION_TOKEN_COOKIE_NAME,
)
from peepsapi.auth.utils.middleware import AuthMiddleware, add_auth_middleware

# Create a router for all auth endpoints
router = APIRouter()

# Include all the auth routers
router.include_router(invite.router)
router.include_router(registration.router)
router.include_router(login.router)
router.include_router(recovery.router)
router.include_router(session.router)
router.include_router(devices.router, include_in_schema=False)
router.include_router(azure_ad.router, include_in_schema=False)
router.include_router(monitoring.router, include_in_schema=False)
router.include_router(token.router)
router.include_router(email_verification.router)

# Export commonly used functions and classes
__all__ = [
    # Constants
    "OVERRIDE_PERSON_ID_COOKIE_NAME",
    "SESSION_TOKEN_COOKIE_NAME",
    # Middleware
    "AuthMiddleware",
    "add_auth_middleware",
    # Services
    "auth_service",
    "challenge_service",
    "device_service",
    "token_service",
    "auth_rate_limiter",
    "login_rate_limiter",
    "registration_rate_limiter",
    "audit_logger",
    "brute_force_protection",
    # Router
    "router",
]
