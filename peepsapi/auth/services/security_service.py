"""Security service for authentication.

This module provides security utilities for authentication, including
password strength validation, brute force protection, and audit logging.
"""

import time
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from typing import Dict, List, Optional, Set, Tuple, Union
from uuid import UUID

from fastapi import Request

from peepsapi import config
from peepsapi.models import UTCDateTime, now, to_iso_string
from peepsapi.models.base import IdentifierType
from peepsapi.services.cosmos_containers import get_audit_logs_container
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class AuditLogger:
    """Logger for security-related events.

    This class provides methods for logging security-related events,
    such as authentication attempts, account lockouts, and password changes.
    """

    def __init__(self):
        """Initialize the audit logger."""
        self.enable_db_logging = (
            config.get("ENABLE_AUDIT_LOGGING", "true").lower() == "true"
        )

        # Only initialize the container if logging is enabled
        if self.enable_db_logging:
            self.audit_container = get_audit_logs_container()

    def log_event(
        self,
        event_type: str,
        user_id: Optional[Union[str, UUID]] = None,
        email: Optional[str] = None,
        phone_number: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict] = None,
        success: bool = True,
    ) -> None:
        """Log a security event.

        Args:
            event_type (str): The type of event
            user_id (Optional[Union[str, UUID]]): The ID of the user
            email (Optional[str]): The email address of the user
            phone_number (Optional[str]): The phone number of the user
            ip_address (Optional[str]): The IP address of the client
            user_agent (Optional[str]): The user agent of the client
            details (Optional[Dict]): Additional details about the event
            success (bool): Whether the event was successful
        """
        # Log to console
        log_message = f"AUDIT: {event_type} - "
        if user_id:
            log_message += f"User ID: {user_id} - "
        if email:
            log_message += f"Email: {email} - "
        if phone_number:
            log_message += f"Phone: {phone_number} - "
        if ip_address:
            log_message += f"IP: {ip_address} - "
        log_message += f"Success: {success}"
        if details:
            log_message += f" - Details: {details}"

        if success:
            logger.info(log_message)
        else:
            logger.warning(log_message)

        # Log to database if enabled
        if self.enable_db_logging:
            try:
                log_entry = {
                    "id": f"{int(time.time() * 1000)}",
                    "event_type": event_type,
                    "timestamp": to_iso_string(now()),
                    "user_id": user_id,
                    "email": email,
                    "phone_number": phone_number,
                    "ip_address": ip_address,
                    "user_agent": user_agent,
                    "details": details,
                    "success": success,
                }
                # Double-check that we have the container before trying to use it
                if hasattr(self, "audit_container"):
                    self.audit_container.create_item(log_entry)
                else:
                    logger.debug(
                        "🧹 Audit container not initialized, skipping database logging"
                    )
            except Exception as e:
                logger.error(f"❌ Error logging to database", extra={"error": str(e)})

    def log_registration_attempt(
        self,
        identifier: Optional[str] = None,
        user_id: Optional[Union[str, UUID]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        reason: Optional[str] = None,
    ) -> None:
        """Log an authentication attempt.

        Args:
            identifier (str): The identifier used for authentication (email, phone, etc.)
            ip_address (Optional[str]): The IP address of the client
            user_agent (Optional[str]): The user agent of the client
            success (bool): Whether the authentication was successful
            reason (Optional[str]): The reason for failure, if applicable
        """
        details = {"reason": reason} if reason else None
        self.log_event(
            event_type="registration_attempt",
            user_id=user_id,
            email=identifier if "@" in identifier else None,
            phone_number=identifier if "@" not in identifier else None,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details,
            success=success,
        )

    def log_authentication_attempt(
        self,
        identifier: Optional[str] = None,
        user_id: Optional[Union[str, UUID]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        reason: Optional[str] = None,
    ) -> None:
        """Log an authentication attempt.

        Args:
            identifier (str): The identifier used for authentication (email, phone, etc.)
            ip_address (Optional[str]): The IP address of the client
            user_agent (Optional[str]): The user agent of the client
            success (bool): Whether the authentication was successful
            reason (Optional[str]): The reason for failure, if applicable
        """
        details = {"reason": reason} if reason else None
        self.log_event(
            event_type="authentication_attempt",
            user_id=user_id,
            email=identifier if "@" in identifier else None,
            phone_number=identifier if "@" not in identifier else None,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details,
            success=success,
        )

    def log_account_lockout(
        self,
        request: Request,
        user_id: Optional[Union[str, UUID]] = None,
        email: Optional[str] = None,
        reason: str = "Too many failed login attempts",
    ) -> None:
        """Log an account lockout event.

        Args:
            request (Request): The FastAPI request object
            user_id (Optional[str]): The ID of the user
            email (Optional[str]): The email address of the user
            reason (str): The reason for the lockout
        """
        ip_address = request.client.host if request.client else "127.0.0.1"
        user_agent = request.headers.get("user-agent")

        self.log_event(
            event_type="account_lockout",
            user_id=user_id,
            email=email,
            ip_address=ip_address,
            user_agent=user_agent,
            details={"reason": reason},
            success=False,
        )

    def log_password_change(
        self,
        user_id: Union[str, UUID],
        request: Request,
        success: bool = True,
        reason: Optional[str] = None,
    ) -> None:
        """Log a password change event.

        Args:
            user_id (str): The ID of the user
            request (Request): The FastAPI request object
            success (bool): Whether the password change was successful
            reason (Optional[str]): The reason for failure, if applicable
        """
        ip_address = request.client.host if request.client else "127.0.0.1"
        user_agent = request.headers.get("user-agent")

        details = {"reason": reason} if reason else None
        self.log_event(
            event_type="password_change",
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details,
            success=success,
        )

    def log_recovery_attempt(
        self,
        identifier_type: IdentifierType,
        identifier_value: str,
        user_id: Optional[Union[str, UUID]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        reason: Optional[str] = None,
    ) -> None:
        """Log an account recovery attempt.

        Args:
            identifier (str): The identifier used for recovery (email, phone, etc.)
            ip_address (Optional[str]): The IP address of the client
            user_agent (Optional[str]): The user agent of the client
            success (bool): Whether the recovery was successful
            reason (Optional[str]): The reason for failure, if applicable
        """
        details = {"reason": reason} if reason else None
        self.log_event(
            event_type="recovery_attempt",
            user_id=user_id,
            email=identifier_value if identifier_type == IdentifierType.EMAIL else None,
            phone_number=identifier_value
            if identifier_type == IdentifierType.PHONE
            else None,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details,
            success=success,
        )

    def log_invite_attempt(
        self,
        identifier_type: IdentifierType,
        identifier_value: str,
        current_person: Union[str, UUID],
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        reason: Optional[str] = None,
    ) -> None:
        """Create invite attempt.

        Args:
            identifier (str): The identifier used for recovery (email, phone, etc.)
            ip_address (Optional[str]): The IP address of the client
            user_agent (Optional[str]): The user agent of the client
            success (bool): Whether the recovery was successful
            reason (Optional[str]): The reason for failure, if applicable
        """
        details = {"reason": reason} if reason else None
        email = None
        phone_number = None
        if identifier_type == IdentifierType.EMAIL:
            email = identifier_value
        else:  # IdentifierType.PHONE
            phone_number = identifier_value
        self.log_event(
            event_type="create_invite",
            email=email,
            phone_number=phone_number,
            user_id=current_person,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details,
            success=success,
        )


class BruteForceProtection:
    """Protection against brute force attacks.

    This class implements protection against brute force attacks by tracking
    failed authentication attempts and blocking IPs and users after too many failures.
    """

    def __init__(self, max_attempts: int = 5, block_duration_minutes: int = 15):
        """Initialize the brute force protection.

        Args:
            max_attempts (int): The maximum number of failed attempts allowed
            block_duration_minutes (int): The duration to block in minutes
        """
        self.max_attempts = max_attempts
        self.block_duration = timedelta(minutes=block_duration_minutes)
        self.failed_attempts: Dict[str, List[float]] = defaultdict(list)
        self.blocked_ips: Set[str] = set()
        self.blocked_users: Set[str] = set()
        self.block_expiry: Dict[str, UTCDateTime] = {}
        self.audit_logger = AuditLogger()

    def record_failed_attempt(
        self,
        request: Request,
        user_id: Optional[Union[str, UUID]] = None,
        email: Optional[str] = None,
    ) -> Tuple[bool, str]:
        """Record a failed authentication attempt.

        Args:
            request (Request): The FastAPI request object
            user_id (Optional[str]): The ID of the user
            email (Optional[str]): The email address of the user

        Returns:
            Tuple[bool, str]: Whether the IP/user is blocked and the reason
        """
        ip = request.client.host if request.client else "127.0.0.1"

        # Check if IP is blocked
        if ip in self.blocked_ips:
            self.audit_logger.log_event(
                event_type="authentication_attempt",
                user_id=user_id,
                email=email,
                ip_address=request.client.host if request.client else "127.0.0.1",
                user_agent=request.headers.get("user-agent", ""),
                details={"reason": "IP blocked"},
                success=False,
            )
            return True, "IP address is blocked due to too many failed attempts"

        # Check if user is blocked
        if user_id and user_id in self.blocked_users:
            self.audit_logger.log_event(
                event_type="authentication_attempt",
                user_id=user_id,
                email=email,
                ip_address=request.client.host if request.client else "127.0.0.1",
                user_agent=request.headers.get("user-agent", ""),
                details={"reason": "User blocked"},
                success=False,
            )
            return True, "Account is locked due to too many failed attempts"

        # Clean up expired blocks
        current_time = now()
        for key, expiry in list(self.block_expiry.items()):
            if current_time > expiry:
                if key in self.blocked_ips:
                    self.blocked_ips.remove(key)
                if key in self.blocked_users:
                    self.blocked_users.remove(key)
                del self.block_expiry[key]

        # Record the failed attempt
        now_ts = time.time()
        self.failed_attempts[ip].append(now_ts)
        if user_id:
            self.failed_attempts[user_id].append(now_ts)

        # Clean up old attempts (older than 1 hour)
        cutoff = now_ts - 3600
        self.failed_attempts[ip] = [t for t in self.failed_attempts[ip] if t > cutoff]
        if user_id:
            self.failed_attempts[user_id] = [
                t for t in self.failed_attempts[user_id] if t > cutoff
            ]

        # Check if IP should be blocked
        if len(self.failed_attempts[ip]) >= self.max_attempts:
            self.blocked_ips.add(ip)
            self.block_expiry[ip] = current_time + self.block_duration
            self.audit_logger.log_event(
                event_type="account_lockout",
                user_id=user_id,
                email=email,
                ip_address=request.client.host if request.client else "127.0.0.1",
                user_agent=request.headers.get("user-agent", ""),
                details={"reason": "Too many failed login attempts from IP"},
                success=False,
            )
            return True, "IP address is blocked due to too many failed attempts"

        # Check if user should be blocked
        if user_id and len(self.failed_attempts[user_id]) >= self.max_attempts:
            self.blocked_users.add(user_id)
            self.block_expiry[user_id] = current_time + self.block_duration
            self.audit_logger.log_event(
                event_type="account_lockout",
                user_id=user_id,
                email=email,
                ip_address=request.client.host if request.client else "127.0.0.1",
                user_agent=request.headers.get("user-agent", ""),
                details={"reason": "Too many failed login attempts for user"},
                success=False,
            )
            return True, "Account is locked due to too many failed attempts"

        return False, ""

    def reset_failed_attempts(self, user_id: Union[str, UUID]) -> None:
        """Reset failed attempts for a user after successful authentication.

        Args:
            user_id (Union[str, UUID]): The ID of the user
        """
        if user_id in self.failed_attempts:
            del self.failed_attempts[user_id]
        if user_id in self.blocked_users:
            self.blocked_users.remove(user_id)
            if user_id in self.block_expiry:
                del self.block_expiry[user_id]

    def is_blocked(
        self, request: Request, user_id: Optional[Union[str, UUID]] = None
    ) -> Tuple[bool, str]:
        """Check if an IP or user is blocked.

        Args:
            request (Request): The FastAPI request object
            user_id (Optional[str]): The ID of the user

        Returns:
            Tuple[bool, str]: Whether the IP/user is blocked and the reason
        """
        ip = request.client.host if request.client else "127.0.0.1"

        # Clean up expired blocks
        current_time = now()
        for key, expiry in list(self.block_expiry.items()):
            if current_time > expiry:
                if key in self.blocked_ips:
                    self.blocked_ips.remove(key)
                if key in self.blocked_users:
                    self.blocked_users.remove(key)
                del self.block_expiry[key]

        # Check if IP is blocked
        if ip in self.blocked_ips:
            return True, "IP address is blocked due to too many failed attempts"

        # Check if user is blocked
        if user_id and user_id in self.blocked_users:
            return True, "Account is locked due to too many failed attempts"

        return False, ""


# Create global instances
audit_logger = AuditLogger()
brute_force_protection = BruteForceProtection()
