"""Device service for authentication.

This module provides functionality for managing devices used for authentication,
including extracting device information from requests and managing device records.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

import pytz
from fastapi import Request

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.passkey import PasskeyCredential
from peepsapi.auth.models.session_token import SessionToken
from peepsapi.models import now
from peepsapi.services.cosmos_containers import (
    get_passkeys_container,
    get_session_tokens_container,
)
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class DeviceService:
    """Service for device management operations."""

    def __init__(self):
        """Initialize the device service."""
        self.session_tokens_container: CosmosContainer = get_session_tokens_container()
        self.passkeys_container: CosmosContainer = get_passkeys_container()

    def extract_device_info(
        self,
        request: Request,
        override_name: Optional[str] = None,
    ) -> DeviceInfo:
        """Extract device information from a request.

        Args:
            request (Request): The FastAPI request object
            override_name (Optional[str]): Override the auto-detected device name

        Returns:
            DeviceInfo: A standardized device information object
        """
        # Get user agent from request
        user_agent = request.headers.get("user-agent", "Unknown")
        client_ip = request.client.host if request.client else "0.0.0.0"

        # Parse user agent to extract browser, OS, and device type information
        browser_info = self._parse_browser_info(user_agent)
        os_info = self._parse_os_info(user_agent)
        device_type = self._determine_device_type(user_agent)

        # Create device info object
        current_time = now()

        # Use override name or generate from user agent
        device_name = override_name or f"{browser_info} on {os_info}"

        return DeviceInfo(
            name=device_name,
            type=device_type,
            os=os_info,
            browser=browser_info,
            user_agent=user_agent,
            ip=client_ip,
            created_at=current_time,
            last_used_at=current_time,
            is_active=True,
        )

    def get_last_login_device(self, person_id: UUID) -> Optional[DeviceInfo]:
        """Get the device info from the most recent login for a person.

        Args:
            person_id (UUID): The person's ID

        Returns:
            Optional[DeviceInfo]: The device info from the last login, or None if not found
        """
        try:
            # Query for the most recent active session
            query = """
            SELECT TOP 1 c.device_info
            FROM c
            WHERE c.person_id = @person_id
            AND c.is_active = true
            ORDER BY c.created_at DESC
            """
            params = [{"name": "@person_id", "value": person_id}]

            results: List[SessionToken] = self.session_tokens_container.query_models(
                query=query,
                parameters=params,
                model_class=SessionToken,
            )

            if results and results is not [None]:
                return next(iter(results)).device_info

            return None
        except Exception as e:
            logger.error(
                f" Error getting last login device for person {person_id}",
                extra={"error": str(e)},
            )
            return None

    def get_registration_device(self, person_id: UUID) -> Optional[DeviceInfo]:
        """Get the device info from the initial registration for a person.

        Args:
            person_id (UUID): The person's ID

        Returns:
            Optional[DeviceInfo]: The registration device info, or None if not found
        """
        try:
            # Query for the oldest credential for this person
            query = """
            SELECT TOP 1 c.device_info
            FROM c
            WHERE c.person_id = @person_id
            ORDER BY c.created_at ASC
            """
            params = [{"name": "@person_id", "value": person_id}]

            results: List[PasskeyCredential] = self.passkeys_container.query_models(
                query=query,
                parameters=params,
                model_class=PasskeyCredential,
            )

            if results and results is not [None]:
                return next(iter(results)).device_info

            return None
        except Exception as e:
            logger.error(
                f" Error getting registration device for person {person_id}",
                extra={"error": str(e)},
            )
            return None

    def update_device_info(
        self,
        credential_id: str,
        updates: Dict[str, Any],
        person_id: Optional[str] = None,
    ) -> bool:
        """Update device info for a credential.

        Args:
            credential_id (str): The credential ID
            updates (Dict[str, Any]): The updates to apply to device info
            person_id (Optional[str]): Optional person ID for validation

        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            # Get the credential
            credential = self.passkeys_container.read_model(
                id=credential_id,
                partition_key=person_id if person_id else credential_id,
            )

            if not credential:
                logger.warning(f"Credential {credential_id} not found")
                return False

            # Verify person_id if provided
            if person_id and credential.person_id != person_id:
                logger.warning(
                    f"Credential {credential_id} does not belong to person {person_id}"
                )
                return False

            # Update device info
            if not credential.device_info:
                credential.device_info = DeviceInfo(
                    name="Unknown Device",
                    type="unknown",
                    os="Unknown",
                    browser="Unknown",
                    ip="0.0.0.0",
                    created_at=now(),
                    last_used_at=now(),
                    is_active=True,
                )

            # Apply updates
            for key, value in updates.items():
                if hasattr(credential.device_info, key):
                    setattr(credential.device_info, key, value)

            # Save changes
            self.passkeys_container.replace_model(model=credential)
            return True

        except Exception as e:
            logger.error(
                f" Error updating device info for credential {credential_id}",
                extra={"error": str(e)},
            )
            return False

    def get_device_activity(self, person_id: UUID) -> List[Dict[str, Any]]:
        """Get device activity history for a person.

        Args:
            person_id (UUID): The person's ID

        Returns:
            List[Dict[str, Any]]: List of device activity records
        """
        try:
            # Query for all active sessions with device info
            query = """
            SELECT c.device_info, c.created_at, c.last_used_at, c.login_time
            FROM c
            WHERE c.person_id = @person_id
            AND c.is_active = true
            ORDER BY c.last_used_at DESC
            """
            params = [{"name": "@person_id", "value": person_id}]

            results: List[SessionToken] = self.session_tokens_container.query_models(
                query=query,
                parameters=params,
                model_class=SessionToken,
            )

            activity = []
            for result in results:
                if result.device_info:
                    activity.append(
                        {
                            "device": result.device_info,
                            "created_at": datetime.fromisoformat(
                                result.created_at
                            ).replace(tzinfo=pytz.UTC),
                            "expires_at": datetime.fromisoformat(
                                result.expires_at
                            ).replace(tzinfo=pytz.UTC),
                            "login_time": datetime.fromisoformat(
                                result.login_time
                            ).replace(tzinfo=pytz.UTC)
                            if result.login_time
                            else None,
                        }
                    )

            return activity
        except Exception as e:
            logger.error(
                f" Error getting device activity for person {person_id}",
                extra={"error": str(e)},
            )
            return []

    def get_last_login_time(self, person_id: UUID) -> Optional[datetime]:
        """Get the last login time for a person.

        Args:
            person_id (UUID): The person's ID

        Returns:
            Optional[datetime]: The last login time, or None if not found
        """
        try:
            # Query for the most recent active session
            query = """
            SELECT TOP 1 c.login_time
            FROM c
            WHERE c.person_id = @person_id
            AND c.is_active = true
            ORDER BY c.created_at DESC
            """
            params = [{"name": "@person_id", "value": person_id}]

            results: List[SessionToken] = self.session_tokens_container.query_models(
                query=query,
                parameters=params,
                model_class=SessionToken,
            )

            if results and results is not [None]:
                return datetime.fromisoformat(next(iter(results)).login_time).replace(
                    tzinfo=pytz.UTC
                )

            return None
        except Exception as e:
            logger.error(
                f" Error getting last login time for person {person_id}",
                extra={"error": str(e)},
            )
            return None

    def _parse_browser_info(self, user_agent: str) -> str:
        """Parse browser information from a user agent string.

        Args:
            user_agent (str): The user agent string

        Returns:
            str: The browser name
        """
        user_agent = user_agent.lower()

        if "firefox" in user_agent:
            return "Firefox"
        elif "chrome" in user_agent:
            return "Chrome"
        elif "safari" in user_agent:
            return "Safari"
        elif "edge" in user_agent:
            return "Edge"
        else:
            return "Unknown"

    def _parse_os_info(self, user_agent: str) -> str:
        """Parse OS information from a user agent string.

        Args:
            user_agent (str): The user agent string

        Returns:
            str: The OS name
        """
        user_agent = user_agent.lower()

        if "windows" in user_agent:
            return "Windows"
        elif "mac os" in user_agent or "macos" in user_agent:
            return "macOS"
        elif "linux" in user_agent:
            return "Linux"
        elif "android" in user_agent:
            return "Android"
        elif "ios" in user_agent or "iphone" in user_agent or "ipad" in user_agent:
            return "iOS"
        else:
            return "Unknown"

    def _determine_device_type(self, user_agent: str) -> str:
        """Determine device type from a user agent string.

        Args:
            user_agent (str): The user agent string

        Returns:
            str: The device type (mobile, tablet, desktop)
        """
        user_agent = user_agent.lower()

        if "mobile" in user_agent or "android" in user_agent or "iphone" in user_agent:
            return "mobile"
        elif "tablet" in user_agent or "ipad" in user_agent:
            return "tablet"
        else:
            return "desktop"


# Create a global instance of the device service
device_service = DeviceService()
