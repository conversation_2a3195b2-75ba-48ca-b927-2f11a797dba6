"""Business logic for invite management."""

import secrets
from datetime import <PERSON><PERSON><PERSON>
from typing import List
from uuid import UUID, uuid4

from fastapi import BackgroundTasks

from peepsapi.auth.models.invite import Invite, InviteRequest, InviteStatus
from peepsapi.auth.models.invite_quota import InviteQuota<PERSON>hange
from peepsapi.auth.services.stats_service import stats_service
from peepsapi.auth.utils.constants import (
    MAX_INVITE_EXPIRATION_DAYS,
    ChallengeVerb,
    build_challenge_url,
)
from peepsapi.crud.models.person import Email, Person, PhoneNumber
from peepsapi.crud.services.people_services import people_service
from peepsapi.models import now
from peepsapi.models.base import IdentifierType
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.services.email_service import EmailService
from peepsapi.utils.constants import SYSTEM_USER_ID
from peepsapi.utils.error_handling import ValidationError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class InviteService:
    """Service handling the invite lifecycle."""

    def __init__(self) -> None:
        self.email_service = EmailService()

    def _email_in_use(
        self, invite_request: InviteRequest, people_container: CosmosContainer
    ) -> bool:
        people: List[Person] = people_service.get_people_by_identifier(
            identifier_type=invite_request.identifier_type,
            identifier_value=invite_request.identifier_value,
            people_container=people_container,
        )
        return bool(people)

    def send_email_with_status_update(
        self, invite: Invite, invite_container: CosmosContainer
    ):
        """Background task to send email and update invite status."""
        try:
            # Send the email (with retry logic handled in EmailService)
            self.email_service.send_email(
                invite.identifier_value,
                "invite",
                {
                    "user_name": invite.identifier_value,
                    "link": invite.invite_url,
                    "subject": "Peeps Invitation",
                },
            )

            # Update invite status to SENT on successful email delivery
            invite.status = InviteStatus.SENT
            invite.updated_at = now()
            invite_container.patch_model(
                item=invite.id,
                partition_key=invite.id,
                update_fields={
                    "status": invite.status,
                    "updated_at": invite.updated_at,
                    "updated_by": SYSTEM_USER_ID,
                },
                model_class=Invite,
            )
            logger.info(
                "📧 Invite email sent successfully",
                extra={"invite_id": invite.id, "recipient": invite.identifier_value},
            )

        except Exception as e:
            # Log the failure but keep invite status as PENDING for manual retry
            logger.error(
                "❌ Failed to send invite email after all retry attempts",
                extra={
                    "invite_id": invite.id,
                    "recipient": invite.identifier_value,
                    "error": str(e),
                },
            )

    def _send_invite_email_in_background(
        self,
        invite: Invite,
        background_tasks: BackgroundTasks,
        invite_container: CosmosContainer,
    ) -> None:
        """Send invite email and update status on success/failure."""

        background_tasks.add_task(
            self.send_email_with_status_update, invite, invite_container
        )

    def create_invite(
        self,
        creator_id: UUID,
        actor_id: UUID,
        auth_source: str,
        invite_request: InviteRequest,
        protocol: str,
        host: str,
        background_tasks: BackgroundTasks,
        invite_container: CosmosContainer,
        people_container: CosmosContainer,
    ) -> Invite:
        """Create a new invite if the email is unused."""
        logger.info(
            f"🔐 Creating invite by {creator_id} via {'SSO' if auth_source == 'azure_ad' else 'JWT'} auth",
            extra={"invite_request": invite_request, "creator_id": creator_id},
        )
        invite = self.get_valid_invite_by_identifier(invite_request, invite_container)
        if invite is not None:
            invite = self.extend_expiry(
                invite_id=invite.id,
                expires_in_days=invite_request.expires_in_days,
                creator_id=creator_id,
                actor_id=actor_id,
                auth_source=auth_source,
                invite_container=invite_container,
            )
        else:
            if self._email_in_use(invite_request, people_container):
                raise ValidationError(
                    message=f"This {invite_request.identifier_type.value} is already registered",
                    error_code="IDENTIFIER_EXISTS",
                )
            # Decrease inviter quota if not set by an admin
            if auth_source != "azure_ad":
                inviter: Person = people_container.read_model(
                    creator_id, creator_id, model_class=Person
                )
                if inviter.remaining_invites <= 0:
                    raise ValidationError(
                        message="Invite quota exceeded",
                        error_code="QUOTA_EXCEEDED",
                    )
                inviter.remaining_invites -= 1
                people_container.patch_model(
                    item=inviter.id,
                    partition_key=inviter.id,
                    update_fields={
                        "remaining_invites": inviter.remaining_invites,
                    },
                    model_class=Person,
                )
            # Create shell person profile
            person = Person(
                invited_by_id=self.get_invited_by(creator_id, auth_source, actor_id),
                primary_identifier_type=invite_request.identifier_type,
                primary_identifier_value=invite_request.identifier_value,
                member_since=now(),
                emails=[
                    Email(
                        address=invite_request.identifier_value,
                        type="personal",
                        active_since=now(),
                    )
                ]
                if invite_request.identifier_type == IdentifierType.EMAIL
                else [],
                phone_numbers=[
                    PhoneNumber(
                        number=invite_request.identifier_value,
                        type="personal",
                        active_since=now(),
                    )
                ]
                if invite_request.identifier_type == IdentifierType.PHONE
                else [],
            )
            # Save shell person profile
            people_service.create_person(person, people_container)
            logger.info(f"👤 Shell person created", extra={"person_id": person.id})
            logger.info(
                f"👤 actor_id: {actor_id}, azure_ad: {auth_source == 'azure_ad'}, creator_id: {creator_id}"
            )
            token = secrets.token_urlsafe(32)
            invite_url = build_challenge_url(
                protocol, host, ChallengeVerb.REGISTER, token
            )
            invite = Invite(
                id=uuid4(),
                token=token,
                identifier_value=invite_request.identifier_value,
                identifier_type=invite_request.identifier_type,
                person_id=person.id,
                invited_by=[self.get_invited_by(creator_id, auth_source, actor_id)],
                updated_by=self.get_updated_by(creator_id, actor_id, auth_source),
                created_at=now(),
                expires_at=now() + timedelta(days=invite_request.expires_in_days),
                status=InviteStatus.PENDING,
                invite_url=invite_url,
                resend_count=0,
                is_used=False,
            )
            invite_container.create_model(invite)
            logger.info(
                "🔐 Invite token created",
                extra={"invite_id": invite.id, "person_id": invite.person_id},
            )
        if invite_request.identifier_type == IdentifierType.EMAIL:
            self._send_invite_email_in_background(
                invite, background_tasks, invite_container
            )
        elif invite_request.identifier_type == IdentifierType.PHONE:
            logger.info("📜 SMS intergation is not implemented yet")
        return invite

    def resend_invite(
        self,
        creator_id: UUID,
        actor_id: UUID,
        auth_source: str,
        invite_id: UUID,
        background_tasks: BackgroundTasks,
        invite_container: CosmosContainer,
        days: int = MAX_INVITE_EXPIRATION_DAYS,
    ) -> Invite:
        invite: Invite = invite_container.read_model(
            invite_id, invite_id, model_class=Invite
        )
        # Check authorization
        if auth_source != "azure_ad" and creator_id not in invite.invited_by:
            raise ValidationError(
                message="Not authorized to resend this invite",
                error_code="UNAUTHORIZED",
            )
        if invite.status == InviteStatus.CANCELLED:
            raise ValidationError(message="Invite cancelled", error_code="CANCELLED")

        invite.expires_at = now() + timedelta(days=days)
        invite.updated_at = now()
        invite.updated_by = self.get_updated_by(creator_id, actor_id, auth_source)
        invite.invited_by = list(
            set(
                invite.invited_by
                + [self.get_invited_by(creator_id, auth_source, actor_id)]
            )
        )
        invite.resend_count += 1
        invite.status = InviteStatus.PENDING
        invite_container.patch_model(
            item=invite.id,
            partition_key=invite.id,
            update_fields={
                "expires_at": invite.expires_at,
                "updated_at": invite.updated_at,
                "resend_count": invite.resend_count,
                "updated_by": invite.updated_by,
                "invited_by": invite.invited_by,
                "status": invite.status,
            },
            model_class=Invite,
        )
        self._send_invite_email_in_background(
            invite, background_tasks, invite_container
        )
        return invite

    def cancel_invite(
        self,
        invite_id: UUID,
        auth_source: str,
        actor_id: UUID,
        creator_id: UUID,
        invite_container: CosmosContainer,
    ) -> Invite:
        try:
            invite: Invite = invite_container.read_model(
                invite_id, invite_id, model_class=Invite
            )
            if auth_source != "azure_ad" and creator_id not in invite.invited_by:
                raise ValidationError(
                    message="Not authorized to cancel this invite",
                    error_code="UNAUTHORIZED",
                )
            invite.updated_at = now()
            invite.updated_by = self.get_updated_by(creator_id, actor_id, auth_source)
            invite.status = InviteStatus.CANCELLED
            invite_container.patch_model(
                item=invite.id,
                partition_key=invite.id,
                update_fields={
                    "updated_at": invite.updated_at,
                    "updated_by": invite.updated_by,
                    "status": invite.status,
                },
                model_class=Invite,
            )
            return invite
        except Exception as e:
            logger.error(f"Error '{invite_id}'in cancel_invite: {str(e)}")
            raise ValidationError(
                message="Failed to cancel invite", error_code="CANCEL_FAILED"
            )

    def fetch_active_invites_by_creator(
        self, creator_id: UUID, invite_container: CosmosContainer
    ) -> List[Invite]:
        query = "SELECT * FROM c WHERE c.is_used = false AND c.status != 'cancelled' AND ARRAY_CONTAINS(c.invited_by, @c)"
        params = [{"name": "@c", "value": creator_id}]
        return invite_container.query_models(
            query=query, parameters=params, model_class=Invite
        )

    def fetch_all_active_invites(
        self, invite_container: CosmosContainer
    ) -> List[Invite]:
        query = "SELECT * FROM c WHERE c.is_used = false AND c.status != 'cancelled'"
        return invite_container.query_models(
            query=query, parameters=None, model_class=Invite
        )

    def fetch_invite_by_id(
        self, invite_id: UUID, invite_container: CosmosContainer
    ) -> Invite:
        return invite_container.read_model(invite_id, invite_id, model_class=Invite)

    def get_valid_invite_by_identifier(
        self, invite_request: InviteRequest, invite_container: CosmosContainer
    ) -> Invite:
        # Check if identifier is already invited and still valid
        query = "SELECT * FROM c WHERE c.identifier_value = @value AND c.is_used = false AND c.status != 'cancelled'"
        params = [{"name": "@value", "value": invite_request.identifier_value}]
        existing_invites: List[Invite] = invite_container.query_models(
            query=query, parameters=params, model_class=Invite
        )
        return next(iter(existing_invites), None)

    def extend_expiry(
        self,
        invite_id: UUID,
        actor_id: UUID,
        auth_source: str,
        expires_in_days: int,
        creator_id: UUID,
        invite_container: CosmosContainer,
    ) -> Invite:
        invite: Invite = invite_container.read_model(
            invite_id, invite_id, model_class=Invite
        )
        if invite.status == InviteStatus.CANCELLED:
            raise ValidationError(message="Invite cancelled", error_code="CANCELLED")

        invite.expires_at = now() + timedelta(days=expires_in_days)
        # Ensure invited_by is unique (in case of race)
        invite.invited_by = list(
            set(
                invite.invited_by
                + [self.get_invited_by(creator_id, auth_source, actor_id)]
            )
        )
        invite.updated_at = now()
        invite.updated_by = self.get_updated_by(creator_id, actor_id, auth_source)
        invite.resend_count += 1
        invite.status = InviteStatus.PENDING
        invite_container.patch_model(
            item=invite.id,
            partition_key=invite.id,
            update_fields={
                "expires_at": invite.expires_at,
                "updated_at": invite.updated_at,
                "invited_by": invite.invited_by,
                "updated_by": invite.updated_by,
                "resend_count": invite.resend_count,
                "status": invite.status,
            },
            model_class=Invite,
        )
        logger.info(
            "🔧 Extending existing invite expiry",
            person_id=self.get_invited_by(creator_id, auth_source, actor_id),
            extra={
                "invite_id": invite.id,
                "old_expires_at": invite.expires_at,
                "new_expires_at": invite.expires_at,
                "created_by": self.get_invited_by(creator_id, auth_source, actor_id),
            },
        )
        return invite

    # ------------------------------------------------------------------
    # Quota and statistics helpers
    # ------------------------------------------------------------------
    def set_quota(
        self,
        person_id: UUID,
        quota: int,
        actor_id: UUID,
        quota_container: CosmosContainer,
        people_container: CosmosContainer,
    ) -> None:
        """Set a person's remaining invite quota and log the change."""
        current_quota = self.get_quota(person_id, people_container)
        people_container.patch_model(
            item=person_id,
            partition_key=person_id,
            update_fields={
                "remaining_invites": quota,
            },
            model_class=Person,
        )
        log = InviteQuotaChange.create(
            person_id=person_id,
            actor_id=actor_id,
            prior_quota=current_quota,
            new_quota=quota,
        )
        quota_container.create_model(log)

    def get_quota(self, person_id: UUID, people_container: CosmosContainer) -> int:
        person: Person = people_container.read_model(
            person_id, person_id, model_class=Person
        )
        return getattr(person, "remaining_invites", 0)

    def increase_quota(
        self,
        person_id: UUID,
        increment: int,
        actor_id: UUID,
        quota_container: CosmosContainer,
        people_container: CosmosContainer,
    ) -> int:
        current_quota = self.get_quota(person_id, people_container)
        new_quota = current_quota + increment
        people_container.patch_model(
            item=person_id,
            partition_key=person_id,
            update_fields={
                "remaining_invites": new_quota,
            },
            model_class=Person,
        )
        log = InviteQuotaChange.create(
            person_id=person_id,
            actor_id=actor_id,
            prior_quota=current_quota,
            new_quota=new_quota,
        )
        quota_container.create_model(log)
        return new_quota

    def get_usage(
        self,
        person_id: UUID,
        invite_container: CosmosContainer,
    ) -> int:
        query = "SELECT * FROM c WHERE ARRAY_CONTAINS(c.invited_by, @pid)"
        params = [{"name": "@pid", "value": person_id}]
        invites = invite_container.query_models(
            query=query, parameters=params, model_class=Invite
        )
        return len(invites)

    def get_email_stats(
        self,
        invite_container: CosmosContainer,
        hours: int = 24,
    ) -> dict:
        """Return invite email statistics grouped by status."""
        return stats_service.get_email_stats(
            invite_container, Invite, InviteStatus, hours
        )

    def get_invited_by(
        self, creator_id: UUID, auth_source: str, actor_id: UUID
    ) -> UUID:
        logger.info(
            f"👤 creator_id: {creator_id}, auth_source: {auth_source}, actor_id: {actor_id}"
        )
        return (
            creator_id
            if auth_source != "azure_ad"
            else creator_id
            if creator_id != actor_id
            else SYSTEM_USER_ID
        )

    def get_updated_by(
        self, creator_id: UUID, actor_id: UUID, auth_source: str
    ) -> UUID:
        logger.info(
            f"👤 creator_id: {creator_id}, actor_id: {actor_id}, auth_source: {auth_source}"
        )
        return creator_id if auth_source != "azure_ad" else actor_id


invite_service = InviteService()
