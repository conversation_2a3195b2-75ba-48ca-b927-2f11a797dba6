"""Service logic for account recovery."""

from typing import List
from uuid import UUID

from azure.cosmos.exceptions import CosmosResourceNotFoundError
from fastapi import BackgroundTasks

from peepsapi.auth.models.recovery import Recovery, RecoveryStatus
from peepsapi.auth.services.security_service import audit_logger
from peepsapi.auth.services.stats_service import stats_service
from peepsapi.auth.utils.constants import (
    RECOVERY_TOKEN_EXPIRATION,
    ChallengeVerb,
    build_challenge_url,
)
from peepsapi.crud.models.person import Person
from peepsapi.crud.services.people_services import people_service
from peepsapi.models import now
from peepsapi.models.base import IdentifierType
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.services.email_service import EmailService
from peepsapi.utils.error_handling import ResourceNotFoundError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class RecoveryService:
    """Handle recovery token lifecycle."""

    def __init__(self) -> None:
        self.email_service = EmailService()

    # Database helpers -------------------------------------------------
    def _find_active_token(
        self, identifier_value: str, recovery_container: CosmosContainer
    ) -> Recovery | None:
        query = (
            "SELECT * FROM c WHERE c.identifier_value = @e AND c.is_used = false "
            "AND c.status != 'expired'"
        )
        params = [{"name": "@e", "value": identifier_value}]
        tokens = recovery_container.query_models(
            query=query, parameters=params, model_class=Recovery
        )
        token = next(iter(tokens), None)
        return token

    # Public API -------------------------------------------------------
    def initiate_recovery(
        self,
        identifier_type: IdentifierType,
        identifier_value: str,
        protocol: str,
        host: str,
        background_tasks: BackgroundTasks,
        recovery_container: CosmosContainer,
        people_container: CosmosContainer,
    ) -> None:
        """Initiate recovery for the given email."""

        logger.debug(
            f"🎯 Generate recovery token for {identifier_type}={identifier_value}"
        )

        person: Person = None
        try:
            people: List[Person] = people_service.get_people_by_identifier(
                identifier_type, identifier_value, people_container
            )
            person = next(iter(people), None)
        except Exception as e:
            logger.error(f"❌ Unexpected error in generate_recovery_token: {e}")
            logger.error(f"❌ Exception type: {type(e)}")

        if person is None:
            logger.debug(
                f"Recovery requested for unknown {identifier_type}={identifier_value}"
            )
            audit_logger.log_recovery_attempt(
                identifier_type=identifier_type,
                identifier_value=identifier_value,
                success=False,
            )
            return

        token = self._find_active_token(identifier_value, recovery_container)
        if token and token.expires_at > now():
            token = self.update_or_extend_token(token, recovery_container)
        else:
            token = Recovery.create(
                identifier_type=identifier_type,
                identifier_value=identifier_value,
                person_id=person.id,
            )
            recovery_container.create_model(token)

        background_tasks.add_task(
            self._send_email_with_status, token, protocol, host, recovery_container
        )
        audit_logger.log_recovery_attempt(
            identifier_type=identifier_type,
            identifier_value=identifier_value,
            user_id=person.id,
            success=True,
        )

    def update_or_extend_token(
        self, token: Recovery, recovery_container: CosmosContainer
    ) -> Recovery:
        token.expires_at = now() + RECOVERY_TOKEN_EXPIRATION
        token.updated_at = now()
        token.resend_count += 1
        token.status = RecoveryStatus.PENDING
        recovery_container.patch_model(
            item=token.id,
            partition_key=token.id,
            update_fields={
                "expires_at": token.expires_at,
                "updated_at": token.updated_at,
                "resend_count": token.resend_count,
                "status": token.status,
            },
            model_class=Recovery,
        )
        return token

    def validate_and_consume_token(
        self, token_value: str, recovery_container: CosmosContainer
    ) -> Recovery | None:
        query = "SELECT * FROM c WHERE c.token = @t"
        params = [{"name": "@t", "value": token_value}]
        records = recovery_container.query_models(
            query=query, parameters=params, model_class=Recovery
        )
        token = next(iter(records), None)
        if not token:
            return None
        if token.expires_at <= now() or token.is_used:
            return None

        token.is_used = True
        token.used_at = now()
        token.status = RecoveryStatus.CONSUMED
        recovery_container.patch_model(
            item=token.id,
            partition_key=token.id,
            update_fields={
                "is_used": token.is_used,
                "used_at": token.used_at,
                "status": token.status,
            },
            model_class=Recovery,
        )
        return token

    # Email ------------------------------------------------------------
    def _send_email_with_status(
        self,
        token: Recovery,
        protocol: str,
        host: str,
        recovery_container: CosmosContainer,
    ) -> None:
        try:
            recovery_url = build_challenge_url(
                protocol, host, ChallengeVerb.RECOVER, token.token
            )
            logger.info(f"🔗 Recovery URL: {recovery_url}")
            self.email_service.send_email(
                token.identifier_value,
                "recovery",
                {
                    "user_name": token.identifier_value,
                    "link": recovery_url,
                    "subject": "Account recovery",
                },
            )
            token.status = RecoveryStatus.SENT
            recovery_container.patch_model(
                item=token.id,
                partition_key=token.id,
                update_fields={"status": token.status},
                model_class=Recovery,
            )
        except Exception as exc:  # pragma: no cover - logging only
            logger.error(
                "Failed to send recovery email",
                extra={"error": str(exc), "identifier_value": token.identifier_value},
            )

    def get_active_tokens(
        self, recovery_tokens_container: CosmosContainer
    ) -> list[Recovery]:
        """Return all non-expired, unused recovery tokens."""
        query = "SELECT * FROM c WHERE c.is_used = false AND c.status != 'expired'"
        return recovery_tokens_container.query_models(
            query=query, parameters=None, model_class=Recovery
        )

    def cancel_token(
        self, token_id: UUID, recovery_tokens_container: CosmosContainer
    ) -> None:
        try:
            token: Recovery = recovery_tokens_container.read_model(
                token_id, token_id, model_class=Recovery
            )
        except CosmosResourceNotFoundError:
            raise ResourceNotFoundError(message="Recovery token not found")
        token.status = RecoveryStatus.EXPIRED
        token.is_used = True
        token.updated_at = now()
        recovery_tokens_container.patch_model(
            item=token.id,
            partition_key=token.id,
            update_fields={
                "status": token.status,
                "is_used": token.is_used,
                "updated_at": token.updated_at,
            },
            model_class=Recovery,
        )

    def get_email_stats(self, container: CosmosContainer, hours: int = 24) -> dict:
        return stats_service.get_email_stats(container, Recovery, RecoveryStatus, hours)


recovery_service = RecoveryService()
