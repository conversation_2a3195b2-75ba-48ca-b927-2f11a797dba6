"""FIDO2 challenge service for passkey authentication.

This module provides utility functions for FIDO2 operations, including
generating and validating challenges for registration and authentication.
Following the Yubico FIDO2 example pattern.
"""

import json
import secrets
from dataclasses import asdict, replace
from datetime import timed<PERSON>ta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union
from uuid import UUID

from fido2.server import Fido2Server  # type: ignore
from fido2.utils import websafe_decode, websafe_encode
from fido2.webauthn import (  # type: ignore
    AuthenticationResponse,
    AuthenticatorData,
    AuthenticatorSelectionCriteria,
    CollectedClientData,
    CredentialCreationOptions,
    CredentialRequestOptions,
    PublicKeyCredentialDescriptor,
    PublicKeyCredentialType,
    PublicKeyCredentialUserEntity,
    RegistrationResponse,
    webauthn_json_mapping,
)

from peepsapi import config
from peepsapi.auth.models.authentication import (
    AuthenticationChallenge,
    AuthenticationCredentialData,
)
from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.invite import Invite
from peepsapi.auth.models.passkey import CredentialSource, PasskeyCredential
from peepsapi.auth.models.recovery import Recovery
from peepsapi.auth.models.registration import (
    RegistrationChallenge,
    RegistrationCredentialData,
)
from peepsapi.auth.utils.constants import CHALLENGE_TIMEOUT_MS, ChallengeMode
from peepsapi.models import now, to_utc_datetime
from peepsapi.services.cosmos_containers import (
    get_authentication_challenges_container,
    get_passkeys_container,
    get_registration_challenges_container,
)
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.error_handling import ValidationError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class ChallengeService:
    """Service for FIDO2 challenge operations following the Yubico FIDO2 example pattern."""

    def __init__(self):
        """Initialize the FIDO2 service."""
        # Load FIDO2 configuration
        self.rp_id = config.get("WEBAUTHN_RP_ID")
        self.rp_name = config.get("WEBAUTHN_RP_NAME")
        self.rp_origin = config.get("WEBAUTHN_RP_ORIGIN")

        # Validate FIDO2 configuration
        if not self.rp_id or not self.rp_name or not self.rp_origin:
            raise ValueError(
                "FIDO2 configuration is incomplete. "
                "Please set WEBAUTHN_RP_ID, WEBAUTHN_RP_NAME, and WEBAUTHN_RP_ORIGIN "
                "environment variables."
            )

        # Create the server with a simple dictionary for RP
        self.server = Fido2Server(
            {"id": self.rp_id, "name": self.rp_name},
            attestation="none",
        )

        webauthn_json_mapping.enabled = True

        # Initialize containers
        self.registration_challenges_container: CosmosContainer = (
            get_registration_challenges_container()
        )
        self.authentication_challenges_container: CosmosContainer = (
            get_authentication_challenges_container()
        )
        self.passkeys_container: CosmosContainer = get_passkeys_container()

    async def create_challenge(
        self,
        mode: ChallengeMode,
        person_id: UUID,
        person_name: Optional[str] = None,
        display_name: Optional[str] = None,
        token: Optional[Invite | Recovery] = None,
        existing_credentials: Optional[List[PasskeyCredential]] = None,
    ) -> Tuple[Dict, Union[RegistrationChallenge, AuthenticationChallenge]]:
        """Create a WebAuthn challenge for the specified mode.

        Args:
            mode (ChallengeMode): The challenge mode (registration, authentication, or recovery)
            person_id (UUID): The person's ID
            person_name (Optional[str]): The person's name (required for recovery)
            display_name (Optional[str]): The person's display name (required for recovery)
            token (Optional[Invite]): The invite token (for registration/recovery)
            existing_credentials (Optional[List[PasskeyCredential]]): Existing credentials (for authentication)

        Returns:
            Tuple[Dict, Union[RegistrationChallenge, AuthenticationChallenge]]: The options and challenge record

        Raises:
            ValidationError: If required parameters are missing
        """
        if mode in [ChallengeMode.REGISTRATION, ChallengeMode.RECOVERY]:
            if mode == ChallengeMode.RECOVERY and (not person_name or not display_name):
                raise ValidationError(
                    "person_name and display_name are required for registration/recovery"
                )

            options, challenge = self.create_registration_options(
                person_id=person_id,
                person_name=person_name
                if person_name
                else (display_name if display_name else "User"),
                display_name=display_name
                if display_name
                else (person_name if person_name else "User"),
                token=token,
                mode=mode,
            )

            # Save challenge
            self.registration_challenges_container.create_model(challenge)

        elif mode == ChallengeMode.AUTHENTICATION:
            if not existing_credentials:
                # Try to find existing credentials
                existing_credentials = self.find_credentials_by_id_or_person_id(
                    person_id=person_id
                )
            if not existing_credentials:
                raise ValidationError("No credentials found for authentication")

            options, challenge = self.create_authentication_options(
                passkeys=existing_credentials
            )
            # Save challenge
            self.authentication_challenges_container.create_model(challenge)

        else:
            raise ValidationError(f"Invalid challenge mode: {mode}")

        return options, challenge

    def create_registration_options(
        self,
        person_id: UUID,
        token: Invite | Recovery,
        mode: ChallengeMode,
        person_name: Optional[str] = None,
        display_name: Optional[str] = None,
    ) -> Tuple[CredentialCreationOptions, RegistrationChallenge]:
        """Create FIDO2 options following the Yubico FIDO2 example pattern.

        Args:
            person_id (UUID): The person's ID
            person_name (str): The person's name (username)
            display_name (str): The person's display name
            token (Invite|Recovery): The invite token
            mode (ChallengeMode): The challenge mode (registration, authentication, or recovery)

        Returns:
            Tuple[CredentialCreationOptions, RegistrationChallenge]: The options and challenge record
        """
        user = PublicKeyCredentialUserEntity(
            id=str(person_id).encode(),
            name=person_name if person_name else person_id,
            display_name=display_name if display_name else person_id,
        )

        credentials = []
        if mode == ChallengeMode.RECOVERY:
            self.deactivate_credentials_by_id_or_person_id(person_id=person_id)
        else:
            credentials = self.find_credentials_by_id_or_person_id(
                person_id=person_id,
            )
            credentials = self.get_credentials(credentials)
        options, state = self.server.register_begin(
            user=user,
            credentials=credentials,
        )

        options = self.create_registration_options_extended(create_options=options)

        authenticator_selection = (
            {
                k: (v.value if isinstance(v, Enum) else v)
                for k, v in asdict(options.public_key.authenticator_selection).items()
            }
            if options.public_key.authenticator_selection
            else None
        )

        # Create challenge record
        challenge_record = RegistrationChallenge(
            id=secrets.token_hex(16),
            challenge=state["challenge"],
            timeout=CHALLENGE_TIMEOUT_MS,
            rp_id=self.rp_id,
            rp_name=self.rp_name,
            user_name=person_name if person_name else person_id,
            user_display_name=display_name if display_name else person_id,
            created_at=now(),
            expires_at=now() + timedelta(milliseconds=CHALLENGE_TIMEOUT_MS),
            person_id=person_id,
            state_dict_json=json.dumps(state),  # Store state for verification
            attestation=options.public_key.attestation,
            authenticator_selection=authenticator_selection,
            invite_token=token.token,
            is_recovery=mode == ChallengeMode.RECOVERY,
        )

        logger.info(
            f"🛠️ Created {'recovery' if mode == ChallengeMode.RECOVERY else 'registration'} challenge for person ID: {person_id}"
        )

        return options, challenge_record

    def create_authentication_options(
        self, passkeys: List[PasskeyCredential]
    ) -> Tuple[CredentialRequestOptions, AuthenticationChallenge]:
        """Create FIDO2 options following the Yubico FIDO2 example pattern.

        Args:
            credentials (List[PasskeyCredential]): The user's credentials

        Returns:
            Tuple[Dict, Union[AuthenticationChallenge, RegistrationChallenge]]: The options and challenge record
        """
        logger.info(
            f"🛠️ Creating authentication options for person ID: {passkeys[0].person_id}"
        )
        credentials = self.get_credentials(passkeys)
        options, state = self.server.authenticate_begin(
            credentials=credentials,
        )

        options = self.create_authentication_options_extended(create_options=options)

        # Create challenge record
        challenge_record = AuthenticationChallenge(
            id=secrets.token_hex(16),
            challenge=state["challenge"],
            timeout=CHALLENGE_TIMEOUT_MS,
            rp_id=self.rp_id,
            rp_name=self.rp_name,
            created_at=now(),
            expires_at=now() + timedelta(milliseconds=CHALLENGE_TIMEOUT_MS),
            person_id=passkeys[0].person_id,
            state_dict_json=json.dumps(state),  # Store state for verification
            allow_credentials=[
                {
                    "id": websafe_encode(cred.id),
                    "type": "public-key",
                    "transports": "internal",
                }
                for cred in credentials
            ],
        )

        logger.info(
            f"🛠️ Authentication options created for person ID: {passkeys[0].person_id}"
        )
        return options, challenge_record

    def verify_registration_response_data(
        self,
        credential_data: RegistrationCredentialData,
        device_info: DeviceInfo,
    ) -> Optional[PasskeyCredential]:
        """Verify FIDO2 registration response and create credential following the Yubico FIDO2 example pattern.

        Args:
            credential_data (RegistrationCredentialData): The credential data from the client
            device_info (DeviceInfo): The device information

        Returns:
            Optional[PasskeyCredential]: The created credential if verification succeeds
        """
        try:
            # extract challenge from credential data
            raw_id = websafe_decode(credential_data.id)
            attestation_object_bytes = websafe_decode(
                credential_data.response.attestation_object
            )
            client_data_bytes = websafe_decode(
                credential_data.response.client_data_json
            )
            client_data = CollectedClientData(client_data_bytes)
            challenge_b64 = websafe_encode(client_data.challenge)

            challenge_container = get_registration_challenges_container()
            # fetch challenge record
            challenge_records = challenge_container.query_models(
                query="SELECT * FROM c WHERE c.challenge = @challenge",
                model_class=RegistrationChallenge,
                parameters=[{"name": "@challenge", "value": challenge_b64}],
            )

            challenge_record = next(iter(challenge_records), None)
            logger.debug(f"🔍 Challenge record: {challenge_record}")

            if challenge_record is None:
                raise ValidationError(
                    message="No registration challenge found",
                    error_code="CHALLENGE_NOT_FOUND",
                )

            # Check if challenge has expired
            if now() > to_utc_datetime(challenge_record.expires_at):
                raise ValidationError(
                    message="Challenge has exdpired",
                    error_code="CHALLENGE_EXPIRED",
                )

            # Parse the state from the challenge record
            if not challenge_record.state_dict_json:
                raise ValidationError(
                    message="Challenge record is invalid",
                    error_code="CHALLENGE_INVALID",
                )

            try:
                state = json.loads(challenge_record.state_dict_json)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse challenge state: {e}")
                return None

            # Create a RegistrationResponse object
            try:
                auth_data = self.get_auth_data_by_register_complete(
                    raw_id=raw_id,
                    client_data_bytes=client_data_bytes,
                    attestation_object_bytes=attestation_object_bytes,
                    state=state,
                )

                # Create the credential using our helper method
                credential = PasskeyCredential.init_from_auth_data(
                    id=websafe_encode(auth_data.credential_data.credential_id),
                    raw_id=raw_id,
                    client_data_bytes=client_data_bytes,
                    attestation_object_bytes=attestation_object_bytes,
                    state_dict_json=json.dumps(state),
                    person_id=challenge_record.person_id,
                    rp_id=self.rp_id,
                    sign_count=auth_data.counter,
                    device_info=device_info,
                )
                logger.info(
                    f"✅ Successfully created credential for person ID: {challenge_record.person_id}"
                )

                # Cleanup used challenge
                challenge_container.delete_item(
                    challenge_record.id, challenge_record.person_id
                )

                return credential

            except Exception as e:
                logger.error(f"Error in registration verification: {e}")
                return None

        except Exception as e:
            logger.error(f"Error verifying registration response: {e}")
            return None

    def verify_authentication_response_data(
        self,
        credential: PasskeyCredential,
        request_body: AuthenticationCredentialData,
        challenge: Optional[AuthenticationChallenge] = None,
    ) -> PasskeyCredential:
        """Verify FIDO2 authentication response following the Yubico FIDO2  pattern.

        Args:
            credential : Passkey Credential object
            request: The request object
            device_info: Device Info object
            challenge AuthenticationChallenge: Authentication Challenge Object

        Returns:
            PasskeyCredential: The verified credential if authentication is successful, None otherwise
        """
        try:
            request_cred = request_body
            response = AuthenticationResponse(
                request_cred.raw_id,
                {
                    "clientDataJSON": websafe_decode(
                        request_cred.response.client_data_json
                    ),
                    "authenticatorData": websafe_decode(
                        request_cred.response.authenticator_data
                    ),
                    "signature": websafe_decode(request_cred.response.signature),
                    "userHandle": websafe_decode(request_cred.response.user_handle)
                    if request_cred.response.user_handle
                    else None,
                },
            )
            auth_data: AuthenticatorData = response.response.authenticator_data
            sign_count = auth_data.counter
            cred_source = self.get_credential_source_from_passkey_credential(credential)

            # Complete authentication
            self.server.authenticate_complete(
                json.loads(challenge.state_dict_json), [cred_source], response
            )
            if sign_count != credential.sign_count:
                logger.critical(
                    f"☢️ Sign count does not match: {sign_count} != {credential.sign_count}",
                    extra={
                        "sign_count": sign_count,
                        "credential_sign_count": credential.sign_count,
                        "person_id": credential.person_id,
                        "credential_id": credential.id,
                        "device_info": credential.device_info,
                    },
                )

            credential.update_at = now()
            credential.device_info.last_used_at = now()
            credential.sign_count = sign_count

            return credential

        except Exception as e:
            logger.error(f"Error verifying authentication response: {e}")
            return None

    async def verify_challenge(
        self,
        mode: ChallengeMode,
        credential_data: Union[
            RegistrationCredentialData, AuthenticationCredentialData
        ],
        device_info: DeviceInfo,
    ) -> PasskeyCredential:
        """Verify a WebAuthn challenge response.

        Args:
            mode (ChallengeMode): The challenge mode (registration, authentication, or recovery)
            credential_data (Union[RegistrationCredentialData, AuthenticationCredentialData]): The credential data from the client
            device_info (DeviceInfo): The device information

        Returns:
            PasskeyCredential:  For registration/recovery, returns the created credential.
                                For authentication, returns the verified credential.

        Raises:
            ValidationError: If verification fails or required parameters are missing
        """
        if mode in [ChallengeMode.REGISTRATION, ChallengeMode.RECOVERY]:
            # For registration and recovery, verify registration response
            credential = self.verify_registration_response_data(
                credential_data=credential_data,
                device_info=device_info,
            )

            if not credential:
                raise ValidationError("Registration verification failed")

            return credential

        elif mode == ChallengeMode.AUTHENTICATION:
            # Extract credential ID from the request
            raw_id = credential_data.id
            if not raw_id:
                raise ValidationError("Missing credential ID")

            # Find the credential
            credentials: List[PasskeyCredential] = self.find_credentials_by_id(raw_id)
            if not credentials:
                raise ValidationError("No credentials found for authentication")

            # Get person_id from credential
            credential = next(iter(credentials))

            # Find the latest challenge for this person
            query = """
                SELECT * FROM c
                WHERE c.person_id = @person_id
                AND EXISTS(
                    SELECT VALUE a FROM a IN c.allow_credentials
                    WHERE a.id = @credential_id
                )
                ORDER BY c.created_at DESC
                """
            params = [
                {"name": "@person_id", "value": credential.person_id},
                {"name": "@credential_id", "value": raw_id},
            ]
            challenges: List[
                AuthenticationChallenge
            ] = self.authentication_challenges_container.query_models(
                model_class=AuthenticationChallenge,
                query=query,
                parameters=params,
            )
            if not challenges or challenges == [None]:
                raise ValidationError("No recent challenge found")

            # Verify authentication response
            credential = self.verify_authentication_response_data(
                credential=credential,
                request_body=credential_data,
                challenge=next(iter(challenges)),
            )

            if not credential:
                raise ValidationError("Authentication verification failed")

            return credential

        else:
            raise ValidationError(f"Invalid challenge mode: {mode}")

    def create_registration_options_extended(
        self,
        create_options: CredentialCreationOptions,
        *,
        timeout: int = CHALLENGE_TIMEOUT_MS,
        resident_key: str = "preferred",
        user_verification: str = "preferred",
        authenticator_attachment: str = "platform",
        extensions: dict = {"credProps": True, "uvm": True},
    ):
        """Mutates FIDO2 creation options with authenticator selection and extensions."""
        create_options = replace(
            create_options,
            public_key=replace(
                create_options.public_key,
                timeout=timeout,
                extensions=extensions,
                authenticator_selection=AuthenticatorSelectionCriteria(
                    resident_key=resident_key,
                    user_verification=user_verification,
                    authenticator_attachment=authenticator_attachment,
                ),
            ),
        )

        return create_options

    def create_authentication_options_extended(
        self,
        create_options: CredentialRequestOptions,
        *,
        timeout: int = CHALLENGE_TIMEOUT_MS,
        extensions: dict = {"credProps": True, "uvm": True},
    ):
        """Mutates FIDO2 creation options with authenticator selection and extensions."""
        create_options = replace(
            create_options,
            public_key=replace(
                create_options.public_key,
                timeout=timeout,
                extensions=extensions,
            ),
        )

        return create_options

    def get_credential_source_from_passkey_credential(
        self, credential: PasskeyCredential
    ) -> CredentialSource:
        """Convert the credential to a CredentialSource object.

        Returns:
            CredentialSource: A CredentialSource object
        """
        result = RegistrationResponse(
            credential.raw_id,
            {
                "clientDataJSON": websafe_decode(credential.client_data_bytes),
                "attestationObject": websafe_decode(
                    credential.attestation_object_bytes
                ),
            },
        )
        state = json.loads(credential.state_dict_json)
        auth_data = self.server.register_complete(state, result)

        return CredentialSource(
            credential_id=auth_data.credential_data.credential_id,
            public_key=auth_data.credential_data.public_key,
            rp_id=credential.rp_id,
            user_handle=str(credential.person_id).encode().decode(),
            sign_count=credential.sign_count,
        )

    def get_auth_data_by_register_complete(
        self,
        raw_id: bytes,
        client_data_bytes: bytes,
        attestation_object_bytes: bytes,
        state: dict,
    ) -> AuthenticatorData:
        """Get the authenticator data from the registration response.

        Args:
            raw_id (bytes): The raw ID
            client_data_bytes (bytes): The client data bytes
            attestation_object_bytes (bytes): The attestation object bytes
            state (dict): The state

        Returns:
            AuthenticatorData: The authenticator data
        """
        result = RegistrationResponse(
            raw_id,
            {
                "clientDataJSON": client_data_bytes,
                "attestationObject": attestation_object_bytes,
            },
        )
        auth_data = self.server.register_complete(state, result)
        return auth_data

    def get_credentials(
        self, passkeys: list[PasskeyCredential]
    ) -> list[PublicKeyCredentialDescriptor]:
        """Convert a list of PasskeyCredentials to a list of PublicKeyCredentialDescriptors.

        Args:
            passkeys (list): A list of PasskeyCredential objects

        Returns:
            list[PublicKeyCredentialDescriptor]: A list of PublicKeyCredentialDescriptors
        """
        public_key_creds = []
        for cred in passkeys:
            auth_data = self.get_auth_data_by_register_complete(
                raw_id=websafe_decode(cred.raw_id),
                client_data_bytes=websafe_decode(cred.client_data_bytes),
                attestation_object_bytes=websafe_decode(cred.attestation_object_bytes),
                state=json.loads(cred.state_dict_json),
            )
            public_key_creds.append(
                PublicKeyCredentialDescriptor(
                    type=PublicKeyCredentialType.PUBLIC_KEY,
                    id=auth_data.credential_data.credential_id,
                )
            )
        return public_key_creds

    @staticmethod
    def find_credentials_by_id(credential_id: str) -> list[PasskeyCredential]:
        """Find a credential by ID.

        Args:
            credential_id (str): The credential ID

        Returns:
            List[PasskeyCredential]: The credential if found otherwise [None]
        """
        passkeys_container = get_passkeys_container()
        if not credential_id:
            return [None]

        # Find by credential ID
        query = "SELECT * FROM c WHERE c.id = @id"
        params = [{"name": "@id", "value": credential_id}]
        partition_key = None

        return list(
            passkeys_container.query_models(
                model_class=PasskeyCredential,
                query=query,
                parameters=params,
                partition_key=partition_key,
            )
        )

    @staticmethod
    def find_credentials_by_id_or_person_id(
        credential_id: str = None, person_id: UUID = None
    ) -> list[PasskeyCredential]:
        """Find a credential by ID or person ID.

        Args:
            credential_id (str, optional): The credential ID
            person_id (UUID, optional): The person ID

        Returns:
            Optional[PasskeyCredential]: The credential if found
        """
        passkeys_container = get_passkeys_container()
        if not credential_id and not person_id:
            return [None]

        if credential_id:
            # Find by credential ID
            query = "SELECT * FROM c WHERE c.id = @id AND c.is_active = true"
            params = [{"name": "@id", "value": credential_id}]
            partition_key = None
        else:
            # Find by person ID
            query = (
                "SELECT * FROM c WHERE c.person_id = @person_id AND c.is_active = true"
            )
            params = [{"name": "@person_id", "value": str(person_id)}]
            partition_key = str(person_id)

        return list(
            passkeys_container.query_models(
                model_class=PasskeyCredential,
                query=query,
                parameters=params,
                partition_key=partition_key,
            )
        )

    @staticmethod
    def deactivate_credentials_by_id_or_person_id(
        credential_id: str = None, person_id: UUID = None
    ) -> None:
        """Deactivate a credential by ID or person ID.

        Args:
            credential_id (str, optional): The credential ID
            person_id (UUID, optional): The person ID

        Returns:
            Optional[PasskeyCredential]: The credential if found
        """
        passkeys_container = get_passkeys_container()
        if not credential_id and not person_id:
            return

        if credential_id:
            # Find by credential ID
            query = "SELECT * FROM c WHERE c.id = @id"
            params = [{"name": "@id", "value": credential_id}]
            partition_key = None
        else:
            # Find by person ID
            query = "SELECT * FROM c WHERE c.person_id = @person_id"
            params = [{"name": "@person_id", "value": str(person_id)}]
            partition_key = str(person_id)

        credentials = passkeys_container.query_models(
            model_class=PasskeyCredential,
            query=query,
            parameters=params,
            partition_key=partition_key,
        )

        for credential in credentials:
            credential.is_active = False
            passkeys_container.patch_model(
                item=credential.id,
                partition_key=credential.person_id,
                update_fields={
                    "is_active": False,
                },
                model_class=PasskeyCredential,
            )


# Create a global instance of the challenge service
challenge_service = ChallengeService()
