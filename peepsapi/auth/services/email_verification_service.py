"""Service logic for email verification."""

import secrets
from uuid import UUID, uuid4

from fastapi import BackgroundTasks

from peepsapi.auth.models.email_verification import (
    EmailVerification,
    EmailVerificationRequest,
    VerificationStatus,
)
from peepsapi.auth.services.stats_service import stats_service
from peepsapi.auth.utils.constants import (
    VERIFICATION_EXPIRATION,
    VERIFICATION_URL,
    VerificationType,
)
from peepsapi.crud.models.person import Person
from peepsapi.models import now
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.services.email_service import EmailService
from peepsapi.utils.error_handling import ResourceNotFoundError, ValidationError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


def build_verification_url(
    protocol: str, host: str, type: VerificationType, token: str
) -> str:
    return VERIFICATION_URL.format(
        protocol=protocol, host=host, type=type.value.lower(), token=token
    )


class EmailVerificationService:
    """Business logic for email verification."""

    def __init__(self) -> None:
        self.email_service = EmailService()

    def _send_verification_email(
        self,
        record: EmailVerification,
        protocol: str,
        host: str,
        email_verification_container: CosmosContainer,
    ) -> None:
        try:
            self.email_service.send_email(
                record.email,
                "verification",
                {
                    "user_name": record.email,
                    "link": build_verification_url(
                        protocol, host, VerificationType.EMAIL, record.token
                    ),
                    "subject": "Verify your email",
                },
            )
            record.status = VerificationStatus.SENT
            email_verification_container.patch_model(
                item=record.id,
                partition_key=record.id,
                update_fields={
                    "status": record.status,
                    "updated_at": now(),
                },
                model_class=EmailVerification,
            )
        except Exception as exc:  # pragma: no cover - logging only
            logger.error(
                "❌ Failed to send verification email",
                extra={"error": str(exc), "email": record.email},
            )

    def initiate_verification(
        self,
        person_id: UUID,
        request: EmailVerificationRequest,
        protocol: str,
        host: str,
        background_tasks: BackgroundTasks,
        email_verification_container: CosmosContainer,
        people_container: CosmosContainer,
    ) -> EmailVerification:
        person: Person = people_container.read_model(
            person_id, person_id, model_class=Person
        )
        person_email = None
        for email in person.emails:
            if email.address == request.email:
                person_email = email
                break
        if person_email is None:
            raise ResourceNotFoundError(
                message="Email not found", error_code="NOT_FOUND"
            )
        if person_email.verified:
            raise ValidationError(
                message="Already verified", error_code="ALREADY_VERIFIED"
            )
        token = secrets.token_urlsafe(32)
        record = EmailVerification(
            id=uuid4(),
            token=token,
            email=request.email,
            person_id=person_id,
            created_at=now(),
            expires_at=now() + VERIFICATION_EXPIRATION,
        )
        email_verification_container.create_model(record)
        background_tasks.add_task(
            self._send_verification_email,
            record,
            protocol,
            host,
            email_verification_container,
        )
        return record

    def resend_verification(
        self,
        person_id: UUID,
        verification_id: UUID,
        protocol: str,
        host: str,
        background_tasks: BackgroundTasks,
        email_verification_container: CosmosContainer,
    ) -> EmailVerification:
        record: EmailVerification = email_verification_container.read_model(
            verification_id, verification_id, model_class=EmailVerification
        )
        if record.person_id != person_id:
            raise ResourceNotFoundError(
                message="Verification not found", error_code="NOT_FOUND"
            )
        if record.status == VerificationStatus.VERIFIED:
            raise ValidationError(
                message="Already verified", error_code="ALREADY_VERIFIED"
            )
        record.token = secrets.token_urlsafe(32)
        record.expires_at = now() + VERIFICATION_EXPIRATION
        record.updated_at = now()
        record.resend_count += 1
        record.status = VerificationStatus.PENDING
        email_verification_container.patch_model(
            item=record.id,
            partition_key=record.id,
            update_fields={
                "token": record.token,
                "expires_at": record.expires_at,
                "updated_at": record.updated_at,
                "resend_count": record.resend_count,
                "status": record.status,
            },
            model_class=EmailVerification,
        )
        background_tasks.add_task(
            self._send_verification_email,
            record,
            protocol,
            host,
            email_verification_container,
        )
        return record

    def verify_code_or_link(
        self,
        token: str,
        person_id: UUID,
        email_verification_container: CosmosContainer,
        people_container: CosmosContainer,
    ) -> EmailVerification:
        query = "SELECT * FROM c WHERE c.token = @t"
        params = [{"name": "@t", "value": token}]
        records = email_verification_container.query_models(
            query=query, parameters=params, model_class=EmailVerification
        )
        record = next(iter(records), None)
        if not record:
            raise ValidationError(message="Invalid token", error_code="INVALID")
        if record.person_id != person_id:
            raise ResourceNotFoundError(
                message="Verification not found", error_code="NOT_FOUND"
            )
        if record.is_used or record.status == VerificationStatus.VERIFIED:
            raise ValidationError(message="Token already used", error_code="USED")
        if record.expires_at <= now():
            record.status = VerificationStatus.EXPIRED
            record.updated_at = now()
            email_verification_container.patch_model(
                item=record.id,
                partition_key=record.id,
                update_fields={
                    "status": record.status,
                    "updated_at": record.updated_at,
                },
                model_class=EmailVerification,
            )
            raise ValidationError(message="Token expired", error_code="EXPIRED")

        record.status = VerificationStatus.VERIFIED
        record.is_used = True
        record.verified_at = now()
        record.updated_at = now()
        record.attempts += 1
        email_verification_container.patch_model(
            item=record.id,
            partition_key=record.id,
            update_fields={
                "status": record.status,
                "is_used": record.is_used,
                "verified_at": record.verified_at,
                "attempts": record.attempts,
                "updated_at": record.updated_at,
            },
            model_class=EmailVerification,
        )

        person: Person = people_container.read_model(
            record.person_id, record.person_id, model_class=Person
        )
        for email in person.emails:
            if email.address == record.email:
                email.verified = True
        people_container.patch_model(
            item=person.id,
            partition_key=person.id,
            update_fields={"emails": [e.model_dump() for e in person.emails]},
            model_class=Person,
        )
        return record

    def fetch_verification_status(
        self,
        person_id: UUID,
        verification_id: UUID,
        email_verification_container: CosmosContainer,
    ) -> EmailVerification:
        record = email_verification_container.read_model(
            verification_id, verification_id, model_class=EmailVerification
        )
        if record.person_id != person_id:
            raise ResourceNotFoundError(
                message="Verification not found", error_code="NOT_FOUND"
            )
        return record

    def get_email_stats(
        self, email_verification_container: CosmosContainer, hours: int = 24
    ) -> dict:
        return stats_service.get_email_stats(
            email_verification_container,
            EmailVerification,
            VerificationStatus,
            hours,
        )


email_verification_service = EmailVerificationService()
