from datetime import timedelta
from enum import Enum
from typing import Type

from peepsapi.models import now
from peepsapi.services.cosmos_db import CosmosContainer


class StatsService:
    """Utility service for computing email stats."""

    @staticmethod
    def get_email_stats(
        container: CosmosContainer,
        model_class: Type,
        status_enum: Type[Enum],
        hours: int = 24,
    ) -> dict:
        """Return counts for each status in the given time window."""
        since = now() - timedelta(hours=hours)
        query = "SELECT * FROM c WHERE c.created_at >= @since"
        params = [{"name": "@since", "value": since.isoformat()}]
        records = container.query_models(
            query=query, parameters=params, model_class=model_class
        )
        stats = {status.value: 0 for status in status_enum}
        stats["total"] = 0
        for rec in records:
            if getattr(rec, "created_at", now()) < since:
                continue
            stats["total"] += 1
            status = rec.status.value if isinstance(rec.status, Enum) else rec.status
            if status in stats:
                stats[status] += 1
        return stats


stats_service = StatsService()
