"""Constants for authentication.

This module provides constants used throughout the authentication system,
including cookie names, token expiration times, and other configuration values.
"""
import datetime
from enum import Enum

# <PERSON>ie names
OVERRIDE_PERSON_ID_COOKIE_NAME = (
    "x-peeps-id"  # <PERSON><PERSON> used to override person ID when using Azure AD auth
)
OVERRIDE_AUTH_SOURCE_COOKIE_NAME = (
    "x-peeps-auth-source"  # <PERSON><PERSON> used to override auth source
)
SESSION_TOKEN_COOKIE_NAME = "session_token"
AZURE_AD_COOKIE_NAME = "azure_ad_session_token"  # Defined here for consistency, imported from azure_ad_service.py

# Cookie settings
COOKIE_MAX_AGE = 28800  # 8 hours in seconds
COOKIE_SECURE = True
COOKIE_SAMESITE = "strict"
COOKIE_PATH = "/"

# JWT settings
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_DAYS = 30
JWT_EXPIRATION_SECONDS = JWT_EXPIRATION_DAYS * 24 * 60 * 60  # Convert days to seconds

# Azure AD settings
AZURE_AD_COOKIE_MAX_AGE = 8 * 60 * 60  # 8 hours in seconds

# Token expiration times
RECOVERY_TOKEN_EXPIRATION_HOURS = 1
RECOVERY_TOKEN_EXPIRATION = datetime.timedelta(hours=RECOVERY_TOKEN_EXPIRATION_HOURS)

# Invite settings
DEFAULT_INVITE_EXPIRATION_DAYS = 7
MAX_INVITE_EXPIRATION_DAYS = 30

# WebAuthn settings
WEBAUTHN_TIMEOUT_MS = 60000  # 60 seconds in milliseconds

CHALLENGE_TIMEOUT_MS = 60000  # 60 seconds in milliseconds

VERIFICATION_EXPIRATION_DAYS = 1
VERIFICATION_EXPIRATION = datetime.timedelta(days=VERIFICATION_EXPIRATION_DAYS)
VERIFICATION_URL = "{protocol}://{host}/auth/verify/{type}/{token}"

CHALLENGE_URL = "{protocol}://{host}/auth/{verb}/challenge?token={token}"


class VerificationType(str, Enum):
    """Verification types for verification URLs."""

    EMAIL = "email"
    PHONE = "phone"


class ChallengeMode(str, Enum):
    """Challenge modes for WebAuthn operations."""

    REGISTRATION = "registration"
    AUTHENTICATION = "authentication"
    RECOVERY = "recovery"


class ChallengeVerb(str, Enum):
    """Challenge verbs for challenge URLs."""

    RECOVER = "recover"
    REGISTER = "register"


def build_challenge_url(
    protocol: str, host: str, verb: ChallengeVerb, token: str
) -> str:
    """Build a challenge URL for WebAuthn operations.

    Args:
        protocol (str): The protocol (http or https)
        host (str): The host name
        verb (ChallengeVerb): The challenge verb (register or recover)

    Returns:
        str: The formatted challenge URL
    """
    return CHALLENGE_URL.format(
        protocol=protocol, host=host, verb=verb.value.lower(), token=token
    )
