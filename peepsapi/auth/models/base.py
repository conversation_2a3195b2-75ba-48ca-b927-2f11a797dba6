"""Base models for authentication."""
from typing import Optional

import phonenumbers
from pydantic import EmailStr, field_validator

from peepsapi.models.base import BaseModelWithExtra, IdentifierType


def validate_phone_number(value):
    if value is None:
        return value
    try:
        # If the number starts with '+' assume it's complete with country code
        if value.startswith("+"):
            parsed = phonenumbers.parse(value, None)
        else:
            # Fall back to default region (e.g., US) if not prefixed with +
            parsed = phonenumbers.parse(value, "US")

        if not phonenumbers.is_valid_number(parsed):
            raise ValueError("Invalid phone number")
        return phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
    except Exception as e:
        raise ValueError(f"Invalid phone number format: {e}")


class IdentifierBasedRequest(BaseModelWithExtra):
    """Request model for initiating account recovery."""

    email: Optional[EmailStr] = None
    phone_number: Optional[str] = None

    @field_validator("phone_number")
    def validate_phone_number(cls, value):
        return validate_phone_number(value)

    @property
    def identifier_type(self) -> str:
        """Determine the type of identifier provided."""
        if self.email:
            return IdentifierType.EMAIL
        elif self.phone_number:
            return IdentifierType.PHONE
        else:
            raise ValueError("Either email or phone_number must be provided")

    @property
    def identifier_value(self) -> str:
        """Get the value of the identifier."""
        if self.email:
            return self.email
        elif self.phone_number:
            return self.phone_number
        else:
            raise ValueError("Either email or phone_number must be provided")

    @property
    def has_identifier(self) -> bool:
        """Check if any identifier is provided."""
        return bool(self.email or self.phone_number)
