from enum import Enum
from typing import Optional
from uuid import UUID

from peepsapi.auth.models.base import IdentifierBasedRequest
from peepsapi.auth.utils.constants import MAX_INVITE_EXPIRATION_DAYS
from peepsapi.models import DateTimeModelMixin, UTCDateTime
from peepsapi.models.base import BaseModelWithExtra, IdentifierType


class InviteStatus(str, Enum):
    """Status values for invites."""

    PENDING = "pending"
    SENT = "sent"
    CANCELLED = "cancelled"
    ACCEPTED = "accepted"
    FAILED = "failed"


class Invite(DateTimeModelMixin, BaseModelWithExtra):
    """Model representing an invite record."""

    id: UUID  # Unique identifier for the token
    token: str
    identifier_value: str  # primary identifier value (email or phone)
    identifier_type: IdentifierType  # Type of identifier: "email" or "phone"
    person_id: UUID | None = None  # ID of the person who is invited
    invited_by: list[UUID]  # ID of the person who created the invite
    created_at: UTCDateTime
    updated_by: UUID | None = None  # ID of the person who updated the invite
    updated_at: UTCDateTime | None = None
    expires_at: UTCDateTime
    status: InviteStatus = InviteStatus.PENDING
    resend_count: int = 0
    used_at: UTCDateTime | None = None
    invite_url: str | None = None
    is_used: bool = False
    is_recovery: bool = False  # Whether this is a recovery token


class InviteRequest(IdentifierBasedRequest):
    """Request model for creating an invitation."""

    expires_in_days: Optional[int] = MAX_INVITE_EXPIRATION_DAYS


class InviteResponse(BaseModelWithExtra):
    """Response model for invitation creation."""

    invite_url: str
    expires_at: UTCDateTime
