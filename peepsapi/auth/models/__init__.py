"""Models package for authentication.

This package contains model modules for authentication data structures.
"""

from .authentication import (
    AuthenticationChallenge,
    AuthenticationCredentialData,
    AuthenticationCredentialResponseData,
    AuthenticationVerifyRequest,
    ChallengeResponse,
)
from .device import (
    DeviceInfo,
    DeviceInfoRequest,
    DeviceInfoResponse,
    DeviceListResponse,
)
from .email_verification import (
    EmailVerification,
    EmailVerificationRequest,
    VerificationStatus,
)
from .invite import Invite, InviteRequest, InviteResponse, InviteStatus
from .invite_quota import InviteQuotaChange
from .passkey import PasskeyCredential
from .recovery import RecoveryRegistrationVerifyRequest, RecoveryResponse
from .registration import (
    RegistrationChallenge,
    RegistrationCredentialData,
    RegistrationCredentialResponseData,
    RegistrationResponse,
    RegistrationVerifyRequest,
    RegistrationVerifyResponse,
)
from .session_token import SessionToken

__all__ = [
    # Authentication models
    "AuthenticationChallenge",
    "ChallengeResponse",
    "AuthenticationCredentialResponseData",
    "AuthenticationCredentialData",
    "AuthenticationVerifyRequest",
    # Device models
    "DeviceInfo",
    "DeviceInfoResponse",
    "DeviceListResponse",
    "DeviceInfoRequest",
    # Invite models
    "Invite",
    "InviteStatus",
    "InviteRequest",
    "InviteResponse",
    "InviteQuotaChange",
    # Passkey models
    "PasskeyCredential",
    # Registration models
    "RegistrationChallenge",
    "RegistrationCredentialResponseData",
    "RegistrationCredentialData",
    "RegistrationVerifyRequest",
    "RegistrationResponse",
    "RegistrationVerifyResponse",
    # Recovery models
    "RecoveryResponse",
    "RecoveryRegistrationVerifyRequest",
    # Session
    "SessionToken",
    # Email verification models
    "EmailVerification",
    "EmailVerificationRequest",
    "VerificationStatus",
]
