"""Passkey authentication models for the PeepsAPI application.

This module contains models related to passkey authentication, including the
PasskeyCredential model and related authentication models.
"""

from base64 import urlsafe_b64decode
from typing import Any, Dict, List, Optional
from uuid import UUID

from fido2.webauthn import <PERSON><PERSON><PERSON><PERSON>

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.models import DateTimeModelMixin, UTCDateTime, now
from peepsapi.models.base import BaseModelWithExtra
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class CredentialSource:
    """Represents a credential source for WebAuthn authentication."""

    credential_id: bytes
    public_key: CoseKey
    rp_id: str
    user_handle: bytes
    sign_count: int

    def __init__(self, credential_id, public_key, rp_id, user_handle, sign_count):
        self.credential_id = credential_id
        self.public_key = public_key
        self.rp_id = rp_id
        self.user_handle = user_handle
        self.sign_count = sign_count


class PasskeyCredential(DateTimeModelMixin, BaseModelWithExtra):
    """Represents a WebAuthn credential for passkey authentication."""

    id: str
    person_id: UUID
    state_dict_json: str = None  # JSON-encoded state for FIDO2 verification
    raw_id: bytes
    client_data_bytes: bytes
    attestation_object_bytes: bytes
    rp_id: str
    # user_handle: bytes
    sign_count: int
    device_info: DeviceInfo
    created_at: UTCDateTime
    updated_at: Optional[UTCDateTime] = None
    is_active: bool = True

    def update_from_auth_data(
        self,
        id: str,
        raw_id: bytes,
        client_data_bytes: bytes,
        attestation_object_bytes: bytes,
        state_dict_json: str,
        person_id: UUID,
        rp_id: str,
        sign_count: int,
        device_info: DeviceInfo,
    ):
        """Update an existing PasskeyCredential from FIDO2 data."""
        fields = self._auth_data_fields(
            id,
            raw_id,
            client_data_bytes,
            attestation_object_bytes,
            state_dict_json,
            person_id,
            rp_id,
            sign_count,
            device_info,
        )
        for key, value in fields.items():
            setattr(self, key, value)
        self.update_at = now()

    @staticmethod
    def _auth_data_fields(
        id: str,
        raw_id: bytes,
        client_data_bytes: bytes,
        attestation_object_bytes: bytes,
        state_dict_json: str,
        person_id: UUID,
        rp_id: str,
        sign_count: int,
        device_info: DeviceInfo,
    ) -> dict:
        """Build shared field values from FIDO2 AuthenticatorData."""
        return {
            "id": id,
            "person_id": person_id,
            "raw_id": raw_id,
            "client_data_bytes": client_data_bytes,
            "attestation_object_bytes": attestation_object_bytes,
            "state_dict_json": state_dict_json,
            "rp_id": rp_id,
            "sign_count": sign_count,
            "device_info": device_info,
        }

    @classmethod
    def init_from_auth_data(
        cls,
        id: str,
        raw_id: bytes,
        client_data_bytes: bytes,
        attestation_object_bytes: bytes,
        state_dict_json: str,
        person_id: UUID,
        rp_id: str,
        sign_count: int,
        device_info: DeviceInfo,
    ):
        """Create a new PasskeyCredential from FIDO2 data."""
        fields = cls._auth_data_fields(
            id,
            raw_id,
            client_data_bytes,
            attestation_object_bytes,
            state_dict_json,
            person_id,
            rp_id,
            sign_count,
            device_info,
        )
        fields["created_at"] = now()
        return cls(**fields)

    @classmethod
    def model_construct(cls, **kwargs):
        if "credential_data" in kwargs:
            kwargs["credential_data"] = urlsafe_b64decode(
                kwargs["credential_data"] + "=="
            )
