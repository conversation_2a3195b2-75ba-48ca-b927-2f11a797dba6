"""Device models for the PeepsAPI application.

This module contains models related to devices, including the DeviceInfo model
which standardizes device information across the application.
"""

from typing import List, Optional

from pydantic import Field

from peepsapi.models import DateTimeModelMixin, UTCDateTime
from peepsapi.models.base import BaseModelWithExtra


class DeviceInfo(DateTimeModelMixin, BaseModelWithExtra):
    """Standardized device information model.

    This model provides a consistent structure for device information
    across the application, including authentication and session management.
    """

    name: str  # User-friendly name for the device
    type: str  # Type of device (e.g., "mobile", "desktop")
    os: Optional[str] = None  # Operating system of the device
    browser: Optional[str] = None  # Browser used
    user_agent: Optional[str] = None  # User agent string
    ip: Optional[str] = None  # IP address
    created_at: Optional[UTCDateTime]  # When the device info was created
    last_used_at: Optional[UTCDateTime] = None  # When the device was last used
    is_active: bool = True  # Whether the device is active
    credential_id: Optional[str] = None  # Reference to the PasskeyCredential


class DeviceInfoResponse(DateTimeModelMixin, BaseModelWithExtra):
    """Response model for device information.

    This model is used for API responses and is derived from the standard
    DeviceInfo model.
    """

    id: str
    name: str
    type: str
    os: Optional[str] = None
    browser: Optional[str] = None
    is_active: bool = True
    registered_at: Optional[UTCDateTime] = None
    last_used_at: Optional[UTCDateTime] = None

    @classmethod
    def from_device_info(
        cls, device_id: str, device_info: DeviceInfo
    ) -> "DeviceInfoResponse":
        """Create a response model from a DeviceInfo model.

        Args:
            device_id: The ID of the device (usually the credential ID)
            device_info: The DeviceInfo model

        Returns:
            DeviceInfoResponse: A response model for the API
        """
        return cls(
            id=device_id,
            name=device_info.name,
            type=device_info.type,
            os=device_info.os,
            browser=device_info.browser,
            is_active=device_info.is_active,
            registered_at=device_info.created_at,
            last_used_at=device_info.last_used_at,
        )


class DeviceListResponse(BaseModelWithExtra):
    """Response model for device listing."""

    devices: List[DeviceInfoResponse]


class DeviceInfoRequest(BaseModelWithExtra):
    """Request model for device information.

    This model is used for API requests and will be converted to the standard
    DeviceInfo model internally.
    """

    name: str = Field(..., min_length=1, max_length=100)
    type: str = Field(..., min_length=1, max_length=50)
    os: Optional[str] = Field(None, max_length=50)
    browser: Optional[str] = Field(None, max_length=50)
    ip: Optional[str] = None

    def to_device_info(self, created_at: UTCDateTime) -> DeviceInfo:
        """Convert this request model to a DeviceInfo model.

        Args:
            created_at: The creation timestamp for the device info

        Returns:
            DeviceInfo: A standardized device info model
        """
        return DeviceInfo(
            name=self.name,
            type=self.type,
            os=self.os,
            browser=self.browser,
            ip=self.ip,
            created_at=created_at,
            last_used_at=created_at,
            is_active=True,
        )
