"""Models for email verification flow."""

from enum import Enum
from typing import Optional
from uuid import UUID

from pydantic import EmailStr

from peepsapi.models import DateTimeModelMixin, UTCDateTime
from peepsapi.models.base import BaseModelWithExtra


class VerificationStatus(str, Enum):
    """Status values for email verification records."""

    PENDING = "pending"
    SENT = "sent"
    VERIFIED = "verified"
    EXPIRED = "expired"


class EmailVerification(DateTimeModelMixin, BaseModelWithExtra):
    """Represents a single email verification token."""

    id: UUID
    token: str
    email: EmailStr
    person_id: UUID
    created_at: UTCDateTime
    expires_at: UTCDateTime
    updated_at: Optional[UTCDateTime] = None
    verified_at: Optional[UTCDateTime] = None
    status: VerificationStatus = VerificationStatus.PENDING
    resend_count: int = 0
    attempts: int = 0
    is_used: bool = False


class EmailVerificationRequest(BaseModelWithExtra):
    """Request model for starting email verification."""

    email: EmailStr
