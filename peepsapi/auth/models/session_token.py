"""Session token model."""

from typing import Optional
from uuid import UUID

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.models import DateTimeModelMixin, UTCDateTime
from peepsapi.models.base import BaseModelWithExtra


class SessionToken(DateTimeModelMixin, BaseModelWithExtra):
    """Represents a user session token.

    This model stores information about a user's session, including the
    JWT token and associated metadata.
    """

    id: str  # Unique identifier for the session
    person_id: UUID  # ID of the person associated with the session
    token: str  # JWT token
    device_info: DeviceInfo  # Standardized device information
    credential_id: Optional[str] = None  # Reference to the PasskeyCredential
    created_at: UTCDateTime
    expires_at: UTCDateTime
    login_time: Optional[UTCDateTime] = None  # When the user logged in
    is_active: bool = True
