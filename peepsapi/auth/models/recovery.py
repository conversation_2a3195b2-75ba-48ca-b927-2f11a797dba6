"""Recovery models"""
import secrets
from enum import Enum
from typing import Dict
from uuid import UUID, uuid4

from peepsapi.auth.models.base import IdentifierBasedRequest
from peepsapi.auth.utils.constants import RECOVERY_TOKEN_EXPIRATION
from peepsapi.models import DateTimeModelMixin, UTCDateTime, now
from peepsapi.models.base import BaseModelWithExtra, IdentifierType


class RecoveryStatus(str, Enum):
    """Status values for recovery tokens."""

    PENDING = "pending"
    SENT = "sent"
    CONSUMED = "consumed"
    EXPIRED = "expired"


class Recovery(DateTimeModelMixin, BaseModelWithExtra):
    """Represents a single recovery token."""

    id: UUID
    token: str
    identifier_type: IdentifierType
    identifier_value: str
    person_id: UUID | None = None
    created_at: UTCDateTime
    expires_at: UTCDateTime
    updated_at: UTCDateTime | None = None
    used_at: UTCDateTime | None = None
    status: RecoveryStatus = RecoveryStatus.PENDING
    resend_count: int = 0
    attempts: int = 0
    is_used: bool = False

    @classmethod
    def create(
        cls,
        identifier_type: IdentifierType,
        identifier_value: str,
        person_id: UUID | None = None,
    ) -> "Recovery":
        token = secrets.token_urlsafe(32)
        token_id = uuid4()
        now_dt = now()
        return cls(
            id=token_id,
            token=token,
            identifier_type=identifier_type,
            identifier_value=identifier_value,
            person_id=person_id,
            created_at=now_dt,
            expires_at=now_dt + RECOVERY_TOKEN_EXPIRATION,
        )


class RecoveryVerifyRequest(IdentifierBasedRequest):
    """Request model for verifying a recovery code."""

    token: str


class RecoveryResponse(BaseModelWithExtra):
    """Response model for account recovery."""

    success: bool
    message: str


class RecoveryRegistrationVerifyRequest(BaseModelWithExtra):
    """Request model for verifying a registration during recovery."""

    token: str
    credential: Dict
    is_recovery: bool = True
