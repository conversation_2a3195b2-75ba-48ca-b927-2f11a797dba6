"""Registration models"""
from typing import Any, Dict, Optional
from uuid import UUID

from pydantic import Field

from peepsapi.models import DateTimeModelMixin, UTCDateTime
from peepsapi.models.base import BaseModelWithExtra


class RegistrationChallenge(DateTimeModelMixin, BaseModelWithExtra):
    """Represents a FIDO2 registration challenge.

    This model stores the registration challenge data needed for initiating a FIDO2 registration.
    """

    id: str  # Unique identifier for the challenge
    challenge: str  # Random challenge for registration (base64url encoded)
    person_id: UUID  # Person ID for the registration
    timeout: int  # Timeout in milliseconds
    rp_id: str  # Relying Party ID (domain name)
    rp_name: str  # Relying Party name (user-friendly)
    user_name: str  # User's name
    user_display_name: str  # User's display name
    attestation: Optional[str]  # Attestation requirement
    authenticator_selection: Optional[
        Dict[str, Any]
    ] = None  # Authenticator selection criteria
    created_at: UTCDateTime
    expires_at: UTCDateTime
    state_dict_json: str = None  # JSON-encoded state for FIDO2 verification
    invite_token: str  # Invite token ID
    is_recovery: bool = False  # Whether this is a recovery challenge


class RegistrationCredentialResponseData(BaseModelWithExtra):
    """Model for registration credential data."""

    attestation_object: bytes
    client_data_json: bytes


class RegistrationCredentialData(BaseModelWithExtra):
    """Model for registration credential data."""

    id: str
    raw_id: str
    type: str
    response: RegistrationCredentialResponseData


class RegistrationVerifyRequest(BaseModelWithExtra):
    """Request model for registration verification."""

    credential: RegistrationCredentialData
    token: str = Field(..., min_length=32, max_length=64)  # Based on token generation


class RegistrationResponse(BaseModelWithExtra):
    """Response model for registration challenge."""

    options: dict
    challenge_id: str
    person_id: UUID


class RegistrationVerifyResponse(BaseModelWithExtra):
    """Response model for registration verification."""

    credential_id: str
    session_token: str
    success: bool
    message: str
    person_id: UUID
