from uuid import UUID, uuid4

from peepsapi.models import DateTimeModelMixin, UTCDateTime, now
from peepsapi.models.base import BaseModelWithExtra


class InviteQuotaChange(DateTimeModelMixin, BaseModelWithExtra):
    """Audit record for invite quota changes."""

    id: UUID
    person_id: UUID
    actor_id: UUID
    prior_quota: int
    new_quota: int
    updated_at: UTCDateTime

    @classmethod
    def create(
        cls, person_id: UUID, actor_id: UUID, prior_quota: int, new_quota: int
    ) -> "InviteQuotaChange":
        return cls(
            id=uuid4(),
            person_id=person_id,
            actor_id=actor_id,
            prior_quota=prior_quota,
            new_quota=new_quota,
            updated_at=now(),
        )
