"""Request and response models for authentication endpoints.

This module contains Pydantic models for request and response data used in
authentication endpoints, including registration, login, and recovery flows.
"""
from typing import Dict, List, Optional
from uuid import UUID

from peepsapi.models import DateTimeModelMixin, UTCDateTime
from peepsapi.models.base import BaseModelWithExtra
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class ChallengeResponse(BaseModelWithExtra):
    """Response model for challenge."""

    options: Dict


class AuthenticationChallenge(DateTimeModelMixin, BaseModelWithExtra):
    """Represents a FIDO2 authentication challenge.

    This model stores the challenge data needed for initiating a FIDO2 authentication.
    """

    id: str  # Unique identifier for the challenge
    challenge: str  # Random challenge for authentication (base64url encoded)
    person_id: UUID  # Person ID for the authentication
    timeout: int  # Timeout in milliseconds
    rp_id: str  # Relying Party ID (domain name)
    rp_name: str  # Relying Party name (user-friendly)
    created_at: UTCDateTime
    expires_at: UTCDateTime
    state_dict_json: str = None  # JSON-encoded state for FIDO2 verification
    allow_credentials: Optional[List[Dict[str, str]]]  # List of allowed credential IDs


class AuthenticationCredentialResponseData(BaseModelWithExtra):
    """Model for authentication credential data."""

    authenticator_data: bytes
    client_data_json: bytes
    signature: bytes
    user_handle: bytes


class AuthenticationCredentialData(BaseModelWithExtra):
    """Model for authentication credential data."""

    id: str
    raw_id: str
    type: str
    response: AuthenticationCredentialResponseData


class AuthenticationVerifyRequest(BaseModelWithExtra):
    """Request model for authentication verification."""

    credential: AuthenticationCredentialData


class AuthenticationVerifyResponse(BaseModelWithExtra):
    """Response model for authentication verification."""

    person_id: UUID
    profile_completed: bool
    session_token: str
