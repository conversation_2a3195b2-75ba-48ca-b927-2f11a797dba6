"""Session management routes for authentication.

This module provides API endpoints for session management, including
logout and session token refresh.
"""

from typing import Optional
from uuid import UUID

from fastapi import APIRouter, <PERSON><PERSON>, Depends, Request, Response

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.token_service import token_service
from peepsapi.models import now
from peepsapi.services.cosmos_containers import get_session_tokens_container
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.logging import get_logger

router = APIRouter(tags=["session"])

# Configure logger
logger = get_logger(__name__)


@router.post("/logout")
@handle_exceptions(error_code_prefix="SESSION")
async def logout(
    response: Response,
    request: Request,
    session_token: Optional[str] = Cookie(None),
    current_person: Optional[UUID] = Depends(auth_service.get_current_person),
):
    """Log out the current user by invalidating the session.

    Args:
        response (Response): The FastAPI response object
        request (Request): The FastAPI request object
        session_token (Optional[str]): The session token cookie
        current_person (Optional[UUID]): The current authenticated person

    Returns:
        Dict: The result of logout
    """
    # Clear session token cookie with secure attributes
    response.delete_cookie(
        key="session_token",
        httponly=True,  # JavaScript cannot access the cookie
        secure=True,  # Requires HTTPS
        samesite="strict",  # Only sent in same-site navigation (no cross-origin)
    )

    # If we have a session token, revoke it
    if session_token:
        success = token_service.revoke_token(session_token)
        if success:
            logger.info(
                "✅ Token revoked successfully during logout",
                request=request,
                person_id=current_person,
            )
        else:
            logger.warning(
                "⚠️ Failed to revoke token during logout",
                request=request,
                person_id=current_person,
            )

    return {"success": True, "message": "Logged out successfully"}


@router.post("/refresh-token")
@handle_exceptions(error_code_prefix="SESSION")
async def refresh_token(
    response: Response,
    request: Request,
    session_token: Optional[str] = Cookie(None),
    current_person: UUID = Depends(auth_service.get_current_person),
):
    """Refresh the session token.

    Args:
        response (Response): The FastAPI response object
        request (Request): The FastAPI request object
        session_token (Optional[str]): The session token cookie
        current_person (UUID): The current authenticated person

    Returns:
        Dict: The result of token refresh
    """
    if not session_token:
        logger.warning(
            "⚠️ No session token provided for refresh",
            request=request,
            person_id=current_person,
        )
        return {"success": False, "message": "No session token provided"}

    # Find the current session
    query = "SELECT * FROM c WHERE c.token = @token"
    params = [{"name": "@token", "value": session_token}]

    session_tokens_container = get_session_tokens_container()

    session_results = list(
        session_tokens_container.query_items(
            query=query,
            parameters=params,
            enable_cross_partition_query=True,
        )
    )

    if not session_results:
        logger.warning(
            "⚠️ Session not found for refresh",
            request=request,
            person_id=current_person,
        )
        return {"success": False, "message": "Session not found"}

    session = session_results[0]

    # Ensure device_info is a DeviceInfo object
    device_info_data = session.get("device_info", {})
    current_time = now()

    # Convert to DeviceInfo if it's a dictionary
    if isinstance(device_info_data, dict):
        # Ensure all required fields are present
        if "name" not in device_info_data:
            device_info_data["name"] = "Unknown Device"
        if "type" not in device_info_data:
            device_info_data["type"] = "unknown"
        if "created_at" not in device_info_data:
            device_info_data["created_at"] = current_time.isoformat()
        if "is_active" not in device_info_data:
            device_info_data["is_active"] = True

        device_info = DeviceInfo(**device_info_data)
    else:
        # Create a default DeviceInfo if none exists
        device_info = DeviceInfo(
            name="Unknown Device",
            type="unknown",
            os="Unknown",
            browser="Unknown",
            ip="0.0.0.0",
            created_at=current_time,
            last_used_at=current_time,
            is_active=True,
        )

    # Update last_used_at
    device_info.last_used_at = current_time

    # Create a new session token
    _, _ = token_service.create_and_set_token(
        response=response,
        person_id=current_person,
        device_info=device_info,
        login_time=current_time,
    )

    # Revoke the old token
    token_service.revoke_token(session_token)

    logger.info(
        "✅ Token refreshed successfully",
        request=request,
        person_id=current_person,
    )

    return {"success": True, "message": "Token refreshed successfully"}
