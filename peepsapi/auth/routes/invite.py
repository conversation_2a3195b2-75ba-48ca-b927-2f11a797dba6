"""API endpoints for invite management."""

from typing import List
from uuid import UUID

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Request

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.invite import Invite, InviteRequest, InviteResponse
from peepsapi.auth.services import audit_logger
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.device_service import device_service
from peepsapi.auth.services.invite_service import invite_service
from peepsapi.auth.services.rate_limiter import registration_rate_limiter
from peepsapi.models import to_iso_string
from peepsapi.services.cosmos_containers import (
    get_invite_tokens_container,
    get_people_container,
)
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.constants import SYSTEM_USER_ID
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import ValidationError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/invite", tags=["invite"])


@router.post("", response_model=InviteResponse)
@handle_exceptions(error_code_prefix="INVITE")
async def create_invite(
    invite_request: InviteRequest,
    request: Request,
    background_tasks: BackgroundTasks,
    auth_source: str = Depends(auth_service.get_auth_source),
    actor_id: UUID = Depends(auth_service.get_actor_id),
    current_person: UUID = Depends(auth_service.get_current_person),
    invite_container: CosmosContainer = Depends(get_invite_tokens_container),
    people_container: CosmosContainer = Depends(get_people_container),
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
):
    """Create an invitation for a new user.

    Args:
        invite_request (InviteRequest): The invitation request
        request (Request): The FastAPI request object
        auth_source (str): The current authentication source (from auth_service.get_auth_source)
        actor_id (UUID): The current logged in person's real ID (from auth_service.get_actor_id)
        current_person (UUID): The current authenticated person (from auth_service.get_current_person)
        invite_container: The invite tokens container dependency
        people_container: The people container dependency
        device_info (DeviceInfo): The device information

    Returns:
        InviteResponse: The created invitation
    """
    logger.info(
        f"🔐 Creating invite for {invite_request.identifier_type}",
        request=request,
        person_id=current_person,
        extra={
            "identifier_type": invite_request.identifier_type,
            "expires_in_days": invite_request.expires_in_days,
            "client_ip": device_info.ip,
            "user_agent": device_info.user_agent,
        },
    )
    protocol = request.headers.get("x-forwarded-proto", "https")
    host = request.headers.get("host", "")
    invite = invite_service.create_invite(
        creator_id=current_person,
        actor_id=actor_id,
        auth_source=auth_source,
        invite_request=invite_request,
        protocol=protocol,
        host=host,
        background_tasks=background_tasks,
        invite_container=invite_container,
        people_container=people_container,
    )

    audit_logger.log_invite_attempt(
        invite_request.identifier_type,
        invite_request.identifier_value,
        current_person,
        ip_address=device_info.ip,
        user_agent=device_info.user_agent,
        success=True,
    )

    return InviteResponse(
        invite_url=invite.invite_url,
        expires_at=to_iso_string(invite.expires_at),
    )


@router.post("/{invite_id}/resend", response_model=InviteResponse)
@handle_exceptions(error_code_prefix="INVITE")
async def resend_invite(
    invite_id: UUID,
    background_tasks: BackgroundTasks,
    auth_source: str = Depends(auth_service.get_auth_source),
    actor_id: UUID = Depends(auth_service.get_actor_id),
    current_person: UUID = Depends(auth_service.get_current_person),
    invite_container: CosmosContainer = Depends(get_invite_tokens_container),
):
    """Resend an invitation.

    Args:
        invite_id (str): The invite ID
        background_tasks (BackgroundTasks): The background tasks
        current_person (UUID): The current authenticated person (from auth_service.get_current_person)
        invite_container: The invite tokens container dependency

    Returns:
        InviteResponse: The updated invitation
    """
    invite = invite_service.resend_invite(
        creator_id=current_person,
        actor_id=actor_id,
        auth_source=auth_source,
        invite_id=invite_id,
        background_tasks=background_tasks,
        invite_container=invite_container,
    )
    audit_logger.log_event(
        event_type="resend_invite",
        user_id=current_person,
        details={"invite_id": str(invite_id)},
    )
    return InviteResponse(
        invite_url=invite.invite_url,
        expires_at=to_iso_string(invite.expires_at),
    )


@router.get("/active", response_model=List[Invite])
@handle_exceptions(error_code_prefix="INVITE")
async def fetch_active_invites(
    auth_source: str = Depends(auth_service.get_auth_source),
    current_person: UUID = Depends(auth_service.get_current_person),
    invite_container: CosmosContainer = Depends(get_invite_tokens_container),
):
    if auth_source == "azure_ad":
        return invite_service.fetch_all_active_invites(invite_container)
    else:
        return invite_service.fetch_active_invites_by_creator(
            current_person, invite_container
        )


@router.get("/{invite_id}", response_model=Invite)
@handle_exceptions(error_code_prefix="INVITE")
async def fetch_invite(
    invite_id: UUID,
    invite_container: CosmosContainer = Depends(get_invite_tokens_container),
):
    return invite_service.fetch_invite_by_id(invite_id, invite_container)


@router.delete("/{invite_id}")
@handle_exceptions(error_code_prefix="INVITE")
async def cancel_invite(
    invite_id: UUID,
    auth_source: str = Depends(auth_service.get_auth_source),
    actor_id: UUID = Depends(auth_service.get_actor_id),
    current_person: UUID = Depends(auth_service.get_current_person),
    invite_container: CosmosContainer = Depends(get_invite_tokens_container),
) -> Invite:
    """Cancel an invite."""
    try:
        invite = invite_service.cancel_invite(
            invite_id=invite_id,
            auth_source=auth_source,
            actor_id=actor_id,
            creator_id=current_person,
            invite_container=invite_container,
        )
        audit_logger.log_event(
            event_type="cancel_invite",
            user_id=current_person,
            details={"invite_id": str(invite_id)},
        )
        return invite
    except ValidationError as e:
        if e.error_code == "CANCEL_FAILED":
            raise HTTPException(status_code=404, detail="Invite not found")
        raise HTTPException(status_code=400, detail=e.message)
