"""Recovery routes for authentication.

This module provides API endpoints for account recovery, including
initiating recovery, verifying recovery codes, and registering new credentials.
"""

from uuid import UUID

from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    HTTPException,
    Query,
    Request,
    Response,
)
from fastapi.responses import JSONResponse

from peepsapi.auth.models.base import IdentifierBasedRequest
from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.recovery import RecoveryResponse
from peepsapi.auth.models.registration import (
    RegistrationResponse,
    RegistrationVerifyRequest,
    RegistrationVerifyResponse,
)
from peepsapi.auth.services import token_service
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.challenge_service import challenge_service
from peepsapi.auth.services.device_service import device_service
from peepsapi.auth.services.rate_limiter import registration_rate_limiter
from peepsapi.auth.services.recovery_service import recovery_service
from peepsapi.auth.services.security_service import audit_logger
from peepsapi.auth.utils.constants import ChallengeMode
from peepsapi.models.base import recursive_clean
from peepsapi.services.cosmos_containers import (
    get_passkeys_container,
    get_people_container,
    get_recovery_tokens_container,
    get_session_tokens_container,
)
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import ResourceNotFoundError, ValidationError
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/recover", tags=["recovery"])
logger = get_logger(__name__)


@router.post("/initiate", response_model=RecoveryResponse)
@handle_exceptions(error_code_prefix="RECOVERY")
async def initiate_recovery(
    request: IdentifierBasedRequest,
    request_obj: Request,
    background_tasks: BackgroundTasks,
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    recovery_container: CosmosContainer = Depends(get_recovery_tokens_container),
    people_container: CosmosContainer = Depends(get_people_container),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
):
    """Initiate account recovery.

    Args:
        request (IdentifierBasedRequest): The recovery request
        request_obj (Request): The FastAPI request object
        device_info (DeviceInfo): The device information
        recovery_container: The recovery container dependency
        people_container: The people container dependency
        _ (None): Rate limit check dependency

    Returns:
        RecoveryResponse: The recovery response
    """
    logger.info(
        f"🔐 Initiating recovery for {request.identifier_type}",
        request=request_obj,
        extra={
            "identifier_type": request.identifier_type,
            "client_ip": device_info.ip,
            "user_agent": device_info,
        },
    )

    recovery_service.initiate_recovery(
        identifier_type=request.identifier_type,
        identifier_value=request.identifier_value,
        protocol=request_obj.headers.get("x-forwarded-proto", "https"),
        host=request_obj.headers.get("host", ""),
        background_tasks=background_tasks,
        recovery_container=recovery_container,
        people_container=people_container,
    )

    # Always return silent success
    audit_logger.log_recovery_attempt(
        identifier_type=request.identifier_type,
        identifier_value=request.identifier_value,
        ip_address=device_info.ip,
        user_agent=device_info.user_agent,
        success=True,
    )

    return RecoveryResponse(
        success=True,
        message="If an account exists with this identifier, a recovery email has been sent.",
    )


@router.post("/challenge", response_model=RegistrationResponse)
@handle_exceptions(error_code_prefix="RECOVERY")
async def create_recovery_challenge(
    request_obj: Request,
    token: str = Query(..., min_length=32, max_length=64),
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    recovery_container: CosmosContainer = Depends(get_recovery_tokens_container),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
):
    """Create a WebAuthn recovery challenge.

    Args:
        token (str): The recovery token
        request_obj (Request): The FastAPI request object
        device_info (DeviceInfo): The device information
        recovery_container: The recovery container dependency
        _ (None): Rate limit check dependency

    Returns:
        Dict: The registration challenge response
    """
    logger.info(
        "🔐 Recovery challenge request",
        request=request_obj,
        extra={"client_ip": device_info.ip, "user_agent": device_info.user_agent},
    )

    # Validate recovery token
    recovery_token = token_service.validate_challenge_token(
        token, ChallengeMode.RECOVERY, recovery_container
    )
    if not recovery_token:
        logger.warning(
            "Invalid or expired recovery token",
            request=request_obj,
            extra={"token": token[:5] + "..." if token else None},
        )
        raise ValidationError(
            message="Invalid or expired recovery token",
            error_code="INVALID_TOKEN",
        )

    # Log successful recovery token validation
    audit_logger.log_recovery_attempt(
        identifier_type=recovery_token.identifier_type,
        identifier_value=recovery_token.identifier_value,
        user_id=recovery_token.person_id,
        ip_address=device_info.ip,
        user_agent=device_info.user_agent,
        success=True,
        reason="Recovery token validated",
    )
    display_name = recovery_token.identifier_value or recovery_token.person_id

    # Create challenge using unified method
    options, challenge = await challenge_service.create_challenge(
        mode=ChallengeMode.RECOVERY,
        person_id=recovery_token.person_id,
        person_name=display_name,
        display_name=display_name,
        token=recovery_token,
    )

    logger.info(
        "✅ Recovery Registration challenge created successfully",
        request=request_obj,
        person_id=recovery_token.person_id,
    )

    return JSONResponse(content=recursive_clean(dict(options)))


@router.post("/verify", response_model=RegistrationVerifyResponse)
@handle_exceptions(error_code_prefix="RECOVERY")
async def verify_recovery_challenge(
    request: RegistrationVerifyRequest,
    response: Response,
    request_obj: Request,
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    recovery_container: CosmosContainer = Depends(get_recovery_tokens_container),
    passkeys_container: CosmosContainer = Depends(get_passkeys_container),
    people_container: CosmosContainer = Depends(get_people_container),
    session_tokens_container: CosmosContainer = Depends(get_session_tokens_container),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
):
    """Verify WebAuthn recovery challenge.

    Args:
        request (RegistrationVerifyRequest): The recovery registration verification request
        response (Response): The FastAPI response object
        request_obj (Request): The FastAPI request object
        device_info (DeviceInfo): The device information
        recovery_container: The recovery container dependency
        passkeys_container: The passkey credentials container dependency
        people_container: The people container dependency
        session_tokens_container: The session tokens container dependency
        device_info (DeviceInfo): The device information
        _ (None): Rate limit check dependency

    Returns:
        Dict: The result of registration verification
    """
    return await auth_service.verify_registration_challenge(
        request=request,
        request_obj=request_obj,
        response=response,
        device_info=device_info,
        tokens_container=recovery_container,
        passkeys_container=passkeys_container,
        people_container=people_container,
        session_tokens_container=session_tokens_container,
        mode=ChallengeMode.RECOVERY,
    )


@router.get("/tokens", include_in_schema=False)
@handle_exceptions(error_code_prefix="RECOVERY")
async def list_recovery_tokens(
    auth_source: str = Depends(auth_service.get_auth_source),
    recovery_container: CosmosContainer = Depends(get_recovery_tokens_container),
):
    """Return active recovery tokens for administrators.

    Args:
        auth_source: Source of authentication for the request.
        recovery_container: Cosmos container for recovery tokens.

    Returns:
        List of active recovery tokens.
    """
    if auth_source != "azure_ad":
        raise HTTPException(status_code=403, detail="Unauthorized")
    return recovery_service.get_active_tokens(recovery_container)


@router.delete("/token/{token_id}", include_in_schema=False)
@handle_exceptions(error_code_prefix="RECOVERY")
async def cancel_recovery_token(
    token_id: UUID,
    auth_source: str = Depends(auth_service.get_auth_source),
    recovery_container: CosmosContainer = Depends(get_recovery_tokens_container),
):
    """Cancel a recovery token.

    Args:
        token_id: Identifier of the token to cancel.
        auth_source: Source of authentication for the request.
        recovery_container: Cosmos container for recovery tokens.

    Returns:
        Confirmation of cancellation.
    """
    if auth_source != "azure_ad":
        raise HTTPException(status_code=403, detail="Unauthorized")
    try:
        recovery_service.cancel_token(token_id, recovery_container)
    except Exception as exc:
        if isinstance(exc, ResourceNotFoundError):
            raise HTTPException(status_code=404, detail="Recovery token not found")
        raise
    return {"token_id": token_id, "cancelled": True}
