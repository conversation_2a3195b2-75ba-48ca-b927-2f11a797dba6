"""API routes for email verification."""

from uuid import UUID

from fastapi import APIRouter, BackgroundTasks, Depends, Request

from peepsapi.auth.models.email_verification import (
    EmailVerification,
    EmailVerificationRequest,
)
from peepsapi.auth.services import audit_logger
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.email_verification_service import email_verification_service
from peepsapi.auth.services.rate_limiter import registration_rate_limiter
from peepsapi.services.cosmos_containers import (
    get_email_verifications_container,
    get_people_container,
)
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.decorators import handle_exceptions

router = APIRouter(prefix="/verify", tags=["email-verification"])


@router.post("/email/initiate", response_model=EmailVerification)
@handle_exceptions(error_code_prefix="EMAILVER")
async def start_verification(
    email_verification_request: EmailVerificationRequest,
    request: Request,
    background_tasks: BackgroundTasks,
    current_person: UUID = Depends(auth_service.get_current_person),
    email_verification_container: CosmosContainer = Depends(
        get_email_verifications_container
    ),
    people_container: CosmosContainer = Depends(get_people_container),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
) -> EmailVerification:
    protocol = request.headers.get("x-forwarded-proto", "https")
    host = request.headers.get("host", "")
    record = email_verification_service.initiate_verification(
        current_person,
        email_verification_request,
        protocol,
        host,
        background_tasks,
        email_verification_container,
        people_container,
    )
    audit_logger.log_event(
        event_type="email_verification_initiate",
        user_id=current_person,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
        details={"email": email_verification_request.email},
    )
    return record


@router.get("/email/{token}", response_model=EmailVerification)
@handle_exceptions(error_code_prefix="EMAILVER")
async def verify_email(
    token: str,
    current_person: UUID = Depends(auth_service.get_current_person),
    email_verification_container: CosmosContainer = Depends(
        get_email_verifications_container
    ),
    people_container: CosmosContainer = Depends(get_people_container),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
) -> EmailVerification:
    record = email_verification_service.verify_code_or_link(
        token, current_person, email_verification_container, people_container
    )
    audit_logger.log_event(
        event_type="email_verification_verify",
        user_id=current_person,
    )
    return record


@router.get("/email/{verification_id}/status", response_model=EmailVerification)
@handle_exceptions(error_code_prefix="EMAILVER")
async def fetch_verification(
    verification_id: UUID,
    current_person: UUID = Depends(auth_service.get_current_person),
    email_verification_container: CosmosContainer = Depends(
        get_email_verifications_container
    ),
) -> EmailVerification:
    record = email_verification_service.fetch_verification_status(
        current_person, verification_id, email_verification_container
    )
    return record


@router.get("/email/{verification_id}/resend", response_model=EmailVerification)
@handle_exceptions(error_code_prefix="EMAILVER")
async def resend_verification(
    verification_id: UUID,
    request: Request,
    background_tasks: BackgroundTasks,
    current_person: UUID = Depends(auth_service.get_current_person),
    email_verification_container: CosmosContainer = Depends(
        get_email_verifications_container
    ),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
) -> EmailVerification:
    protocol = request.headers.get("x-forwarded-proto", "https")
    host = request.headers.get("host", "")
    record = email_verification_service.resend_verification(
        current_person,
        verification_id,
        protocol,
        host,
        background_tasks,
        email_verification_container,
    )
    audit_logger.log_event(
        event_type="email_verification_resend",
        user_id=current_person,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
        details={"verification_id": str(verification_id)},
    )
    return record
