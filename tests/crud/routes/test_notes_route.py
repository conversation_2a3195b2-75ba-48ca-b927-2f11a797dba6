import os
import sys
import types
from unittest.mock import patch
from uuid import uuid4

from fastapi import FastAPI
from fastapi.testclient import TestClient

from tests.conftest import DummyContainer

cosmos_stub = types.ModuleType("azure.cosmos")
cosmos_stub.CosmosClient = object
cosmos_stub.DatabaseProxy = object
cosmos_stub.ContainerProxy = object

# Stub azure.cosmos.aio
cosmos_aio_stub = types.ModuleType("azure.cosmos.aio")
cosmos_aio_stub.ContainerProxy = object
cosmos_aio_stub.CosmosClient = object
cosmos_aio_stub.DatabaseProxy = object
cosmos_stub.aio = cosmos_aio_stub

# Stub azure.cosmos.exceptions
cosmos_exceptions_stub = types.ModuleType("azure.cosmos.exceptions")
cosmos_exceptions_stub.CosmosHttpResponseError = Exception
cosmos_exceptions_stub.CosmosResourceNotFoundError = Exception
cosmos_stub.exceptions = cosmos_exceptions_stub

sys.modules.setdefault("azure.cosmos", cosmos_stub)
sys.modules.setdefault("azure.cosmos.aio", cosmos_aio_stub)
sys.modules.setdefault("azure.cosmos.exceptions", cosmos_exceptions_stub)

REPO_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if REPO_ROOT not in sys.path:
    sys.path.insert(0, REPO_ROOT)

from peepsapi.crud.models.note import NoteObjectType
from peepsapi.crud.routes import notes as notes_route
from peepsapi.crud.services.notes_service import NotesService


class DummyNote:
    def __init__(self, **kwargs):
        self.version = kwargs.get("version", 1)
        for k, v in kwargs.items():
            setattr(self, k, v)

    @staticmethod
    def soft_delete_fields(author_id, delete_reason):
        from peepsapi.models import now

        return {
            "deleted": True,
            "deleted_at": now(),
            "deleted_by": author_id,
            "delete_reason": delete_reason,
            "updated_at": now(),
        }


# Unit Tests ---------------------------------------------------------------


@patch("peepsapi.crud.services.notes_service.Note", DummyNote)
def test_create_note_populates_fields():
    service = NotesService()
    container = DummyContainer()

    author = uuid4()
    obj = uuid4()
    note = service.create_note(
        author, obj, NoteObjectType.PERSON, "hello", notes_container=container
    )
    assert note.author_id == author
    assert note.object_id == obj
    assert note.content == "hello"
    assert note.version == 1
    assert note.note_group_id
    assert note.created_at is not None


@patch("peepsapi.crud.services.notes_service.Note", DummyNote)
def test_create_note_requires_content():
    service = NotesService()
    container = DummyContainer()
    author = uuid4()
    obj = uuid4()
    import pytest

    with pytest.raises(Exception):
        service.create_note(
            author, obj, NoteObjectType.PERSON, "", notes_container=container
        )


@patch("peepsapi.crud.services.notes_service.Note", DummyNote)
def test_update_delete_authorization():
    service = NotesService()
    container = DummyContainer()
    author = uuid4()
    obj = uuid4()
    note = service.create_note(
        author, obj, NoteObjectType.PERSON, "hi", notes_container=container
    )
    other = uuid4()
    import pytest

    # Patch container to raise on unauthorized update/delete
    def patch_model_raise(item, partition_key, update_fields, model_class=None):
        obj = container.storage[item]
        if hasattr(obj, "author_id") and obj.author_id != partition_key:
            raise Exception("Unauthorized")
        for k, v in update_fields.items():
            setattr(obj, k, v)
        container.patched.append((item, update_fields))
        return obj

    container.patch_model = patch_model_raise

    with pytest.raises(Exception):
        service.update_note(
            note.id, other, {"content": "no"}, notes_container=container
        )
    with pytest.raises(Exception):
        service.delete_note(note.id, other, notes_container=container)


@patch("peepsapi.crud.services.notes_service.Note", DummyNote)
def test_deleted_notes_not_returned():
    service = NotesService()
    container = DummyContainer()
    author = uuid4()
    obj = uuid4()
    note = service.create_note(
        author, obj, NoteObjectType.PERSON, "hi", notes_container=container
    )
    service.delete_note(note.id, author, notes_container=container)
    notes = service.get_notes_for_object(
        author, obj, NoteObjectType.PERSON, notes_container=container
    )
    assert notes == []


# Integration Tests -------------------------------------------------------


@patch("peepsapi.crud.routes.notes.service", NotesService())
def test_full_crud_flow(monkeypatch):
    container = DummyContainer()

    current_person = {"id": uuid4()}

    async def get_person():
        return current_person["id"]

    def get_container():
        return container

    app = FastAPI()  # This is a FastAPI instance, not a plain object
    app.include_router(notes_route.router)
    app.dependency_overrides[notes_route.auth_service.get_current_person] = get_person
    app.dependency_overrides[notes_route.get_notes_container] = get_container

    client = TestClient(app)

    obj_id = uuid4()

    # Create
    response = client.post(
        f"/notes/{NoteObjectType.PERSON.value}/{obj_id}",
        json={
            "content": "hi <script>x</script>",
        },
    )
    assert response.status_code == 200
    data = response.json()
    note_id = data["id"]
    assert data["content"] == "hi"
    author_id = current_person["id"]

    # Unauthorized update
    other = uuid4()
    current_person["id"] = other
    response = client.patch(f"/notes/{note_id}", json={"content": "bad"})
    assert response.status_code == 404

    # Authorized update
    current_person["id"] = author_id
    response = client.patch(f"/notes/{note_id}", json={"content": "updated"})
    assert response.status_code == 200
    assert response.json()["content"] == "updated"

    # Unauthorized delete
    current_person["id"] = other
    response = client.delete(f"/notes/{note_id}")
    print(f"response: {response}")
    assert response.status_code == 404

    # Authorized delete
    current_person["id"] = author_id
    response = client.delete(f"/notes/{note_id}")
    assert response.status_code == 200
    assert response.json() == {"ok": True}

    # Ensure gone
    response = client.get(f"/notes/{NoteObjectType.PERSON.value}/{obj_id}")
    assert response.status_code == 200
    assert response.json() == []
