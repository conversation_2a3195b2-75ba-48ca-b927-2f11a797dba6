import os
import sys
import types
from unittest.mock import patch
from uuid import uuid4

import peepsapi.crud.services.picture_service as picture_service
from peepsapi.crud.models.person import Person
from peepsapi.crud.services.picture_service import PictureService
from peepsapi.crud.utils.constants import DEFAULT_PICTURE_PATH, PICTURE_CONTAINER_NAME


def create_test_image_bytes() -> bytes:
    return b"fake-image-bytes"


def test_extract_image_metadata_basic_fields(monkeypatch):
    service = PictureService()

    class DummyImage:
        format = "PNG"
        mode = "RGB"
        is_animated = False

        def __init__(self):
            self.size = (10, 20)

        def __enter__(self):
            return self

        def __exit__(self, exc_type, exc, tb):
            pass

        def getexif(self):
            return {}

        def getcolors(self, maxcolors=256):
            return []

    dummy_module = types.SimpleNamespace(open=lambda *_args, **_kwargs: DummyImage())
    monkeypatch.setattr(picture_service, "Image", dummy_module)

    img_bytes = create_test_image_bytes()
    metadata = service.extract_image_metadata(img_bytes)
    assert metadata["width"] == 10
    assert metadata["height"] == 20
    assert metadata["format"] == "PNG"
    assert metadata["mode"] == "RGB"
    assert metadata["is_animated"] is False


@patch.object(PictureService, "_get_latest", return_value=None)
@patch("peepsapi.crud.services.picture_service.download_blob")
def test_get_picture_returns_default_when_missing(mock_download, mock_get_latest):
    mock_download.return_value = b"default"
    service = PictureService()
    metadata, data = service.get_picture(Person, uuid4())
    assert metadata is None
    assert data == b"default"
    mock_download.assert_called_once_with(
        PICTURE_CONTAINER_NAME, DEFAULT_PICTURE_PATH["person"]["full"]
    )


@patch.object(PictureService, "_get_latest", return_value=None)
@patch(
    "peepsapi.crud.services.picture_service.download_blob",
    side_effect=Exception("missing"),
)
def test_get_picture_returns_empty_when_default_missing(mock_download, mock_get_latest):
    service = PictureService()
    metadata, data = service.get_picture(Person, uuid4())
    assert metadata is None
    assert data == b""
    mock_download.assert_called_once_with(
        PICTURE_CONTAINER_NAME, DEFAULT_PICTURE_PATH["person"]["full"]
    )
