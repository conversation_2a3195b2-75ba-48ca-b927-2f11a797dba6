import os
import sys
import types
from types import SimpleNamespace
from unittest.mock import patch
from uuid import uuid4

from tests.conftest import Dummy<PERSON>ontainer

# Only stub external heavy dependencies that prevent imports
pydantic_stub = types.ModuleType("pydantic")


class _BaseModel(dict):
    pass


pydantic_stub.BaseModel = _BaseModel
pydantic_stub.Field = lambda default=None, **kwargs: default
pydantic_stub.model_validator = lambda *args, **kwargs: lambda func: func
sys.modules.setdefault("pydantic", pydantic_stub)

dotenv_stub = types.ModuleType("dotenv")
dotenv_stub.load_dotenv = lambda *a, **k: None
sys.modules.setdefault("dotenv", dotenv_stub)

fastapi_stub = types.ModuleType("fastapi")
fastapi_stub.Request = object
fastapi_stub.HTTPException = type("HTTPException", (), {})
fastapi_stub.status = SimpleNamespace(
    HTTP_204_NO_CONTENT=204,
    HTTP_403_FORBIDDEN=403,
    HTTP_400_BAD_REQUEST=400,
    HTTP_404_NOT_FOUND=404,
    HTTP_500_INTERNAL_SERVER_ERROR=500,
    HTTP_429_TOO_MANY_REQUESTS=429,
)
sys.modules.setdefault("fastapi", fastapi_stub)

cosmos_stub = types.ModuleType("azure.cosmos")
cosmos_stub.CosmosClient = object
cosmos_stub.DatabaseProxy = object
cosmos_stub.ContainerProxy = object

# Stub azure.cosmos.aio
cosmos_aio_stub = types.ModuleType("azure.cosmos.aio")
cosmos_aio_stub.ContainerProxy = object
cosmos_aio_stub.CosmosClient = object
cosmos_aio_stub.DatabaseProxy = object
cosmos_stub.aio = cosmos_aio_stub

# Stub azure.cosmos.exceptions
cosmos_exceptions_stub = types.ModuleType("azure.cosmos.exceptions")
cosmos_exceptions_stub.CosmosHttpResponseError = Exception
cosmos_exceptions_stub.CosmosResourceNotFoundError = Exception
cosmos_stub.exceptions = cosmos_exceptions_stub

sys.modules.setdefault("azure.cosmos", cosmos_stub)
sys.modules.setdefault("azure.cosmos.aio", cosmos_aio_stub)
sys.modules.setdefault("azure.cosmos.exceptions", cosmos_exceptions_stub)

REPO_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
if REPO_ROOT not in sys.path:
    sys.path.insert(0, REPO_ROOT)

from peepsapi.crud.models.note import NoteObjectType
from peepsapi.crud.services.notes_service import NotesService
from peepsapi.utils.markdown import sanitize_markdown


class DummyNote:
    def __init__(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)

    @staticmethod
    def soft_delete_fields(author_id, delete_reason):
        from peepsapi.models import now

        return {
            "deleted": True,
            "deleted_at": now(),
            "deleted_by": author_id,
            "delete_reason": delete_reason,
            "updated_at": now(),
        }


def test_sanitize_markdown_removes_script():
    md = "Hello <script>alert('x')</script> World"
    assert "script" not in sanitize_markdown(md)


@patch("peepsapi.crud.services.notes_service.Note", DummyNote)
@patch("peepsapi.crud.services.notes_service.get_notes_container")
def test_create_note_sanitizes_content_and_stores(mock_get_container):
    container = DummyContainer()
    mock_get_container.return_value = container
    service = NotesService()

    author = uuid4()
    obj = uuid4()
    note = service.create_note(
        author,
        obj,
        NoteObjectType.PERSON,
        "hi <script>bad</script>",
        notes_container=container,
    )
    assert note.content == "hi"
    assert note in container.created


@patch("peepsapi.crud.services.notes_service.Note", DummyNote)
@patch("peepsapi.crud.services.notes_service.get_notes_container")
def test_update_note_authorization(mock_get_container):
    container = DummyContainer()
    mock_get_container.return_value = container
    service = NotesService()

    author = uuid4()
    obj = uuid4()
    note = service.create_note(
        author, obj, NoteObjectType.PERSON, "hi", notes_container=container
    )
    other = uuid4()
    import pytest

    with pytest.raises(Exception):
        service.update_note(note.id, other, {"content": "no"})
