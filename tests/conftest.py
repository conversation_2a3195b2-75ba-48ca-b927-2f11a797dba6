# conftest.py -- shared test configuration and mocks for PeepsAPI
#
# - All shared mocks (Du<PERSON><PERSON><PERSON><PERSON>, DummyEmailService, DummyBackgroundTasks) are defined here.
# - Pytest fixtures are provided for each.
# - FastAPI/TestClient are NOT stubbed by default; set MOCK_FASTAPI=1 to enable stubs for pure logic tests.
# - Import these mocks/fixtures in your test files to avoid duplication.

import os
import sys
import types
import warnings
from pathlib import Path
from uuid import UUID

import pytest

# Suppress httpx deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="httpx")

from azure.cosmos.exceptions import CosmosResourceNotFoundError

# Add the project root directory to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def pytest_sessionstart(session):
    os.environ["JWT_SECRET"] = "test_secret"


# Provide a minimal azure storage stub to avoid import errors from azure_blob.
azure_blob_stub = types.ModuleType("azure")
storage_stub = types.ModuleType("azure.storage")
storage_blob_stub = types.ModuleType("azure.storage.blob")
storage_blob_stub.BlobServiceClient = object
storage_stub.blob = storage_blob_stub
azure_blob_stub.storage = storage_stub
sys.modules.setdefault("azure", azure_blob_stub)
sys.modules.setdefault("azure.storage", storage_stub)
sys.modules.setdefault("azure.storage.blob", storage_blob_stub)

# Additional stubs used during configuration import
identity_stub = types.ModuleType("azure.identity")
identity_stub.DefaultAzureCredential = object
keyvault_stub = types.ModuleType("azure.keyvault")
keyvault_secrets_stub = types.ModuleType("azure.keyvault.secrets")
keyvault_secrets_stub.SecretClient = object
keyvault_stub.secrets = keyvault_secrets_stub
azure_blob_stub.identity = identity_stub
azure_blob_stub.keyvault = keyvault_stub
sys.modules.setdefault("azure.identity", identity_stub)
sys.modules.setdefault("azure.keyvault", keyvault_stub)
sys.modules.setdefault("azure.keyvault.secrets", keyvault_secrets_stub)

cosmos_stub = types.ModuleType("azure.cosmos")
cosmos_stub.CosmosClient = object
cosmos_stub.DatabaseProxy = object
cosmos_stub.ContainerProxy = object

# Stub azure.cosmos.aio
cosmos_aio_stub = types.ModuleType("azure.cosmos.aio")
cosmos_aio_stub.ContainerProxy = object
cosmos_aio_stub.CosmosClient = object
cosmos_aio_stub.DatabaseProxy = object
cosmos_stub.aio = cosmos_aio_stub

# Stub azure.cosmos.exceptions
cosmos_exceptions_stub = types.ModuleType("azure.cosmos.exceptions")
cosmos_exceptions_stub.CosmosHttpResponseError = Exception
cosmos_exceptions_stub.CosmosResourceNotFoundError = Exception
cosmos_stub.exceptions = cosmos_exceptions_stub

sys.modules.setdefault("azure.cosmos", cosmos_stub)
sys.modules.setdefault("azure.cosmos.aio", cosmos_aio_stub)
sys.modules.setdefault("azure.cosmos.exceptions", cosmos_exceptions_stub)

dotenv_stub = types.ModuleType("dotenv")
dotenv_stub.load_dotenv = lambda *a, **k: None
sys.modules.setdefault("dotenv", dotenv_stub)

# Stub auth services to avoid FIDO2 configuration requirements
auth_service_stub = types.ModuleType("peepsapi.auth.services.auth_service")


class _AuthService:
    def get_current_person(self):
        pass

    def get_auth_source(self):
        pass

    def get_actor_id(self):
        pass


auth_service_stub.auth_service = _AuthService()
sys.modules.setdefault("peepsapi.auth.services.auth_service", auth_service_stub)

# Stub challenge service to prevent module-level instantiation
challenge_service_stub = types.ModuleType("peepsapi.auth.services.challenge_service")


class _ChallengeService:
    def deactivate_credentials_by_id_or_person_id(self, *args, **kwargs):
        pass


challenge_service_stub.ChallengeService = _ChallengeService
challenge_service_stub.challenge_service = _ChallengeService()
sys.modules.setdefault(
    "peepsapi.auth.services.challenge_service", challenge_service_stub
)

# Only stub pydantic if MOCK_PYDANTIC=1
if os.getenv("MOCK_PYDANTIC", "0") == "1":
    pydantic_stub = types.ModuleType("pydantic")

    class _BaseModel(dict):
        pass

    pydantic_stub.BaseModel = _BaseModel
    pydantic_stub.Field = lambda default=None, **kwargs: default
    pydantic_stub.field_serializer = lambda *a, **k: (lambda f: f)
    pydantic_stub.EmailStr = str
    pydantic_stub.field_validator = lambda *a, **k: (lambda f: f)
    pydantic_stub.model_validator = lambda *a, **k: (lambda f: f)
    sys.modules["pydantic"] = pydantic_stub


# Shared DummyContainer
class DummyContainer:
    def __init__(self):
        self.storage = {}
        self.patched = []
        self.created = []  # Add back for backward compatibility

    def create_model(self, model):
        self.storage[model.id] = model
        self.created.append(model)  # Add back for backward compatibility
        return model

    def read_model(self, id, partition_key, model_class=None):
        print(
            f"DummyContainer.read_model called with id={id}, partition_key={partition_key}, type={type(partition_key)}"
        )
        # Convert string id to UUID for lookup if needed
        lookup_id = id
        if isinstance(id, str):
            lookup_id = UUID(id)

        if lookup_id not in self.storage:
            print(f"ID {lookup_id} not found in storage")
            raise CosmosResourceNotFoundError("Not found or unauthorized")
        model = self.storage[lookup_id]
        print(f"Found model: {model}")
        # Check partition key based on model type
        if hasattr(model, "author_id"):  # Note model
            print(
                f"Note model - author_id: {model.author_id}, partition_key: {partition_key}"
            )
            if str(model.author_id) != str(partition_key):
                print(f"Note authorization failed")
                raise CosmosResourceNotFoundError(404, "Not found or unauthorized")
        elif hasattr(model, "invited_by"):  # Invite model
            print(
                f"Invite model - invited_by: {model.invited_by}, partition_key: {partition_key}"
            )
            # For invites, partition_key should be the invite ID, not user ID
            # Convert partition_key to UUID if it's a string
            if isinstance(partition_key, str):
                partition_key = UUID(partition_key)
            print(
                f"Converted partition_key: {partition_key}, type: {type(partition_key)}"
            )
            # For invites, we don't check authorization in read_model - that's handled elsewhere
            print(f"Invite read successful")
        # Check if deleted
        if getattr(model, "deleted", False):
            print(f"Model is deleted")
            raise CosmosResourceNotFoundError("Not found or unauthorized")
        return model

    def patch_model(self, item, partition_key, update_fields, model_class=None):
        print(
            f"DummyContainer.patch_model called with item={item}, partition_key={partition_key}, type={type(partition_key)}"
        )
        # Convert string item to UUID for lookup if needed
        lookup_item = item
        if isinstance(item, str):
            lookup_item = UUID(item)

        if lookup_item not in self.storage:
            print(f"Item {lookup_item} not found in storage")
            raise CosmosResourceNotFoundError("Not found or unauthorized")
        model = self.storage[lookup_item]
        print(f"Found model for patch: {model}")
        # Check partition key based on model type
        if hasattr(model, "author_id"):  # Note model
            print(
                f"Note model - author_id: {model.author_id}, partition_key: {partition_key}"
            )
            if str(model.author_id) != str(partition_key):
                print(f"Note authorization failed")
                raise CosmosResourceNotFoundError(404, "Not found or unauthorized")
        elif hasattr(model, "invited_by"):  # Invite model
            print(
                f"Invite model - invited_by: {model.invited_by}, partition_key: {partition_key}"
            )
            # For invites, partition_key should be the invite ID, not user ID
            # Convert partition_key to UUID if it's a string
            if isinstance(partition_key, str):
                partition_key = UUID(partition_key)
            print(
                f"Converted partition_key: {partition_key}, type: {type(partition_key)}"
            )
            # For invites, we don't check authorization in patch_model - that's handled elsewhere
            print(f"Invite patch successful")
        # Check if deleted
        if getattr(model, "deleted", False):
            print(f"Model is deleted")
            raise CosmosResourceNotFoundError("Not found or unauthorized")
        for k, v in update_fields.items():
            setattr(model, k, v)
        self.patched.append((item, update_fields))
        return model

    def query_models(
        self, query, parameters=None, model_class=None, partition_key=None
    ):
        models = list(self.storage.values())
        # Filter by partition key if specified
        if partition_key:
            models = [
                m
                for m in models
                if hasattr(m, "author_id") and str(m.author_id) == str(partition_key)
            ]
        # Filter out deleted notes
        models = [m for m in models if not getattr(m, "deleted", False)]
        return models


# Shared DummyEmailService
class DummyEmailService:
    def __init__(self):
        self.sent = {"recipient": None, "template": None, "context": None}

    def send_email(self, recipient, template_name, context, **_):
        self.sent["recipient"] = recipient
        self.sent["template"] = template_name
        self.sent["context"] = context


# Shared DummyBackgroundTasks
class DummyBackgroundTasks:
    def add_task(self, *a, **k):
        pass


@pytest.fixture
def dummy_container():
    return DummyContainer()


@pytest.fixture
def dummy_email_service():
    return DummyEmailService()


@pytest.fixture
def dummy_background_tasks():
    return DummyBackgroundTasks()


# Optionally stub FastAPI/TestClient if MOCK_FASTAPI=1
if os.getenv("MOCK_FASTAPI", "0") == "1":
    fastapi_stub = types.ModuleType("fastapi")
    fastapi_stub.FastAPI = object
    fastapi_stub.APIRouter = type(
        "APIRouter", (), {"include_router": lambda self, *a, **k: None}
    )
    fastapi_stub.Depends = lambda *a, **k: None
    fastapi_stub.Response = object
    fastapi_stub.BackgroundTasks = object
    fastapi_stub.Cookie = lambda *a, **k: None
    fastapi_stub.Request = object
    fastapi_stub.HTTPException = type("HTTPException", (), {})
    fastapi_stub.status = types.SimpleNamespace(HTTP_204_NO_CONTENT=204)
    sys.modules["fastapi"] = fastapi_stub
    testclient_stub = types.ModuleType("fastapi.testclient")
    testclient_stub.TestClient = object
    sys.modules["fastapi.testclient"] = testclient_stub
    fastapi_responses_stub = types.ModuleType("fastapi.responses")
    fastapi_responses_stub.RedirectResponse = object
    fastapi_responses_stub.JSONResponse = object
    sys.modules["fastapi.responses"] = fastapi_responses_stub

# Export DummyContainer for use in tests
