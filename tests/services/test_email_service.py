import pytest

from peepsapi.services.email_service import EmailService, ValidationError
from peepsapi.utils.error_handling import ServerError


@pytest.fixture(autouse=True)
def patch_validate_email(monkeypatch):
    from email_validator import validate_email as real_validate_email

    def fake_validate_email(address, *args, **kwargs):
        # Only patch for test addresses, otherwise call the real function
        if address.endswith("@example.com"):

            class Result:
                email = address

            return Result()
        return real_validate_email(address, *args, **kwargs)

    # Patch the reference used in your service module
    monkeypatch.setattr(
        "peepsapi.services.email_service.validate_email", fake_validate_email
    )


def test_send_email_success(tmp_path, monkeypatch):
    tmpl = tmp_path / "invite_v1.html"
    tmpl.write_text("Hello {{ user_name }} {{ link }}")
    service = EmailService(template_dir=str(tmp_path))

    sent = {}

    def fake_send(recipient, subject, body, max_retries):
        sent["recipient"] = recipient
        sent["subject"] = subject
        sent["body"] = body

    monkeypatch.setattr(service, "_send_via_acs", fake_send)
    service.send_email(
        "<EMAIL>",
        "invite",
        {"user_name": "Bob", "link": "http://test"},
    )
    assert sent["recipient"] == "<EMAIL>"
    assert "Bob" in sent["body"]
    assert "http://test" in sent["body"]


def test_missing_template(tmp_path):
    service = EmailService(template_dir=str(tmp_path))
    with pytest.raises(FileNotFoundError):
        service.send_email("<EMAIL>", "invite")


def test_invalid_email(tmp_path):
    (tmp_path / "invite_v1.html").write_text("hi")
    service = EmailService(template_dir=str(tmp_path))
    with pytest.raises(ValidationError):
        service.send_email("bad", "invite")


def test_bounced_email(tmp_path, monkeypatch):
    (tmp_path / "invite_v1.html").write_text("hi")
    service = EmailService(template_dir=str(tmp_path))
    monkeypatch.setattr(service, "_is_bounced", lambda _: True)
    with pytest.raises(ValidationError):
        service.send_email("<EMAIL>", "invite")


def test_template_version(tmp_path, monkeypatch):
    (tmp_path / "invite_v2.html").write_text("V2 {{ user_name }}")
    service = EmailService(template_dir=str(tmp_path))
    captured = {}
    monkeypatch.setattr(
        service, "_send_via_acs", lambda r, s, b, max_retries: captured.update(body=b)
    )
    service.send_email("<EMAIL>", "invite", {"user_name": "Ann"}, version="v2")
    assert "V2" in captured.get("body", "")


def test_email_retry_on_failure(tmp_path, monkeypatch):
    """Test that email sending retries on failure with exponential backoff."""
    tmpl = tmp_path / "invite_v1.html"
    tmpl.write_text("Hello {{ user_name }}")
    service = EmailService(template_dir=str(tmp_path))

    attempts = []
    delays = []

    class DummyPoller:
        def __init__(self, attempts):
            self.attempts = attempts

        class Result:
            def __init__(self, status):
                self.status = status

        def result(self):
            # The current attempt is the last in the attempts list
            if self.attempts[-1] < 3:
                return self.Result("failed")
            else:
                return self.Result("succeeded")

    # Mock the client.send method to simulate failures
    def fake_client_send(message):
        attempts.append(len(attempts) + 1)
        return DummyPoller(attempts)

    def fake_sleep(delay):
        delays.append(delay)

    # Create a mock client
    class MockClient:
        def begin_send(self, message):
            return fake_client_send(message)

    monkeypatch.setattr(service, "_get_client", lambda: MockClient())
    monkeypatch.setattr("time.sleep", fake_sleep)

    # Test with custom retry count
    service.send_email_with_retry(
        "<EMAIL>", "invite", {"user_name": "Bob"}, max_retries=3
    )

    # Should have attempted 3 times
    assert len(attempts) == 3
    # Should have slept with exponential backoff (1s, 2s)
    assert delays == [1, 2]


def test_email_fails_after_all_retries(tmp_path, monkeypatch):
    """Test that email sending fails after all retry attempts."""
    tmpl = tmp_path / "invite_v1.html"
    tmpl.write_text("Hello {{ user_name }}")
    service = EmailService(template_dir=str(tmp_path))

    def fake_send_always_fails(recipient, subject, body, max_retries):
        raise Exception("Persistent failure")

    monkeypatch.setattr(service, "_send_with_retry", fake_send_always_fails)

    with pytest.raises(Exception, match="Persistent failure"):
        service.send_email_with_retry(
            "<EMAIL>", "invite", {"user_name": "Bob"}, max_retries=2
        )
