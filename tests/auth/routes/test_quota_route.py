from datetime import timed<PERSON>ta
from uuid import uuid4

from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

from peepsapi.auth.models.invite import Invite, InviteStatus
from peepsapi.auth.services import auth_service as auth_service_module
from peepsapi.auth.services.invite_service import InviteService
from peepsapi.models import now
from peepsapi.models.base import IdentifierType
from peepsapi.services.cosmos_containers import (
    get_invite_quotas_container,
    get_invite_tokens_container,
    get_people_container,
)
from tests.conftest import DummyContainer


def build_app(monkeypatch):
    invite_container = DummyContainer()
    quota_container = DummyContainer()
    people_container = DummyContainer()
    service = InviteService()
    from peepsapi.dashboard.routes import quota as quota_route

    monkeypatch.setattr(quota_route, "invite_service", service)
    app = FastAPI()
    app.include_router(quota_route.router)
    app.dependency_overrides[get_invite_tokens_container] = lambda: invite_container
    app.dependency_overrides[get_invite_quotas_container] = lambda: quota_container
    app.dependency_overrides[get_people_container] = lambda: people_container
    app.dependency_overrides[auth_service_module.get_auth_source] = lambda: "azure_ad"
    app.dependency_overrides[auth_service_module.get_current_person] = lambda: uuid4()
    return app, service, invite_container, quota_container, people_container


def test_assign_and_usage(monkeypatch):
    app, service, invites, quotas, people = build_app(monkeypatch)
    person_id = uuid4()
    from peepsapi.crud.models.person import Person

    p = Person(id=person_id)
    people.create_model(p)
    client = TestClient(app)
    res = client.post(f"/quota/{person_id}/set/2")
    assert res.status_code == 200
    res = client.get(f"/quota/{person_id}")
    assert res.json()["quota"] == 2
    assert res.json()["used"] == 0
    inv = Invite(
        id=uuid4(),
        token="t",
        identifier_value="<EMAIL>",
        identifier_type=IdentifierType.EMAIL,
        person_id=uuid4(),
        invited_by=[person_id],
        created_at=now(),
        expires_at=now() + timedelta(days=1),
        status=InviteStatus.SENT,
        resend_count=0,
    )
    invites.create_model(inv)
    res = client.get(f"/quota/{person_id}")
    assert res.json()["used"] == 1


def test_increase_quota(monkeypatch):
    app, service, invites, quotas, people = build_app(monkeypatch)
    person_id = uuid4()
    from peepsapi.crud.models.person import Person

    people.create_model(Person(id=person_id))
    client = TestClient(app)
    client.post(f"/quota/{person_id}/set/1")
    res = client.post(f"/quota/{person_id}/increase/2")
    assert res.status_code == 200
    assert res.json()["quota"] == 3
