from uuid import uuid4

from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.routes import recovery as route
from peepsapi.auth.services import auth_service as auth_service_module
from peepsapi.auth.services import device_service as device_service_module
from peepsapi.auth.services import registration_rate_limiter
from peepsapi.auth.services.recovery_service import RecoveryService
from peepsapi.services.cosmos_containers import (
    get_people_container,
    get_recovery_tokens_container,
)
from tests.conftest import DummyContainer, DummyEmailService


def build_app(monkeypatch):
    recovery_tokens_container = DummyContainer()
    people_container = DummyContainer()
    service = RecoveryService()
    service.email_service = DummyEmailService()
    monkeypatch.setattr(route, "recovery_service", service)

    async def _create(*a, **k):
        return {"publicKey": {}}, "c"

    monkeypatch.setattr(
        route, "challenge_service", type("C", (), {"create_challenge": _create})()
    )
    app = FastAPI()
    app.include_router(route.router)
    user = uuid4()
    app.dependency_overrides[auth_service_module.get_current_person] = lambda: user
    app.dependency_overrides[
        get_recovery_tokens_container
    ] = lambda: recovery_tokens_container
    app.dependency_overrides[get_people_container] = lambda: people_container
    from peepsapi.models import now

    app.dependency_overrides[
        device_service_module.extract_device_info
    ] = lambda: DeviceInfo(name="t", type="t", created_at=now())
    app.dependency_overrides[registration_rate_limiter.check_rate_limit] = lambda: True
    return app, service, recovery_tokens_container, people_container


def test_initiate(monkeypatch):
    app, service, recovery_tokens_container, people_container = build_app(monkeypatch)
    # pretend existing person
    from uuid import uuid4

    people_container.storage["p"] = type("P", (), {"id": uuid4()})
    monkeypatch.setattr(
        "peepsapi.crud.services.people_services.people_service.get_people_by_identifier",
        lambda *a, **k: [people_container.storage["p"]],
    )
    client = TestClient(app)
    res = client.post("/recover/initiate", json={"email": "<EMAIL>"})
    assert res.status_code == 200
    assert service.email_service.sent["recipient"] == "<EMAIL>"


def test_validate(monkeypatch):
    app, service, recovery_tokens_container, people_container = build_app(monkeypatch)
    from uuid import uuid4

    person = type("P", (), {"id": uuid4()})
    people_container.storage["p"] = person
    monkeypatch.setattr(
        "peepsapi.crud.services.people_services.people_service.get_people_by_identifier",
        lambda *a, **k: [person],
    )
    client = TestClient(app)
    client.post("/recover/initiate", json={"email": "<EMAIL>"})
    token = next(iter(recovery_tokens_container.storage.values()))
    res = client.post(f"/recover/challenge?token={token.token}")
    assert res.status_code == 200
    data = res.json()
    assert "publicKey" in data
