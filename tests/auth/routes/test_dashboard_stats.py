from datetime import timed<PERSON><PERSON>
from uuid import uuid4

from fastapi import FastAPI
from fastapi.testclient import TestClient

from peepsapi.auth.models.email_verification import (
    EmailVerification,
    VerificationStatus,
)
from peepsapi.auth.models.invite import Invite, InviteStatus
from peepsapi.auth.models.recovery import Recovery, RecoveryStatus
from peepsapi.auth.services import auth_service as auth_service_module
from peepsapi.auth.services.invite_service import InviteService
from peepsapi.models import now
from peepsapi.models.base import IdentifierType
from peepsapi.services.cosmos_containers import (
    get_email_verifications_container,
    get_invite_tokens_container,
    get_recovery_tokens_container,
)
from tests.conftest import DummyContainer


def build_app(monkeypatch):
    invite_container = DummyContainer()
    ver_container = DummyContainer()
    rec_container = DummyContainer()
    service = InviteService()
    auth_service_module.get_auth_source = lambda: "azure_ad"
    from peepsapi.dashboard.routes import stats as stats_route

    monkeypatch.setattr(stats_route, "invite_service", service)
    app = FastAPI()
    app.include_router(stats_route.router, prefix="/stats")
    app.dependency_overrides[get_invite_tokens_container] = lambda: invite_container
    app.dependency_overrides[get_email_verifications_container] = lambda: ver_container
    app.dependency_overrides[get_recovery_tokens_container] = lambda: rec_container
    app.dependency_overrides[auth_service_module.get_auth_source] = lambda: "azure_ad"
    return app, invite_container, ver_container, rec_container, service


def test_email_stats(monkeypatch):
    app, invites, vers, recs, _ = build_app(monkeypatch)
    inv = Invite(
        id=uuid4(),
        token="t",
        identifier_value="<EMAIL>",
        identifier_type=IdentifierType.EMAIL,
        person_id=uuid4(),
        invited_by=[uuid4()],
        created_at=now(),
        expires_at=now() + timedelta(days=1),
        status=InviteStatus.SENT,
        resend_count=0,
    )
    invites.create_model(inv)
    inv2 = Invite(
        id=uuid4(),
        token="t2",
        identifier_value="<EMAIL>",
        identifier_type=IdentifierType.EMAIL,
        person_id=uuid4(),
        invited_by=[uuid4()],
        created_at=now(),
        expires_at=now() + timedelta(days=1),
        status=InviteStatus.CANCELLED,
        resend_count=0,
    )
    invites.create_model(inv2)

    ver = EmailVerification(
        id=uuid4(),
        token="v",
        email="<EMAIL>",
        person_id=uuid4(),
        created_at=now(),
        expires_at=now() + timedelta(days=1),
        status=VerificationStatus.SENT,
    )
    vers.create_model(ver)
    ver2 = EmailVerification(
        id=uuid4(),
        token="v2",
        email="<EMAIL>",
        person_id=uuid4(),
        created_at=now(),
        expires_at=now() + timedelta(days=1),
        status=VerificationStatus.EXPIRED,
    )
    vers.create_model(ver2)

    rec = Recovery.create(IdentifierType.EMAIL, "<EMAIL>", uuid4())
    rec.status = RecoveryStatus.SENT
    rec_container = recs
    rec_container.create_model(rec)
    rec2 = Recovery.create(IdentifierType.EMAIL, "<EMAIL>", uuid4())
    rec2.status = RecoveryStatus.EXPIRED
    rec_container.create_model(rec2)

    client = TestClient(app)
    res = client.get("/stats/email")
    assert res.status_code == 200
    data = res.json()
    assert data["invite"]["sent"] == 1
    assert data["invite"]["cancelled"] == 1
    assert data["verification"]["sent"] == 1
    assert data["verification"]["expired"] == 1
    assert data["recovery"]["sent"] == 1
    assert data["recovery"]["expired"] == 1
