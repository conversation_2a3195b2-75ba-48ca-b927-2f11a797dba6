from uuid import uuid4

from fastapi import FastAPI
from fastapi.testclient import TestClient

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.invite import Invite, InviteStatus
from peepsapi.auth.routes import invite as invite_route
from peepsapi.auth.services import auth_service as auth_service_module
from peepsapi.auth.services import device_service as device_service_module
from peepsapi.auth.services import registration_rate_limiter
from peepsapi.auth.services.invite_service import InviteService
from peepsapi.crud.models.person import Person
from peepsapi.crud.services import people_services
from peepsapi.models import now
from peepsapi.models.base import IdentifierType
from peepsapi.services.cosmos_containers import (
    get_invite_tokens_container,
    get_people_container,
)
from tests.conftest import DummyContainer, DummyEmailService


def build_app(monkeypatch):
    invite_container = DummyContainer()
    people_container = DummyContainer()
    service = InviteService()
    service.email_service = DummyEmailService()
    monkeypatch.setattr("peepsapi.auth.routes.invite.invite_service", service)
    app = FastAPI()
    app.include_router(invite_route.router)
    user = uuid4()
    people_container.create_model(Person(id=user, remaining_invites=5))

    def test_get_people_by_identifier(
        identifier_type, identifier_value, people_container
    ):
        matches = []
        for person in people_container.storage.values():
            if identifier_type == IdentifierType.EMAIL:
                if any(
                    email.address == identifier_value
                    for email in getattr(person, "emails", [])
                ):
                    matches.append(person)
            elif identifier_type == IdentifierType.PHONE:
                if any(
                    phone.number == identifier_value
                    for phone in getattr(person, "phone_numbers", [])
                ):
                    matches.append(person)
        return matches

    monkeypatch.setattr(
        people_services.people_service,
        "get_people_by_identifier",
        test_get_people_by_identifier,
    )

    app.dependency_overrides[auth_service_module.get_current_person] = lambda: user
    app.dependency_overrides[
        device_service_module.extract_device_info
    ] = lambda: DeviceInfo(name="t", type="t", created_at=now())
    app.dependency_overrides[registration_rate_limiter.check_rate_limit] = lambda: True
    app.dependency_overrides[get_invite_tokens_container] = lambda: invite_container
    app.dependency_overrides[get_people_container] = lambda: people_container
    return app, service, user, invite_container, people_container


def test_create_and_cancel(monkeypatch):
    app, service, user, invite_container, people_container = build_app(monkeypatch)
    client = TestClient(app)
    res = client.post("/invite", json={"email": "<EMAIL>"})
    assert res.status_code == 200
    data = res.json()
    inv = list(invite_container.storage.values())[0]
    assert inv.identifier_value == "<EMAIL>"

    # Cancel using the invite ID from the container
    res = client.delete(f"/invite/{inv.id}")
    assert res.status_code == 200
    # Verify the invite was cancelled
    cancelled_invite = invite_container.read_model(inv.id, inv.id, model_class=Invite)
    assert cancelled_invite.status == InviteStatus.CANCELLED


def test_audit_log(monkeypatch):
    app, service, user, invite_container, people_container = build_app(monkeypatch)
    events = []
    monkeypatch.setattr(
        invite_route.audit_logger,
        "log_invite_attempt",
        lambda *a, **k: events.append((a, k)),
    )
    client = TestClient(app)
    client.post("/invite", json={"email": "<EMAIL>"})
    assert events
