from uuid import uuid4

from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

from peepsapi.auth.models.email_verification import EmailVerification
from peepsapi.auth.routes import email_verification as route
from peepsapi.auth.services import auth_service as auth_service_module
from peepsapi.auth.services.email_verification_service import EmailVerificationService
from peepsapi.crud.models.person import Email, Person
from peepsapi.models import now
from peepsapi.services.cosmos_containers import (
    get_email_verifications_container,
    get_people_container,
)
from tests.conftest import DummyContainer, DummyEmailService


def build_app(monkeypatch):
    container = DummyContainer()
    people_container = DummyContainer()
    service = EmailVerificationService()
    service.email_service = DummyEmailService()
    monkeypatch.setattr(route, "email_verification_service", service)
    app = FastAPI()
    app.include_router(route.router)
    user = uuid4()
    app.dependency_overrides[auth_service_module.get_current_person] = lambda: user
    app.dependency_overrides[get_email_verifications_container] = lambda: container
    app.dependency_overrides[get_people_container] = lambda: people_container
    person = Person(
        id=user,
        emails=[Email(type="personal", address="<EMAIL>", active_since=now())],
        member_since=now(),
    )
    people_container.create_model(person)
    return app, service, container, user, people_container


def test_flow(monkeypatch):
    app, service, container, user, people = build_app(monkeypatch)
    client = TestClient(app)
    res = client.post("/verify/email/initiate", json={"email": "<EMAIL>"})
    assert res.status_code == 200
    record = list(container.storage.values())[0]
    res = client.get(f"/verify/email/{record.token}")
    assert res.status_code == 200
    stored = container.read_model(record.id, record.id, model_class=EmailVerification)
    assert stored.status == "verified"
    person = people.read_model(user, user, model_class=Person)
    assert person.emails[0].verified


def test_rate_limit(monkeypatch):
    app, service, container, user, people = build_app(monkeypatch)

    call_count = {"n": 0}

    def limiter():
        call_count["n"] += 1
        if call_count["n"] > 1:
            from fastapi import HTTPException, status

            raise HTTPException(status_code=status.HTTP_429_TOO_MANY_REQUESTS)

    app.dependency_overrides[route.registration_rate_limiter.check_rate_limit] = limiter
    client = TestClient(app)
    assert (
        client.post(
            "/verify/email/initiate", json={"email": "<EMAIL>"}
        ).status_code
        == 200
    )
    assert (
        client.post(
            "/verify/email/initiate", json={"email": "<EMAIL>"}
        ).status_code
        == 429
    )


def test_audit_log(monkeypatch):
    app, service, container, user, _ = build_app(monkeypatch)

    events = []
    monkeypatch.setattr(
        route.audit_logger, "log_event", lambda *a, **k: events.append((a, k))
    )
    client = TestClient(app)
    client.post("/verify/email/initiate", json={"email": "<EMAIL>"})
    assert events
