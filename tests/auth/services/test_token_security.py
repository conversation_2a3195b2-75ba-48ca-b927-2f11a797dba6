from datetime import timed<PERSON>ta
from uuid import uuid4

from peepsapi.auth.models.email_verification import EmailVerificationRequest
from peepsapi.auth.models.invite import Invite, InviteRequest
from peepsapi.auth.models.recovery import Recovery
from peepsapi.auth.services import token_service
from peepsapi.auth.services.email_verification_service import EmailVerificationService
from peepsapi.auth.services.invite_service import InviteService
from peepsapi.auth.services.recovery_service import RecoveryService
from peepsapi.auth.utils.constants import ChallengeMode
from peepsapi.crud.models.person import Email, Person
from peepsapi.models import now
from peepsapi.models.base import IdentifierType
from tests.conftest import DummyBackgroundTasks, Dummy<PERSON>ontainer


def build_invite_service():
    service = InviteService()
    service.email_service = DummyEmailService()
    return service


class DummyEmailService:
    def __init__(self):
        self.sent = {}

    def send_email(self, recipient, template_name, context, **_):
        self.sent = {"recipient": recipient, "template": template_name}


def test_invite_token_length_and_https():
    service = build_invite_service()
    container = DummyContainer()
    people = DummyContainer()
    creator = uuid4()
    people.create_model(Person(id=creator, remaining_invites=5))
    bg = DummyBackgroundTasks()
    req = InviteRequest(email="<EMAIL>")
    invite = service.create_invite(
        creator_id=creator,
        actor_id=uuid4(),
        auth_source="jwt",
        invite_request=req,
        protocol="http",
        host="localhost",
        background_tasks=bg,
        invite_container=container,
        people_container=people,
    )
    assert len(invite.token) >= 32


def test_verify_invite_token_expired():
    container = DummyContainer()
    token = Invite(
        id=uuid4(),
        token="expiredtoken",
        identifier_value="e",
        identifier_type=IdentifierType.EMAIL,
        person_id=uuid4(),
        invited_by=[uuid4()],
        created_at=now(),
        expires_at=now() - timedelta(days=1),
    )
    container.create_model(token)
    result = token_service.validate_challenge_token(
        token.token, ChallengeMode.REGISTRATION, container
    )
    assert result is None


def test_recovery_token_expired():
    container = DummyContainer()
    rec = Recovery.create(IdentifierType.EMAIL, "<EMAIL>", uuid4())
    rec.expires_at = now() - timedelta(hours=2)
    container.create_model(rec)
    result = token_service.validate_challenge_token(
        rec.token, ChallengeMode.RECOVERY, container
    )
    assert result is None


def test_email_verification_token_length():
    service = EmailVerificationService()
    service.email_service = DummyEmailService()
    bg = DummyBackgroundTasks()
    ver_container = DummyContainer()
    people_container = DummyContainer()
    p = Person(
        id=uuid4(),
        emails=[Email(type="p", address="<EMAIL>", active_since=now())],
        member_since=now(),
    )
    people_container.create_model(p)
    req = EmailVerificationRequest(email="<EMAIL>")
    rec = service.initiate_verification(
        p.id, req, "https", "localhost", bg, ver_container, people_container
    )
    assert len(rec.token) >= 32
