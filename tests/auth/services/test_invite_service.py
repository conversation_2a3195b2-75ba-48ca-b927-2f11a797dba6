from uuid import uuid4

from peepsapi.auth.models.invite import Invite, InviteRequest, InviteStatus
from peepsapi.auth.services.invite_service import InviteService
from peepsapi.crud.models.person import Person
from peepsapi.crud.services import people_services
from peepsapi.models import now
from peepsapi.models.base import IdentifierType
from tests.conftest import Dummy<PERSON><PERSON><PERSON>


def build_test(email="<EMAIL>", expires_in_days=7):
    creator_id = uuid4()
    protocol = "http"
    host = "localhost"
    invite_container = DummyContainer()
    people_container = DummyContainer()
    people_container.create_model(Person(id=creator_id, remaining_invites=5))
    service = InviteService()
    service.invite_container = invite_container
    service.email_service = DummyEmailService()

    def test_get_people_by_identifier(
        identifier_type, identifier_value, people_container
    ):
        matches = []
        for person in people_container.storage.values():
            if identifier_type == IdentifierType.EMAIL:
                if any(
                    email.address == identifier_value
                    for email in getattr(person, "emails", [])
                ):
                    matches.append(person)
            elif identifier_type == IdentifierType.PHONE:
                if any(
                    phone.number == identifier_value
                    for phone in getattr(person, "phone_numbers", [])
                ):
                    matches.append(person)
        return matches

    people_services.people_service.get_people_by_identifier = (
        test_get_people_by_identifier
    )

    class DummyBackgroundTasks:
        def add_task(self, task, *args, **kwargs):
            task(*args, **kwargs)

    background_tasks = DummyBackgroundTasks()
    invite_request = InviteRequest(email=email, expires_in_days=expires_in_days)
    return (
        service,
        invite_request,
        creator_id,
        protocol,
        host,
        background_tasks,
        invite_container,
        people_container,
    )


def test_create_invite():
    (
        service,
        invite_request,
        creator_id,
        protocol,
        host,
        background_tasks,
        invite_container,
        people_container,
    ) = build_test()
    invite = service.create_invite(
        invite_request=invite_request,
        creator_id=creator_id,
        auth_source="jwt",
        actor_id=uuid4(),
        protocol=protocol,
        host=host,
        background_tasks=background_tasks,
        invite_container=invite_container,
        people_container=people_container,
    )
    assert invite.identifier_value == "<EMAIL>"
    assert service.email_service.sent["recipient"] == "<EMAIL>"
    assert invite.resend_count == 0


def test_resend_invite():
    (
        service,
        invite_request,
        creator_id,
        protocol,
        host,
        background_tasks,
        invite_container,
        people_container,
    ) = build_test(email="<EMAIL>", expires_in_days=1)
    invite = service.create_invite(
        invite_request=invite_request,
        creator_id=creator_id,
        actor_id=uuid4(),
        auth_source="jwt",
        protocol=protocol,
        host=host,
        background_tasks=background_tasks,
        invite_container=invite_container,
        people_container=people_container,
    )
    old_expiry = invite.expires_at
    service.resend_invite(
        creator_id=creator_id,
        actor_id=uuid4(),
        auth_source="jwt",
        invite_id=invite.id,
        background_tasks=background_tasks,
        invite_container=invite_container,
    )
    assert invite.resend_count == 1
    assert invite.expires_at > old_expiry


def test_cancel_invite():
    (
        service,
        invite_request,
        creator_id,
        protocol,
        host,
        background_tasks,
        invite_container,
        people_container,
    ) = build_test(email="<EMAIL>", expires_in_days=7)
    invite = service.create_invite(
        invite_request=invite_request,
        creator_id=creator_id,
        actor_id=uuid4(),
        protocol=protocol,
        host=host,
        background_tasks=background_tasks,
        auth_source="jwt",
        invite_container=invite_container,
        people_container=people_container,
    )
    service.cancel_invite(
        invite_id=invite.id,
        auth_source="jwt",
        actor_id=uuid4(),
        creator_id=creator_id,
        invite_container=invite_container,
    )
    assert invite.status == InviteStatus.CANCELLED


def test_quota_and_stats():
    service, _, creator_id, _, _, _, _, people_container = build_test()
    quota_container = DummyContainer()
    invite_container = DummyContainer()
    person_id = creator_id
    actor_id = uuid4()
    service.set_quota(person_id, 3, actor_id, quota_container, people_container)
    quota = service.get_quota(person_id, people_container)
    assert quota == 3
    service.increase_quota(person_id, 2, actor_id, quota_container, people_container)
    quota = service.get_quota(person_id, people_container)
    assert quota == 5
    inv = Invite(
        id=uuid4(),
        token="t",
        identifier_value="<EMAIL>",
        identifier_type=IdentifierType.EMAIL,
        person_id=uuid4(),
        invited_by=[person_id],
        created_at=now(),
        expires_at=now(),
    )
    invite_container.create_model(inv)
    usage = service.get_usage(person_id, invite_container)
    assert usage == 1
    stats = service.get_email_stats(invite_container)
    assert stats["total"] == 1
    assert stats["pending"] == 1


class DummyEmailService:
    def __init__(self):
        self.sent = {"recipient": None, "template": None, "context": None}

    def send_email(self, recipient, template_name, context, **_):
        self.sent["recipient"] = recipient
        self.sent["template"] = template_name
        self.sent["context"] = context
