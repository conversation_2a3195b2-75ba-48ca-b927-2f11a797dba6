from uuid import uuid4

import pytest

from peepsapi.auth.models.recovery import Recovery, RecoveryStatus
from peepsapi.auth.services.recovery_service import RecoveryService
from peepsapi.models.base import IdentifierType
from tests.conftest import DummyBackgroundTasks, DummyContainer


@pytest.fixture
def recover_service(monkeypatch, dummy_email_service, dummy_container):
    service = RecoveryService()
    service.email_service = dummy_email_service
    return service


def execute_bg(task, *args, **kwargs):
    task(*args, **kwargs)


def test_initiate_unknown_email(recover_service, monkeypatch):
    container = DummyContainer()
    people = DummyContainer()
    bg = DummyBackgroundTasks()
    bg.add_task = execute_bg
    monkeypatch.setattr(
        "peepsapi.crud.services.people_services.people_service.get_people_by_identifier",
        lambda *a, **k: [],
    )
    recover_service.initiate_recovery(
        identifier_type=IdentifierType.EMAIL,
        identifier_value="<EMAIL>",
        protocol="https",
        host="localhost",
        background_tasks=bg,
        recovery_container=container,
        people_container=people,
    )
    assert container.storage == {}


def test_full_flow(recover_service, monkeypatch):
    container = DummyContainer()
    people = DummyContainer()
    bg = DummyBackgroundTasks()
    bg.add_task = execute_bg
    from uuid import uuid4

    person = type("P", (), {"id": uuid4()})
    monkeypatch.setattr(
        "peepsapi.crud.services.people_services.people_service.get_people_by_identifier",
        lambda *a, **k: [person],
    )
    recover_service.initiate_recovery(
        identifier_type=IdentifierType.EMAIL,
        identifier_value="<EMAIL>",
        protocol="https",
        host="localhost",
        background_tasks=bg,
        recovery_container=container,
        people_container=people,
    )
    token = next(iter(container.storage.values()))
    assert token.identifier_value == "<EMAIL>"
    assert recover_service.email_service.sent["recipient"] == "<EMAIL>"
    old_exp = token.expires_at
    recover_service.update_or_extend_token(token, container)
    assert token.expires_at > old_exp
    validated = recover_service.validate_and_consume_token(token.token, container)
    assert validated.is_used
    assert validated.status == RecoveryStatus.CONSUMED
    assert container.patched  # ensure DB updated


def test_stats(recover_service):
    container = DummyContainer()
    token = Recovery.create(IdentifierType.EMAIL, "<EMAIL>", uuid4())
    token.status = RecoveryStatus.SENT
    container.create_model(token)
    stats = recover_service.get_email_stats(container)
    assert stats["total"] == 1
    assert stats["sent"] == 1
