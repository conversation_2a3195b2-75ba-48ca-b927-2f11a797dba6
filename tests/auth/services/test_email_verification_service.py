from datetime import timedelta
from uuid import uuid4

import pytest

from peepsapi.auth.models.email_verification import (
    EmailVerification,
    EmailVerificationRequest,
    VerificationStatus,
)
from peepsapi.auth.services.email_verification_service import EmailVerificationService
from peepsapi.crud.models.person import Email, Person
from peepsapi.models import now
from tests.conftest import DummyBackgroundTasks, DummyContainer


@pytest.fixture
def verification_service(dummy_email_service, dummy_container):
    service = EmailVerificationService()
    service.email_service = dummy_email_service
    return service


def execute_bg(task, *args, **kwargs):
    task(*args, **kwargs)


def test_initiate_and_verify(monkeypatch, verification_service):
    bg = DummyBackgroundTasks()
    bg.add_task = execute_bg
    verifications_container = DummyContainer()
    people_container = DummyContainer()
    person_id = uuid4()
    person = Person(
        id=person_id,
        emails=[Email(type="personal", address="<EMAIL>", active_since=now())],
        member_since=now(),
    )
    people_container.create_model(person)
    req = EmailVerificationRequest(email="<EMAIL>")
    record = verification_service.initiate_verification(
        person_id,
        req,
        "https",
        "localhost:8000",
        bg,
        verifications_container,
        people_container,
    )
    assert record.email == "<EMAIL>"
    assert verification_service.email_service.sent["recipient"] == "<EMAIL>"
    verification_service.verify_code_or_link(
        record.token, person_id, verifications_container, people_container
    )
    stored = verifications_container.read_model(
        record.id, record.id, model_class=type(record)
    )
    assert stored.status == VerificationStatus.VERIFIED
    person = people_container.read_model(person_id, person_id, model_class=Person)
    assert person.emails[0].verified


def test_resend(monkeypatch, verification_service):
    bg = DummyBackgroundTasks()
    bg.add_task = execute_bg
    verifications_container = DummyContainer()
    people_container = DummyContainer()
    person = Person(
        id=uuid4(),
        emails=[Email(type="personal", address="<EMAIL>", active_since=now())],
        member_since=now(),
    )
    people_container.create_model(person)
    req = EmailVerificationRequest(email="<EMAIL>")
    record = verification_service.initiate_verification(
        person.id,
        req,
        "https",
        "localhost:8000",
        bg,
        verifications_container,
        people_container,
    )
    old_token = record.token
    verification_service.resend_verification(
        record.person_id,
        record.id,
        "https",
        "localhost:8000",
        bg,
        verifications_container,
    )
    assert record.resend_count == 1
    assert record.token != old_token


def test_expired_token(monkeypatch, verification_service):
    bg = DummyBackgroundTasks()
    bg.add_task = execute_bg
    verifications_container = DummyContainer()
    people_container = DummyContainer()
    person = Person(
        emails=[Email(type="p", address="<EMAIL>", active_since=now())],
        member_since=now(),
    )
    people_container.create_model(person)
    rec = verification_service.initiate_verification(
        person.id,
        EmailVerificationRequest(email="<EMAIL>"),
        "https",
        "localhost:8000",
        bg,
        verifications_container,
        people_container,
    )
    rec.expires_at = now() - timedelta(days=2)
    verifications_container.patch_model(
        rec.id, rec.id, {"expires_at": rec.expires_at}, model_class=type(rec)
    )
    with pytest.raises(Exception):
        verification_service.verify_code_or_link(
            rec.token, person.id, verifications_container, people_container
        )


def test_stats(dummy_email_service):
    service = EmailVerificationService()
    service.email_service = dummy_email_service
    container = DummyContainer()
    rec = EmailVerification(
        id=uuid4(),
        token="t",
        email="<EMAIL>",
        person_id=uuid4(),
        created_at=now(),
        expires_at=now(),
        status=VerificationStatus.SENT,
    )
    container.create_model(rec)
    stats = service.get_email_stats(container)
    assert stats["total"] == 1
    assert stats["sent"] == 1
