from uuid import uuid4

from peepsapi.auth.models.email_verification import (
    EmailVerification,
    VerificationStatus,
)
from peepsapi.models import now


def test_email_verification_defaults():
    rec = EmailVerification(
        id=uuid4(),
        token="t",
        email="<EMAIL>",
        person_id=uuid4(),
        created_at=now(),
        expires_at=now(),
    )
    assert rec.status == VerificationStatus.PENDING
    assert rec.resend_count == 0
    assert not rec.is_used
    assert rec.attempts == 0
