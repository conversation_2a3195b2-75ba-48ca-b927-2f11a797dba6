from peepsapi.auth.models.recovery import Recovery, RecoveryStatus
from peepsapi.models import now
from peepsapi.models.base import IdentifierType


def test_recovery_token_defaults():
    token = Recovery.create(
        identifier_type=IdentifierType.EMAIL, identifier_value="<EMAIL>"
    )
    assert token.status == RecoveryStatus.PENDING
    assert not token.is_used
    assert token.expires_at > now()
