from uuid import uuid4

import pytest

from peepsapi.auth.models.invite import IdentifierType, Invite, InviteStatus
from peepsapi.models import now


def test_invite_defaults():
    invite = Invite(
        id=uuid4(),
        token="t",
        identifier_value="<EMAIL>",
        identifier_type=IdentifierType.EMAIL,
        person_id=None,
        invited_by=[uuid4()],
        created_at=now(),
        expires_at=now(),
        status=InviteStatus.PENDING,
        resend_count=0,
        is_used=False,
    )
    assert invite.status == InviteStatus.PENDING
    assert invite.resend_count == 0
    assert not invite.is_used
