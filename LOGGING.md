# Custom Logging System Documentation

This guide provides an overview of the custom logging system in the PeepsAPI project, including configuration, usage, and best practices for contextual and colored logging.

## Table of Contents

1. [Overview](#1-overview)
2. [Configuration](#2-configuration)
   - [Log Levels](#log-levels)
   - [Log Formats](#log-formats)
   - [Environment Variables](#environment-variables)
   - [Handlers & Loggers](#handlers--loggers)
   - [Colored Output](#colored-output)
3. [Usage](#3-usage)
   - [ContextualLogger](#contextuallogger)
   - [Adding Context and Extra Fields](#adding-context-and-extra-fields)
   - [Traceback Logging](#traceback-logging)
   - [Emoji Prefixes](#emoji-prefixes)
4. [Local Development Setup](#4-local-development-setup)
5. [Server Logging](#5-server-logging)
6. [Best Practices](#6-best-practices)
7. [Troubleshooting](#7-troubleshooting)
8. [Reserved LogRecord Attributes](#8-reserved-logrecord-attributes)

## 1. Overview

The PeepsAPI custom logging system is designed for flexibility, clarity, and rich context. It supports multiple log formats, colored output, and automatic inclusion of request/user context. Logging is configured via JSON files and a helper script, and can be controlled at runtime via command-line arguments or environment variables.

## 2. Configuration

### Log Levels

- **DEBUG**: Detailed information for debugging (`DBG` if short log enabled)
- **INFO**: General operational events (`INF` if short log enabled)
- **WARNING**: Unexpected or concerning events (`WRN` if short log enabled)
- **ERROR**: Errors that prevent some functionality (`ERR` if short log enabled)
- **CRITICAL**: Severe errors causing program failure (`CRT` if short log enabled)

Log levels can be set globally or per-logger (e.g., `peepsapi`, `uvicorn`, Azure SDK).

### Log Level Shortening

You can enable short log level names by setting `LOG_SHORT_LEVELNAME=true` in your environment.

**Examples:**

With short log level names enabled:

```text
2025-07-16 14:23:01 | DBG | peepsapi.auth | 🔧 Debugging details
2025-07-16 14:23:01 | INF | peepsapi.auth | ✅ User authenticated
2025-07-16 14:23:02 | WRN | peepsapi.auth | ⚠️ Rate limit threshold reached
2025-07-16 14:23:02 | ERR | peepsapi.auth | ❌ Payment failed
2025-07-16 14:23:02 | CRT | peepsapi.auth | ❌ Critical failure
```

### Log Formats

**Minimal**: Level, logger name, message

```text
INFO | peepsapi.auth | ✅ User authenticated
ERROR | peepsapi.auth | ❌ Payment failed
```

**Standard**: Timestamp, level, logger name, message

```text
2025-07-16 14:23:01 | INFO | peepsapi.auth | ✅ User authenticated
2025-07-16 14:23:02 | ERROR | peepsapi.auth | ❌ Payment failed
```

**Detailed**: Timestamp, level, logger name, line number, message

```text
2025-07-16 14:23:01 | INFO | peepsapi.auth:42 | ✅ User authenticated
2025-07-16 14:23:02 | ERROR | peepsapi.auth:87 | ❌ Payment failed
```

**Link**: Timestamp, Pathname, line number, message

```text
2025-07-16 14:23:01 | INFO | '/Users/<USER>/projects/peepsAPI/peepsapi/auth.py':42 | ✅ User authenticated
2025-07-16 14:23:02 | ERROR | '/Users/<USER>/projects/peepsAPI/peepsapi/auth.py':87 | ❌ Payment failed
```

**Context**: Any format + context info (when `LOG_CONTEXT=true`)

```text
2025-07-16 14:23:01 | INFO | peepsapi.auth:42 | ✅ User authenticated | CONTEXT: {'path': '/login', 'method': 'POST', 'client_ip': '127.0.0.1'}
2025-07-16 14:23:02 | ERROR | peepsapi.auth:87 | ❌ Payment failed | CONTEXT: {'person_id': '123', 'payment_id': 'abc123'}
```

Choose format via environment variables or config file.

### Environment Variables

The logging system is controlled by environment variables that can be set in `.env` or `local-override.env`:

| Variable | Description | Values | Default |
|----------|-------------|--------|---------|
| `LOG_LEVEL` | Application log level | DEBUG, INFO, WARNING, ERROR, CRITICAL | INFO |
| `LOG_LEVEL_ROOT` | Root logger level | DEBUG, INFO, WARNING, ERROR, CRITICAL | INFO |
| `LOG_LEVEL_AZURE` | Azure SDK log level | DEBUG, INFO, WARNING, ERROR, CRITICAL | WARNING |
| `LOG_FORMAT` | Log format style | Minimal, Standard, Detailed, Link | Standard |
| `LOG_CONTEXT` | Enable context information | true, false | false |
| `LOG_COLORED` | Enable colored output | true, false | false |
| `LOG_SHORT_LEVELNAME` | Use short level names | true, false | false |
| `LOG_FORMAT_STRING` | Custom format string | Any valid format | (uses LOG_FORMAT) |
| `LOG_FORMAT_TIMESTAMP` | Custom timestamp format | Any valid strftime format | %Y-%m-%d %H:%M:%S |

**Examples:**

```bash
# Enable debug logging with colors and context
LOG_LEVEL=DEBUG
LOG_COLORED=true
LOG_CONTEXT=true
LOG_SHORT_LEVELNAME=true

# Custom format with time only
LOG_FORMAT_STRING=%(asctime)s | %(name)s:%(lineno)d - %(message)s
LOG_FORMAT_TIMESTAMP=%H:%M:%S
```

### Handlers & Loggers

- Handlers route logs to the console with the chosen format.
- Loggers are defined for each module (e.g., `peepsapi.auth`, `uvicorn`, `azure.core.pipeline.policies.http_logging_policy`).
- The root logger controls fallback behavior.

### Colored Output

- Enable colored log levels and context by setting `LOG_COLORED=true` in your environment.
- Colors improve readability in the console. Short log level names are also colored if enabled.

## 3. Usage

### ContextualLogger

Use the `ContextualLogger` to automatically include request and user context in logs. Context is only included when `LOG_CONTEXT=true`:

```python
from peepsapi.utils.logging import get_logger
logger = get_logger(__name__)

# Basic logging
logger.info("✅ User authenticated")

# With person ID
logger.info("✅ Profile updated", person_id=person.id)

# With request context and extra fields
logger.error(
    "❌ Payment failed",
    request=request,
    person_id=person.id,
    extra={"payment_id": payment.id, "amount": 100.00}
)
```

#### Context Fields (auto-included when `request` object passed, `LOG_CONTEXT=true` and is available):

- `path`: The URL path of the request
- `method`: The HTTP method (GET, POST, etc.)
- `client_ip`: The IP address of the client
- `user_agent`: The client's user agent string
- `person_id`: The ID of the authenticated person (from request state or parameter)
- `auth_source`: The source of authentication (jwt or azure_ad)
- `user_mail`, `user_name`, `override_person_id`: For Azure AD Auth

### Adding Context and Extra Fields

The ContextualLogger accepts both contextual information and additional fields:

- **`request`**: FastAPI request object (automatically extracts context)
- **`person_id`**: Person ID (overrides request state if provided)
- **`extra`**: Dictionary of additional fields to include in context
- **`**kwargs`**: Additional logging parameters (like `exc_info=True`)

**Important**: Fields passed in `extra` become part of the context and should not use reserved LogRecord attribute names (see below).

### Traceback Logging

For error logging with full tracebacks, use the `exc_info=True` parameter:

```python
try:
    result = process_payment(payment_id)
except Exception as e:
    logger.error(
        f"❌ Error processing payment {payment_id}: {e}",
        extra={
            "person_id": person_id,
            "payment_id": payment_id,
            "error": str(e),
        },
        exc_info=True,  # Includes full traceback
    )
```

**When to use `exc_info=True`:**
- Critical errors that need full debugging information
- Unexpected exceptions that shouldn't occur in normal operation
- When you need to see the complete call stack

**When NOT to use `exc_info=True`:**
- Expected validation errors
- User input errors
- Business logic errors that are handled gracefully

### Emoji Prefixes

To improve log readability and make it easier to scan logs for specific types of information, we use a standardized set of emoji prefixes for all log messages.

#### Emoji Prefix Table

| Emoji | Meaning / When to Use                    |
| ----- | ---------------------------------------- |
| ✅    | Success / operation completed            |
| ❌    | Error / failure                          |
| ⚠️    | Warning / degraded behavior              |
| 🛠️    | Setup / configuration steps              |
| 🔧    | Debug logs / detailed internals          |
| 🎯    | Starting a specific task / step          |
| 📦    | Package or dependency loading            |
| 🚀    | App or server start                      |
| 🧪    | Validation / test / checks               |
| 📝    | General info / notes                     |
| 🔐    | Authentication / passkey / security logs |
| ⏳    | Waiting / in-progress status             |
| 🧠    | AI features / GPT / reasoning logs       |
| 🧹    | Cleanup / teardown                       |
| 📤    | Sending data / request outbound          |
| 📥    | Receiving data / response inbound        |

#### Usage Guidelines

1. **Always include an emoji prefix** at the beginning of each log message
2. **Choose the most appropriate emoji** based on the message content and context
3. **Be consistent** with emoji usage across similar log messages
4. **Avoid duplicating emojis** if one already exists in the message
5. **Use only one emoji** per log message (the most relevant one)
6. **For structured logs** (JSON format), add a `tag` or `emoji` field instead

#### Examples

```python
# Success logs
logger.info("✅ User successfully authenticated")
logger.info("✅ Payment processed successfully")

# Error logs
logger.error("❌ Failed to connect to database")
logger.error("❌ Payment processing failed: Invalid card")

# Warning logs
logger.warning("⚠️ Rate limit threshold reached")
logger.warning("⚠️ Database connection pool running low")

# Debug logs
logger.debug("🔧 Processing request parameters")
logger.debug("🔧 Query execution plan: SELECT * FROM users")

# Starting tasks
logger.info("🎯 Starting data migration process")
logger.info("🎯 Processing user input: 'Hello world'")

# Authentication logs
logger.info("🔐 New passkey registered for user")
logger.info("🔐 Token refreshed for session")

# AI-related logs
logger.info("🧠 Parsing user intent with GPT")
logger.debug("🧠 Raw GPT response: {...}")

# Data transfer logs
logger.info("📤 Sending request to payment gateway")
logger.info("📥 Received response from external API")
```

## 4. Local Development Setup

### Using local-override.env

For local development, create or modify `local-override.env` to override default logging settings:

```bash
# local-override.env
LOG_LEVEL=DEBUG
LOG_LEVEL_AZURE=WARNING
LOG_FORMAT=Standard
LOG_CONTEXT=true
LOG_COLORED=true
LOG_SHORT_LEVELNAME=true
LOG_FORMAT_STRING=%(asctime)s | %(name)s:%(lineno)d - %(message)s
LOG_FORMAT_TIMESTAMP=%H:%M:%S
```

### Starting the Server

Use `make start` to run the server with your logging configuration:

```bash
# Uses settings from .env and local-override.env
make start
```

The server automatically watches for changes to `.env` and `local-override.env` files and restarts when they change.

### Environment Variable Override

You can also override settings temporarily:

```bash
# Override log level for this session
LOG_LEVEL=DEBUG make start

# Enable colors and context
LOG_COLORED=true LOG_CONTEXT=true make start
```

## 5. Server Logging

The Docker image includes a basic logging configuration. When the container starts, Uvicorn loads `docker-log-config.json`, which sets the default log level to `INFO` for all application loggers. You can override this level by providing the `LOG_LEVEL` environment variable when deploying the container:

```bash
az containerapp update \
  --name <app-name> \
  --resource-group <resource-group> \
  --set-env-vars LOG_LEVEL=DEBUG

# Or using the Makefile wrapper
LOG_LEVEL=DEBUG make deploy-service ENV=dev
```

This allows `logger.info` and `logger.debug` statements to appear in the Azure Container App logs.

## 6. Best Practices

### Using Proper Log Levels

Choose the appropriate log level based on the importance and purpose of the message:

- **DEBUG**: Detailed information, typically useful only for diagnosing problems

  ```python
  logger.debug(f"🔧 Processing request with parameters: {params}")
  ```

- **INFO**: Confirmation that things are working as expected

  ```python
  logger.info(f"✅ User {user_id} successfully authenticated")
  ```

- **WARNING**: An indication that something unexpected happened, or may happen in the near future

  ```python
  logger.warning(f"⚠️ Rate limit threshold reached for user {user_id}")
  ```

- **ERROR**: Due to a more serious problem, the software has not been able to perform some function

  ```python
  logger.error(f"❌ Failed to process payment: {str(error)}")
  ```

- **CRITICAL**: A serious error, indicating that the program itself may be unable to continue running
  ```python
  logger.critical(f"❌ Database connection failed: {str(error)}")
  ```

### Adding Context Information

Always include relevant context information in your log messages:

```python
# Good: Includes context information and emoji
logger.info(
    "✅ Connection status updated",
    extra={
        "connection_id": connection_id,
        "old_status": old_status,
        "new_status": new_status,
    }
)

# Bad: Missing context and emoji
logger.info("Connection status updated")
```

When logging errors, include details about the error and use `exc_info=True` for unexpected errors:

```python
try:
    # Some operation
    result = process_payment(payment_id)
except ValidationError as e:
    # Expected error - no traceback needed
    logger.warning(
        f"⚠️ Payment validation failed: {str(e)}",
        extra={
            "payment_id": payment_id,
            "validation_errors": e.errors(),
        }
    )
except Exception as e:
    # Unexpected error - include full traceback
    logger.error(
        f"❌ Payment processing failed: {str(e)}",
        extra={
            "payment_id": payment_id,
            "error_type": type(e).__name__,
        },
        exc_info=True,
    )
```

### Avoiding Print Statements

Always use the logger instead of print statements:

```python
# Good: Uses logger with emoji
logger.debug(f"🔐 Token generated: {token[:5]}...")

# Bad: Uses print without emoji
print(f"Token generated: {token[:5]}...")
```

**Benefits of using the logger over print statements:**

1. Respects log level configuration (only shows logs at or above the configured level)
2. Includes contextual information (timestamps, module names, etc.)
3. Can be filtered and redirected to different outputs
4. Consistent formatting across the application

### Error Logging

When logging exceptions, follow these best practices:

1. **Log at the appropriate level** (usually ERROR or CRITICAL)
2. **Include the exception message** using `str(e)`
3. **Add context about where the error occurred**
4. **Include relevant IDs and parameters** (but be careful with sensitive data)
5. **Consider including the exception type** using `type(e).__name__`
6. **For critical errors, include the traceback** using `traceback.format_exc()`

Example:

```python
try:
    # Some operation that might fail
    result = process_data(data_id)
except Exception as e:
    logger.error(
        f"❌ Failed to process data: {str(e)}",
        extra={
            "data_id": data_id,
            "error_type": type(e).__name__,
            "traceback": traceback.format_exc() if is_critical else None,
        }
    )
```

## 7. Troubleshooting

### Troubleshooting Authentication Issues

When debugging authentication issues, pay attention to:

1. Look for log messages from the `peepsapi.auth` module
2. Check for any error messages related to token validation
3. Look for messages containing "JWT token valid" or "Azure AD token valid"
4. Check the request state in middleware logs

### Common Logging Problems

- **Missing logs**: Check your `LOG_LEVEL` setting - use DEBUG for detailed logs
- **Too many logs**: Use a higher log level (e.g., WARNING or ERROR) to reduce verbosity
- **Context not showing**: Ensure `LOG_CONTEXT=true` is set in your environment
- **Colors not working**: Set `LOG_COLORED=true` in your environment
- **Azure SDK logs**: These are controlled by `LOG_LEVEL_AZURE` (default: WARNING)
- **"Attempt to overwrite 'name' in LogRecord" error**: Avoid using 'name' as a key in the `extra` dictionary when logging, as it conflicts with a built-in LogRecord attribute. Use a more specific key like 'user_name', 'search_name', etc. instead.
- **"Attempt to overwrite 'module' in LogRecord" error**: Avoid using 'module' as a key in the `extra` dictionary when logging, as it conflicts with a built-in LogRecord attribute. Use a more specific key like 'func_module', 'source_module', etc. instead.

## 8. Reserved LogRecord Attributes

Python's LogRecord class has several reserved attribute names that should not be used in the `extra` dictionary:

- `name` - Use `user_name`, `search_name`, etc. instead
- `level` - Use `log_level`, `severity_level`, etc. instead
- `pathname` - Use `file_path`, `source_path`, etc. instead
- `lineno` - Use `line_number`, `source_line`, etc. instead
- `msg` - Use `message`, `log_message`, etc. instead
- `args` - Use `arguments`, `parameters`, etc. instead
- `exc_info` - Use `exception_info`, `error_details`, etc. instead
- `func` - Use `function_name`, `method_name`, etc. instead
- `sinfo` - Use `stack_info`, `trace_info`, etc. instead
- `module` - Use `func_module`, `source_module`, etc. instead

When logging with the `extra` parameter, always use unique keys that don't conflict with these built-in attributes.

**Example of correct usage:**

```python
# Good - uses non-conflicting keys
logger.info(
    "✅ User operation completed",
    extra={
        "user_name": "john_doe",        # not "name"
        "func_module": "auth.login",    # not "module"
        "line_number": 42,              # not "lineno"
        "operation_args": ["param1"],   # not "args"
    }
)
```

For more details, see the code in `peepsapi/utils/logging.py` and `template-log-config.json`.
